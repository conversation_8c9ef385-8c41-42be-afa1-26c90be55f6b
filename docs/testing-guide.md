# 测试指南

本文档介绍多维笔记应用的测试策略、测试工具和最佳实践。

## 测试技术栈

### 测试框架
- **Vitest**: 现代化的测试框架，与Vite深度集成
- **@testing-library/react**: React组件测试工具
- **@testing-library/jest-dom**: DOM断言扩展
- **@testing-library/user-event**: 用户交互模拟

### 测试类型
1. **单元测试**: 测试独立的函数、类和组件
2. **集成测试**: 测试组件间的交互
3. **端到端测试**: 测试完整的用户流程（计划中）

## 项目结构

```
src/
├── components/
│   └── __tests__/          # 组件测试
├── services/
│   └── __tests__/          # 服务层测试
├── hooks/
│   └── __tests__/          # Hook测试
├── utils/
│   └── __tests__/          # 工具函数测试
└── setupTests.ts           # 测试配置
```

## 运行测试

### 基本命令
```bash
# 运行所有测试
npm run test

# 监听模式运行测试
npm run test -- --watch

# 运行特定测试文件
npm run test -- documentManager.test.ts

# 运行测试并生成覆盖率报告
npm run test -- --coverage
```

### 测试配置
测试配置位于 `vitest.config.ts` 文件中：

```typescript
export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/setupTests.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
    },
  },
})
```

## 测试最佳实践

### 1. 测试命名规范
- 使用描述性的测试名称
- 遵循 "应该...当...时" 的格式
- 使用中文描述测试场景

```typescript
describe('DocumentManager', () => {
  it('应该成功创建文档', async () => {
    // 测试代码
  })

  it('应该在标题为空时生成默认标题', async () => {
    // 测试代码
  })
})
```

### 2. 测试结构
使用 AAA 模式（Arrange-Act-Assert）：

```typescript
it('应该更新文档标题', async () => {
  // Arrange - 准备测试数据
  const document = await createTestDocument()
  
  // Act - 执行操作
  const result = await documentManager.updateDocument(document.id, {
    title: '新标题'
  })
  
  // Assert - 验证结果
  expect(result?.title).toBe('新标题')
})
```

### 3. Mock策略
- 对外部依赖进行Mock
- 使用vi.mock()模拟模块
- 为异步操作提供可控的Mock

```typescript
vi.mock('@services/database/documentDAO', () => ({
  documentDAO: {
    create: vi.fn(),
    getById: vi.fn(),
    // ...其他方法
  }
}))
```

### 4. 组件测试
- 测试用户交互而非实现细节
- 使用语义化查询
- 模拟用户行为

```typescript
it('应该在点击按钮时触发搜索', async () => {
  const user = userEvent.setup()
  const mockOnSearch = vi.fn()
  
  render(<SearchInput onSearch={mockOnSearch} />)
  
  const button = screen.getByRole('button', { name: /搜索/i })
  await user.click(button)
  
  expect(mockOnSearch).toHaveBeenCalled()
})
```

## 测试覆盖率

### 目标覆盖率
- **语句覆盖率**: ≥ 80%
- **分支覆盖率**: ≥ 75%
- **函数覆盖率**: ≥ 85%
- **行覆盖率**: ≥ 80%

### 查看覆盖率报告
```bash
npm run test -- --coverage
```

覆盖率报告将生成在 `coverage/` 目录中，包含：
- 文本报告（终端输出）
- HTML报告（coverage/index.html）
- JSON报告（coverage/coverage-final.json）

## 已实现的测试

### 服务层测试
1. **DocumentManager测试** (`src/services/document/__tests__/documentManager.test.ts`)
   - 文档创建、读取、更新、删除
   - 搜索功能
   - 统计信息
   - 文档复制

2. **LinkService测试** (`src/services/link/__tests__/linkService.test.ts`)
   - 链接创建和管理
   - 反向链接查询
   - 孤立文档检测
   - 链接统计

### 组件测试
1. **SearchInput测试** (`src/components/search/__tests__/SearchInput.test.tsx`)
   - 搜索输入和触发
   - 搜索历史管理
   - 搜索建议
   - 用户交互

## 测试数据管理

### 测试数据创建
为测试创建辅助函数：

```typescript
// 创建测试文档
const createTestDocument = (overrides = {}) => ({
  title: '测试文档',
  type: DocumentType.TEXT,
  content: { markdown: '测试内容' },
  tags: ['测试'],
  ...overrides
})

// 创建测试链接
const createTestLink = (overrides = {}) => ({
  sourceId: 'doc1',
  targetId: 'doc2',
  type: LinkType.REFERENCE,
  ...overrides
})
```

### 测试数据清理
在每个测试后清理数据：

```typescript
beforeEach(() => {
  mockDB.clear()
  vi.clearAllMocks()
})

afterEach(() => {
  mockDB.clear()
})
```

## 持续集成

### GitHub Actions配置
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test -- --coverage
      - run: npm run build
```

## 调试测试

### 调试技巧
1. 使用 `console.log` 输出调试信息
2. 使用 `screen.debug()` 查看DOM结构
3. 使用 `--reporter=verbose` 获取详细输出
4. 使用 `--no-coverage` 加快测试运行

### 常见问题
1. **异步测试超时**: 使用 `waitFor` 等待异步操作
2. **Mock不生效**: 确保Mock在导入模块之前定义
3. **DOM查询失败**: 使用正确的查询方法和选择器

## 未来计划

### 待实现的测试
1. **Hook测试**: useTheme, useDocument等
2. **工具函数测试**: 格式化、验证等工具
3. **端到端测试**: 使用Playwright或Cypress
4. **性能测试**: 大数据量下的性能表现
5. **可访问性测试**: 使用@testing-library/jest-axe

### 测试改进
1. 增加视觉回归测试
2. 实现测试数据工厂
3. 添加API集成测试
4. 完善错误场景测试

## 贡献指南

### 编写新测试
1. 为新功能编写对应测试
2. 确保测试覆盖主要场景
3. 包含边界情况和错误处理
4. 遵循项目的测试规范

### 测试审查清单
- [ ] 测试名称清晰描述测试场景
- [ ] 使用适当的断言方法
- [ ] 包含正常和异常情况
- [ ] Mock外部依赖
- [ ] 清理测试数据
- [ ] 测试通过且稳定

通过完善的测试体系，我们确保应用的质量和稳定性，为用户提供可靠的多维笔记体验。
