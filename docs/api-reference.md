# API 参考文档

本文档详细介绍多维笔记应用的核心API接口和服务。

## 目录
- [文档管理API](#文档管理api)
- [链接管理API](#链接管理api)
- [搜索API](#搜索api)
- [数据库API](#数据库api)
- [跨格式转换API](#跨格式转换api)

## 文档管理API

### DocumentManager

文档管理器提供文档的CRUD操作和高级功能。

#### 创建文档
```typescript
createDocument(data: CreateDocumentData): Promise<BaseDocument>
```

**参数:**
- `data.title`: 文档标题（可选，默认为"未命名文档"）
- `data.type`: 文档类型（TEXT | WHITEBOARD | MINDMAP | KANBAN）
- `data.content`: 文档内容
- `data.tags`: 标签数组（可选）

**返回值:**
- 创建的文档对象，包含生成的ID和时间戳

**示例:**
```typescript
const document = await documentManager.createDocument({
  title: '我的笔记',
  type: DocumentType.TEXT,
  content: {
    markdown: '# 标题\n这是内容'
  },
  tags: ['学习', '笔记']
})
```

#### 获取文档
```typescript
getDocumentById(id: string): Promise<BaseDocument | null>
```

**参数:**
- `id`: 文档ID

**返回值:**
- 文档对象或null（如果不存在）

#### 更新文档
```typescript
updateDocument(id: string, updates: Partial<BaseDocument>): Promise<BaseDocument | null>
```

**参数:**
- `id`: 文档ID
- `updates`: 要更新的字段

**返回值:**
- 更新后的文档对象或null

#### 删除文档
```typescript
deleteDocument(id: string): Promise<boolean>
```

**参数:**
- `id`: 文档ID

**返回值:**
- 删除成功返回true，否则返回false

#### 搜索文档
```typescript
searchDocuments(query: string, options?: SearchOptions): Promise<BaseDocument[]>
```

**参数:**
- `query`: 搜索关键词
- `options`: 搜索选项
  - `types`: 文档类型筛选
  - `tags`: 标签筛选
  - `limit`: 结果数量限制
  - `offset`: 分页偏移

**返回值:**
- 匹配的文档数组

#### 获取统计信息
```typescript
getDocumentStats(): Promise<DocumentStats>
```

**返回值:**
- 文档统计信息对象
  - `totalDocuments`: 总文档数
  - `documentsByType`: 按类型分组的文档数
  - `totalTags`: 总标签数
  - `tagUsage`: 标签使用统计

## 链接管理API

### LinkService

链接服务提供文档间关联关系的管理。

#### 创建链接
```typescript
createLink(data: CreateLinkData): Promise<DocumentLink>
```

**参数:**
- `data.sourceId`: 源文档ID
- `data.targetId`: 目标文档ID
- `data.type`: 链接类型（REFERENCE | EMBED | RELATED | PARENT_CHILD）
- `data.label`: 链接标签（可选）

**返回值:**
- 创建的链接对象

**示例:**
```typescript
const link = await linkService.createLink({
  sourceId: 'doc1',
  targetId: 'doc2',
  type: LinkType.REFERENCE,
  label: '参考资料'
})
```

#### 获取反向链接
```typescript
getBacklinks(documentId: string, type?: LinkType): Promise<DocumentLink[]>
```

**参数:**
- `documentId`: 目标文档ID
- `type`: 链接类型筛选（可选）

**返回值:**
- 指向该文档的所有链接

#### 查找孤立文档
```typescript
findOrphanDocuments(allDocumentIds: string[]): Promise<string[]>
```

**参数:**
- `allDocumentIds`: 所有文档ID数组

**返回值:**
- 没有任何链接的文档ID数组

#### 获取链接统计
```typescript
getLinkStats(): Promise<LinkStats>
```

**返回值:**
- 链接统计信息
  - `totalLinks`: 总链接数
  - `linksByType`: 按类型分组的链接数
  - `mostLinkedDocuments`: 最多链接的文档
  - `averageLinksPerDocument`: 平均每文档链接数

## 搜索API

### 搜索选项
```typescript
interface SearchOptions {
  types?: DocumentType[]        // 文档类型筛选
  tags?: string[]              // 标签筛选
  dateRange?: {                // 日期范围筛选
    start: Date
    end: Date
  }
  limit?: number               // 结果数量限制
  offset?: number              // 分页偏移
  sortBy?: 'relevance' | 'date' | 'title'  // 排序方式
  sortOrder?: 'asc' | 'desc'   // 排序顺序
}
```

### 高级搜索
```typescript
advancedSearch(filters: AdvancedSearchFilters): Promise<SearchResult[]>
```

**参数:**
- `filters.keywords`: 关键词
- `filters.types`: 文档类型
- `filters.tags`: 标签
- `filters.author`: 作者
- `filters.dateRange`: 日期范围

**返回值:**
- 搜索结果数组，包含匹配度和高亮信息

## 数据库API

### DatabaseManager

数据库管理器提供底层数据存储操作。

#### 初始化数据库
```typescript
initializeDatabase(): Promise<void>
```

初始化IndexedDB数据库和对象存储。

#### 备份数据
```typescript
exportData(): Promise<BackupData>
```

**返回值:**
- 包含所有数据的备份对象

#### 恢复数据
```typescript
importData(backupData: BackupData): Promise<void>
```

**参数:**
- `backupData`: 备份数据对象

#### 清空数据
```typescript
clearAllData(): Promise<void>
```

清空所有数据库数据（危险操作）。

## 跨格式转换API

### CrossFormatLinkManager

跨格式关联管理器提供不同文档格式间的转换和关联。

#### 转换内容
```typescript
convertContent(
  content: any,
  sourceType: DocumentType,
  targetType: DocumentType,
  options?: ConversionOptions
): Promise<any>
```

**参数:**
- `content`: 源内容
- `sourceType`: 源文档类型
- `targetType`: 目标文档类型
- `options`: 转换选项

**返回值:**
- 转换后的内容

#### 创建跨格式引用
```typescript
createReference(data: CreateReferenceData): Promise<CrossFormatReference>
```

**参数:**
- `data.sourceDocumentId`: 源文档ID
- `data.targetDocumentId`: 目标文档ID
- `data.referenceType`: 引用类型
- `data.sourceSelection`: 源选择区域
- `data.targetSelection`: 目标选择区域

## 错误处理

### 错误类型
```typescript
enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  DUPLICATE = 'DUPLICATE',
  DATABASE_ERROR = 'DATABASE_ERROR',
  CONVERSION_ERROR = 'CONVERSION_ERROR'
}
```

### 错误对象
```typescript
interface APIError {
  type: ErrorType
  message: string
  details?: any
  timestamp: Date
}
```

### 错误处理示例
```typescript
try {
  const document = await documentManager.createDocument(data)
} catch (error) {
  if (error.type === ErrorType.VALIDATION_ERROR) {
    console.error('验证失败:', error.message)
  } else if (error.type === ErrorType.DATABASE_ERROR) {
    console.error('数据库错误:', error.message)
  }
}
```

## 事件系统

### 事件类型
```typescript
enum EventType {
  DOCUMENT_CREATED = 'document:created',
  DOCUMENT_UPDATED = 'document:updated',
  DOCUMENT_DELETED = 'document:deleted',
  LINK_CREATED = 'link:created',
  LINK_DELETED = 'link:deleted'
}
```

### 事件监听
```typescript
// 监听文档创建事件
documentManager.on(EventType.DOCUMENT_CREATED, (document) => {
  console.log('文档已创建:', document.title)
})

// 监听链接创建事件
linkService.on(EventType.LINK_CREATED, (link) => {
  console.log('链接已创建:', link.id)
})
```

## 类型定义

### 基础文档类型
```typescript
interface BaseDocument {
  id: string
  title: string
  type: DocumentType
  content: DocumentContent
  tags: string[]
  metadata: DocumentMetadata
  createdAt: Date
  updatedAt: Date
}
```

### 文档内容类型
```typescript
type DocumentContent = 
  | TextContent 
  | WhiteboardContent 
  | MindmapContent 
  | KanbanContent

interface TextContent {
  markdown: string
  html?: string
}

interface WhiteboardContent {
  canvas: CanvasData
  objects: FabricObject[]
  viewport: ViewportData
  layers: LayerData[]
}
```

### 链接类型
```typescript
interface DocumentLink {
  id: string
  sourceId: string
  targetId: string
  type: LinkType
  label?: string
  metadata?: Record<string, any>
  createdAt: Date
  updatedAt: Date
}
```

## 使用示例

### 完整的文档操作流程
```typescript
// 1. 创建文档
const document = await documentManager.createDocument({
  title: '项目计划',
  type: DocumentType.TEXT,
  content: {
    markdown: '# 项目计划\n\n## 目标\n...'
  },
  tags: ['项目', '计划']
})

// 2. 创建另一个文档
const relatedDoc = await documentManager.createDocument({
  title: '相关资料',
  type: DocumentType.TEXT,
  content: {
    markdown: '# 相关资料\n...'
  },
  tags: ['资料']
})

// 3. 创建链接
const link = await linkService.createLink({
  sourceId: document.id,
  targetId: relatedDoc.id,
  type: LinkType.REFERENCE,
  label: '参考资料'
})

// 4. 搜索文档
const searchResults = await documentManager.searchDocuments('项目', {
  types: [DocumentType.TEXT],
  limit: 10
})

// 5. 获取反向链接
const backlinks = await linkService.getBacklinks(relatedDoc.id)

console.log('找到', backlinks.length, '个反向链接')
```

这个API参考文档提供了应用核心功能的详细接口说明，帮助开发者理解和使用各种服务。
