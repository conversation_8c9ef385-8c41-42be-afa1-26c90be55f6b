/**
 * 白板服务
 * 提供白板文档的创建、保存、加载等功能
 */

import { documentDAO } from './database/documentDAO'
import { generateUUID } from '@utils/index'
import {
  WhiteboardDocument,
  WhiteboardContent,
  DocumentType,
  BaseDocument
} from '@/types'

/**
 * 白板服务类
 */
export class WhiteboardService {
  /**
   * 创建新的白板文档
   */
  async createWhiteboard(title: string = '新建白板'): Promise<WhiteboardDocument> {
    const now = new Date()

    const whiteboardContent: WhiteboardContent = {
      canvas: {
        width: 1920,
        height: 1080,
        backgroundColor: '#ffffff',
        zoom: 1
      },
      objects: [],
      viewport: {
        x: 0,
        y: 0,
        zoom: 1
      },
      layers: [{
        id: generateUUID(),
        name: '默认图层',
        visible: true,
        locked: false,
        opacity: 1,
        order: 0
      }]
    }

    const whiteboardDoc: WhiteboardDocument = {
      id: generateUUID(),
      type: DocumentType.WHITEBOARD,
      title,
      content: whiteboardContent,
      createdAt: now,
      updatedAt: now,
      tags: [],
      metadata: {
        version: 1,
        size: 0,
        checksum: '',
        canvasWidth: 1920,
        canvasHeight: 1080,
        elementCount: 0
      }
    }

    // 转换为基础文档格式保存到数据库
    const baseDoc: BaseDocument = {
      id: whiteboardDoc.id,
      type: DocumentType.WHITEBOARD,
      title: whiteboardDoc.title,
      content: whiteboardDoc.content,
      createdAt: whiteboardDoc.createdAt,
      updatedAt: whiteboardDoc.updatedAt,
      tags: whiteboardDoc.tags,
      metadata: whiteboardDoc.metadata,
      links: []
    }

    await documentDAO.create(baseDoc)
    console.log('白板文档创建成功:', whiteboardDoc.title)
    
    return whiteboardDoc
  }

  /**
   * 保存白板文档
   */
  async saveWhiteboard(whiteboard: WhiteboardDocument): Promise<void> {
    const updatedWhiteboard = {
      ...whiteboard,
      updatedAt: new Date(),
      metadata: {
        ...whiteboard.metadata,
        elementCount: whiteboard.content.objects.length
      }
    }

    const baseDoc: BaseDocument = {
      id: updatedWhiteboard.id,
      type: DocumentType.WHITEBOARD,
      title: updatedWhiteboard.title,
      content: updatedWhiteboard.content,
      createdAt: updatedWhiteboard.createdAt,
      updatedAt: updatedWhiteboard.updatedAt,
      tags: updatedWhiteboard.tags,
      metadata: updatedWhiteboard.metadata,
      links: updatedWhiteboard.links || []
    }

    await documentDAO.update(baseDoc.id, baseDoc)
    console.log('白板文档保存成功:', updatedWhiteboard.title)
  }

  /**
   * 加载白板文档
   */
  async loadWhiteboard(id: string): Promise<WhiteboardDocument | null> {
    try {
      const baseDoc = await documentDAO.getById(id)
      
      if (!baseDoc || baseDoc.type !== DocumentType.WHITEBOARD) {
        console.warn('白板文档不存在或类型不匹配:', id)
        return null
      }

      const whiteboardDoc: WhiteboardDocument = {
        id: baseDoc.id,
        type: DocumentType.WHITEBOARD,
        title: baseDoc.title,
        content: baseDoc.content as WhiteboardContent,
        createdAt: baseDoc.createdAt,
        updatedAt: baseDoc.updatedAt,
        tags: baseDoc.tags,
        metadata: {
          version: baseDoc.metadata?.version || 1,
          size: baseDoc.metadata?.size || 0,
          checksum: baseDoc.metadata?.checksum || '',
          canvasWidth: (baseDoc.metadata as any)?.canvasWidth || 1920,
          canvasHeight: (baseDoc.metadata as any)?.canvasHeight || 1080,
          elementCount: (baseDoc.metadata as any)?.elementCount || 0,
          thumbnail: (baseDoc.metadata as any)?.thumbnail
        }
      }

      console.log('白板文档加载成功:', whiteboardDoc.title)
      return whiteboardDoc
    } catch (error) {
      console.error('加载白板文档失败:', error)
      return null
    }
  }

  /**
   * 删除白板文档
   */
  async deleteWhiteboard(id: string): Promise<boolean> {
    try {
      await documentDAO.delete(id)
      console.log('白板文档删除成功:', id)
      return true
    } catch (error) {
      console.error('删除白板文档失败:', error)
      return false
    }
  }

  /**
   * 获取所有白板文档列表
   */
  async getWhiteboardList(): Promise<WhiteboardDocument[]> {
    try {
      const baseDocs = await documentDAO.getByType(DocumentType.WHITEBOARD)
      
      return baseDocs.map(baseDoc => ({
        id: baseDoc.id,
        type: DocumentType.WHITEBOARD,
        title: baseDoc.title,
        content: baseDoc.content as WhiteboardContent,
        createdAt: baseDoc.createdAt,
        updatedAt: baseDoc.updatedAt,
        tags: baseDoc.tags,
        links: baseDoc.links || [],
        metadata: {
          version: baseDoc.metadata?.version || 1,
          size: baseDoc.metadata?.size || 0,
          checksum: baseDoc.metadata?.checksum || '',
          canvasWidth: (baseDoc.metadata as any)?.canvasWidth || 1920,
          canvasHeight: (baseDoc.metadata as any)?.canvasHeight || 1080,
          elementCount: (baseDoc.metadata as any)?.elementCount || 0,
          thumbnail: (baseDoc.metadata as any)?.thumbnail
        }
      }))
    } catch (error) {
      console.error('获取白板文档列表失败:', error)
      return []
    }
  }

  /**
   * 复制白板文档
   */
  async duplicateWhiteboard(id: string, newTitle?: string): Promise<WhiteboardDocument | null> {
    try {
      const originalWhiteboard = await this.loadWhiteboard(id)
      if (!originalWhiteboard) {
        return null
      }

      const duplicatedWhiteboard = await this.createWhiteboard(
        newTitle || `${originalWhiteboard.title} - 副本`
      )

      // 复制内容
      duplicatedWhiteboard.content = {
        ...originalWhiteboard.content,
        objects: originalWhiteboard.content.objects.map(obj => ({
          ...obj,
          id: generateUUID() // 生成新的ID
        }))
      }

      duplicatedWhiteboard.tags = [...originalWhiteboard.tags]

      await this.saveWhiteboard(duplicatedWhiteboard)
      console.log('白板文档复制成功')
      
      return duplicatedWhiteboard
    } catch (error) {
      console.error('复制白板文档失败:', error)
      return null
    }
  }

  /**
   * 导出白板为图片
   */
  async exportWhiteboardAsImage(
    whiteboard: WhiteboardDocument,
    options: { format: 'png' | 'jpg'; quality?: number; scale?: number } = { format: 'png' }
  ): Promise<string | null> {
    try {
      // 创建临时画布
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) return null

      canvas.width = whiteboard.content.canvas.width
      canvas.height = whiteboard.content.canvas.height

      // 设置背景
      ctx.fillStyle = whiteboard.content.canvas.backgroundColor
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // 绘制对象（这里需要根据实际的对象类型进行绘制）
      // 这是一个简化的实现，实际需要根据对象类型进行详细绘制
      for (const obj of whiteboard.content.objects) {
        // TODO: 根据对象类型进行绘制
        console.log('绘制对象:', obj.type)
      }

      // 导出图片
      const quality = options.quality || 0.9
      const dataURL = canvas.toDataURL(`image/${options.format}`, quality)
      
      console.log('白板导出成功')
      return dataURL
    } catch (error) {
      console.error('导出白板失败:', error)
      return null
    }
  }

  /**
   * 生成白板缩略图
   */
  async generateThumbnail(whiteboard: WhiteboardDocument): Promise<string | null> {
    try {
      const thumbnail = await this.exportWhiteboardAsImage(whiteboard, {
        format: 'png',
        scale: 0.2, // 缩放到20%
        quality: 0.7
      })

      if (thumbnail) {
        whiteboard.thumbnail = thumbnail
        await this.saveWhiteboard(whiteboard)
      }

      return thumbnail
    } catch (error) {
      console.error('生成缩略图失败:', error)
      return null
    }
  }

  /**
   * 搜索白板文档
   */
  async searchWhiteboards(query: string): Promise<WhiteboardDocument[]> {
    try {
      const allWhiteboards = await this.getWhiteboardList()
      
      if (!query.trim()) {
        return allWhiteboards
      }

      const searchTerm = query.toLowerCase()
      
      return allWhiteboards.filter(whiteboard => 
        whiteboard.title.toLowerCase().includes(searchTerm) ||
        whiteboard.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      )
    } catch (error) {
      console.error('搜索白板文档失败:', error)
      return []
    }
  }

  /**
   * 更新白板元数据
   */
  async updateWhiteboardMetadata(
    id: string, 
    metadata: Partial<WhiteboardDocument['metadata']>
  ): Promise<boolean> {
    try {
      const whiteboard = await this.loadWhiteboard(id)
      if (!whiteboard) return false

      whiteboard.metadata = { ...whiteboard.metadata, ...metadata }
      await this.saveWhiteboard(whiteboard)
      
      return true
    } catch (error) {
      console.error('更新白板元数据失败:', error)
      return false
    }
  }
}

// 导出单例实例
export const whiteboardService = new WhiteboardService()
