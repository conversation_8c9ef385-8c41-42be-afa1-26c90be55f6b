/**
 * 数据分析服务
 * 提供知识图谱分析、内容统计报表、使用习惯分析功能
 */

import { BaseDocument, DocumentType } from '@/types'
import { documentDAO } from '@services/database/documentDAO'
import { linkService } from '@services/link/linkService'
import { advancedSearchService } from '@services/search/AdvancedSearchService'

/**
 * 文档统计接口
 */
export interface DocumentStats {
  totalDocuments: number
  documentsByType: Record<DocumentType, number>
  totalWords: number
  averageWordsPerDocument: number
  documentsCreatedThisWeek: number
  documentsCreatedThisMonth: number
  documentsUpdatedToday: number
}

/**
 * 链接统计接口
 */
export interface LinkStats {
  totalLinks: number
  averageLinksPerDocument: number
  mostLinkedDocuments: Array<{
    document: BaseDocument
    linkCount: number
  }>
  orphanDocuments: BaseDocument[]
  linksByType: Record<string, number>
}

/**
 * 标签统计接口
 */
export interface TagStats {
  totalTags: number
  mostUsedTags: Array<{
    tag: string
    count: number
  }>
  tagsByType: Record<DocumentType, string[]>
  averageTagsPerDocument: number
}

/**
 * 使用习惯分析接口
 */
export interface UsageAnalytics {
  dailyActivity: Array<{
    date: string
    documentsCreated: number
    documentsUpdated: number
    searchCount: number
  }>
  weeklyActivity: Array<{
    week: string
    activity: number
  }>
  monthlyActivity: Array<{
    month: string
    activity: number
  }>
  peakHours: Array<{
    hour: number
    activity: number
  }>
  mostActiveDocumentTypes: Array<{
    type: DocumentType
    activity: number
  }>
}

/**
 * 知识图谱分析接口
 */
export interface GraphAnalytics {
  networkDensity: number
  averagePathLength: number
  clusteringCoefficient: number
  centralityScores: Array<{
    document: BaseDocument
    betweennessCentrality: number
    closenessCentrality: number
    degreeCentrality: number
  }>
  communities: Array<{
    id: string
    documents: BaseDocument[]
    connections: number
  }>
  isolatedNodes: BaseDocument[]
}

/**
 * 内容质量分析接口
 */
export interface ContentQualityAnalysis {
  documentsWithoutTags: BaseDocument[]
  documentsWithoutLinks: BaseDocument[]
  shortDocuments: BaseDocument[]
  longDocuments: BaseDocument[]
  duplicateContent: Array<{
    documents: BaseDocument[]
    similarity: number
  }>
  qualityScore: number
}

/**
 * 数据分析服务类
 */
export class AnalyticsService {
  private static instance: AnalyticsService

  /**
   * 获取实例
   */
  public static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService()
    }
    return AnalyticsService.instance
  }

  /**
   * 私有构造函数
   */
  private constructor() {}

  /**
   * 获取文档统计
   */
  async getDocumentStats(): Promise<DocumentStats> {
    try {
      const documents = await documentDAO.getAll()
      const now = new Date()
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

      const documentsByType = documents.reduce((acc, doc) => {
        acc[doc.type] = (acc[doc.type] || 0) + 1
        return acc
      }, {} as Record<DocumentType, number>)

      const totalWords = documents.reduce((sum, doc) => {
        return sum + this.getDocumentWordCount(doc)
      }, 0)

      const documentsCreatedThisWeek = documents.filter(
        doc => doc.createdAt >= oneWeekAgo
      ).length

      const documentsCreatedThisMonth = documents.filter(
        doc => doc.createdAt >= oneMonthAgo
      ).length

      const documentsUpdatedToday = documents.filter(
        doc => doc.updatedAt >= today
      ).length

      return {
        totalDocuments: documents.length,
        documentsByType,
        totalWords,
        averageWordsPerDocument: documents.length > 0 ? Math.round(totalWords / documents.length) : 0,
        documentsCreatedThisWeek,
        documentsCreatedThisMonth,
        documentsUpdatedToday
      }
    } catch (error) {
      console.error('获取文档统计失败:', error)
      throw new Error(`获取文档统计失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取链接统计
   */
  async getLinkStats(): Promise<LinkStats> {
    try {
      const documents = await documentDAO.getAll()
      const allLinks = await linkService.getAllLinks()

      // 计算每个文档的链接数
      const linkCounts = new Map<string, number>()
      const linksByType: Record<string, number> = {}

      for (const link of allLinks) {
        linkCounts.set(link.sourceId, (linkCounts.get(link.sourceId) || 0) + 1)
        linkCounts.set(link.targetId, (linkCounts.get(link.targetId) || 0) + 1)
        linksByType[link.type] = (linksByType[link.type] || 0) + 1
      }

      // 找出最多链接的文档
      const mostLinkedDocuments = documents
        .map(doc => ({
          document: doc,
          linkCount: linkCounts.get(doc.id) || 0
        }))
        .sort((a, b) => b.linkCount - a.linkCount)
        .slice(0, 10)

      // 找出孤立文档
      const orphanDocuments = documents.filter(doc => !linkCounts.has(doc.id))

      const totalLinkCount = Array.from(linkCounts.values()).reduce((sum, count) => sum + count, 0)

      return {
        totalLinks: allLinks.length,
        averageLinksPerDocument: documents.length > 0 ? Math.round(totalLinkCount / documents.length) : 0,
        mostLinkedDocuments,
        orphanDocuments,
        linksByType
      }
    } catch (error) {
      console.error('获取链接统计失败:', error)
      throw new Error(`获取链接统计失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取标签统计
   */
  async getTagStats(): Promise<TagStats> {
    try {
      const documents = await documentDAO.getAll()
      const tagCounts = new Map<string, number>()
      const tagsByType: Record<DocumentType, Set<string>> = {
        [DocumentType.TEXT]: new Set(),
        [DocumentType.WHITEBOARD]: new Set(),
        [DocumentType.MINDMAP]: new Set(),
        [DocumentType.KANBAN]: new Set()
      }

      let totalTagCount = 0

      for (const doc of documents) {
        for (const tag of doc.tags) {
          tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1)
          tagsByType[doc.type].add(tag)
          totalTagCount++
        }
      }

      const mostUsedTags = Array.from(tagCounts.entries())
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 20)

      const tagsByTypeArray: Record<DocumentType, string[]> = {
        [DocumentType.TEXT]: Array.from(tagsByType[DocumentType.TEXT]),
        [DocumentType.WHITEBOARD]: Array.from(tagsByType[DocumentType.WHITEBOARD]),
        [DocumentType.MINDMAP]: Array.from(tagsByType[DocumentType.MINDMAP]),
        [DocumentType.KANBAN]: Array.from(tagsByType[DocumentType.KANBAN])
      }

      return {
        totalTags: tagCounts.size,
        mostUsedTags,
        tagsByType: tagsByTypeArray,
        averageTagsPerDocument: documents.length > 0 ? Math.round(totalTagCount / documents.length) : 0
      }
    } catch (error) {
      console.error('获取标签统计失败:', error)
      throw new Error(`获取标签统计失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取使用习惯分析
   */
  async getUsageAnalytics(): Promise<UsageAnalytics> {
    try {
      const documents = await documentDAO.getAll()
      const searchHistory = advancedSearchService.getSearchHistory(1000)

      // 按日期分组活动
      const dailyActivity = this.groupActivityByDay(documents, searchHistory)
      const weeklyActivity = this.groupActivityByWeek(dailyActivity)
      const monthlyActivity = this.groupActivityByMonth(dailyActivity)
      const peakHours = this.calculatePeakHours(documents)
      const mostActiveDocumentTypes = this.calculateDocumentTypeActivity(documents)

      return {
        dailyActivity,
        weeklyActivity,
        monthlyActivity,
        peakHours,
        mostActiveDocumentTypes
      }
    } catch (error) {
      console.error('获取使用习惯分析失败:', error)
      throw new Error(`获取使用习惯分析失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取知识图谱分析
   */
  async getGraphAnalytics(): Promise<GraphAnalytics> {
    try {
      const documents = await documentDAO.getAll()
      const allLinks = await linkService.getAllLinks()

      // 构建邻接表
      const adjacencyList = new Map<string, Set<string>>()
      for (const doc of documents) {
        adjacencyList.set(doc.id, new Set())
      }

      for (const link of allLinks) {
        adjacencyList.get(link.sourceId)?.add(link.targetId)
        adjacencyList.get(link.targetId)?.add(link.sourceId)
      }

      // 计算网络密度
      const totalPossibleEdges = documents.length * (documents.length - 1) / 2
      const networkDensity = totalPossibleEdges > 0 ? allLinks.length / totalPossibleEdges : 0

      // 计算中心性分数
      const centralityScores = documents.map(doc => ({
        document: doc,
        betweennessCentrality: this.calculateBetweennessCentrality(doc.id, adjacencyList),
        closenessCentrality: this.calculateClosenessCentrality(doc.id, adjacencyList),
        degreeCentrality: adjacencyList.get(doc.id)?.size || 0
      }))

      // 找出孤立节点
      const isolatedNodes = documents.filter(doc => 
        (adjacencyList.get(doc.id)?.size || 0) === 0
      )

      // 简化的社区检测
      const communities = this.detectCommunities(adjacencyList, documents)

      return {
        networkDensity,
        averagePathLength: this.calculateAveragePathLength(adjacencyList),
        clusteringCoefficient: this.calculateClusteringCoefficient(adjacencyList),
        centralityScores: centralityScores.sort((a, b) => b.degreeCentrality - a.degreeCentrality),
        communities,
        isolatedNodes
      }
    } catch (error) {
      console.error('获取知识图谱分析失败:', error)
      throw new Error(`获取知识图谱分析失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取内容质量分析
   */
  async getContentQualityAnalysis(): Promise<ContentQualityAnalysis> {
    try {
      const documents = await documentDAO.getAll()
      const allLinks = await linkService.getAllLinks()

      const linkedDocumentIds = new Set<string>()
      for (const link of allLinks) {
        linkedDocumentIds.add(link.sourceId)
        linkedDocumentIds.add(link.targetId)
      }

      const documentsWithoutTags = documents.filter(doc => doc.tags.length === 0)
      const documentsWithoutLinks = documents.filter(doc => !linkedDocumentIds.has(doc.id))
      
      const wordCounts = documents.map(doc => ({
        document: doc,
        wordCount: this.getDocumentWordCount(doc)
      }))

      const shortDocuments = wordCounts
        .filter(item => item.wordCount < 50)
        .map(item => item.document)

      const longDocuments = wordCounts
        .filter(item => item.wordCount > 2000)
        .map(item => item.document)

      // 简化的重复内容检测
      const duplicateContent = this.findDuplicateContent(documents)

      // 计算质量分数
      const qualityScore = this.calculateQualityScore({
        totalDocuments: documents.length,
        documentsWithoutTags: documentsWithoutTags.length,
        documentsWithoutLinks: documentsWithoutLinks.length,
        shortDocuments: shortDocuments.length,
        duplicateGroups: duplicateContent.length
      })

      return {
        documentsWithoutTags,
        documentsWithoutLinks,
        shortDocuments,
        longDocuments,
        duplicateContent,
        qualityScore
      }
    } catch (error) {
      console.error('获取内容质量分析失败:', error)
      throw new Error(`获取内容质量分析失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取文档字数
   */
  private getDocumentWordCount(document: BaseDocument): number {
    switch (document.type) {
      case DocumentType.TEXT:
        return (document as any).content?.markdown?.length || 0
      case DocumentType.WHITEBOARD:
        return this.extractWhiteboardText(document).length
      case DocumentType.MINDMAP:
        return this.extractMindmapText(document).length
      case DocumentType.KANBAN:
        return this.extractKanbanText(document).length
      default:
        return document.title.length
    }
  }

  /**
   * 提取白板文本
   */
  private extractWhiteboardText(document: BaseDocument): string {
    const content = (document as any).content
    if (!content || !content.objects) return ''

    return content.objects
      .filter((obj: any) => obj.type === 'text' || obj.type === 'textbox')
      .map((obj: any) => obj.text || '')
      .join(' ')
  }

  /**
   * 提取思维导图文本
   */
  private extractMindmapText(document: BaseDocument): string {
    const content = (document as any).content
    if (!content || !content.nodes) return ''

    return content.nodes
      .map((node: any) => node.data?.label || '')
      .join(' ')
  }

  /**
   * 提取看板文本
   */
  private extractKanbanText(document: BaseDocument): string {
    const content = (document as any).content
    if (!content || !content.cards) return ''

    return content.cards
      .map((card: any) => `${card.title} ${card.description || ''}`)
      .join(' ')
  }

  /**
   * 按日分组活动
   */
  private groupActivityByDay(documents: BaseDocument[], searchHistory: any[]): any[] {
    const activityMap = new Map<string, any>()

    // 处理文档创建和更新
    for (const doc of documents) {
      const createdDate = doc.createdAt.toISOString().split('T')[0]
      const updatedDate = doc.updatedAt.toISOString().split('T')[0]

      if (!activityMap.has(createdDate)) {
        activityMap.set(createdDate, {
          date: createdDate,
          documentsCreated: 0,
          documentsUpdated: 0,
          searchCount: 0
        })
      }

      if (!activityMap.has(updatedDate)) {
        activityMap.set(updatedDate, {
          date: updatedDate,
          documentsCreated: 0,
          documentsUpdated: 0,
          searchCount: 0
        })
      }

      activityMap.get(createdDate)!.documentsCreated++
      if (createdDate !== updatedDate) {
        activityMap.get(updatedDate)!.documentsUpdated++
      }
    }

    // 处理搜索历史
    for (const search of searchHistory) {
      const searchDate = search.timestamp.toISOString().split('T')[0]
      if (!activityMap.has(searchDate)) {
        activityMap.set(searchDate, {
          date: searchDate,
          documentsCreated: 0,
          documentsUpdated: 0,
          searchCount: 0
        })
      }
      activityMap.get(searchDate)!.searchCount++
    }

    return Array.from(activityMap.values()).sort((a, b) => a.date.localeCompare(b.date))
  }

  /**
   * 按周分组活动
   */
  private groupActivityByWeek(dailyActivity: any[]): any[] {
    const weeklyMap = new Map<string, number>()

    for (const day of dailyActivity) {
      const date = new Date(day.date)
      const weekStart = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay())
      const weekKey = weekStart.toISOString().split('T')[0]
      
      const activity = day.documentsCreated + day.documentsUpdated + day.searchCount
      weeklyMap.set(weekKey, (weeklyMap.get(weekKey) || 0) + activity)
    }

    return Array.from(weeklyMap.entries())
      .map(([week, activity]) => ({ week, activity }))
      .sort((a, b) => a.week.localeCompare(b.week))
  }

  /**
   * 按月分组活动
   */
  private groupActivityByMonth(dailyActivity: any[]): any[] {
    const monthlyMap = new Map<string, number>()

    for (const day of dailyActivity) {
      const date = new Date(day.date)
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
      
      const activity = day.documentsCreated + day.documentsUpdated + day.searchCount
      monthlyMap.set(monthKey, (monthlyMap.get(monthKey) || 0) + activity)
    }

    return Array.from(monthlyMap.entries())
      .map(([month, activity]) => ({ month, activity }))
      .sort((a, b) => a.month.localeCompare(b.month))
  }

  /**
   * 计算高峰时段
   */
  private calculatePeakHours(documents: BaseDocument[]): any[] {
    const hourlyActivity = new Array(24).fill(0)

    for (const doc of documents) {
      const hour = doc.createdAt.getHours()
      hourlyActivity[hour]++
      
      if (doc.createdAt.getTime() !== doc.updatedAt.getTime()) {
        const updateHour = doc.updatedAt.getHours()
        hourlyActivity[updateHour]++
      }
    }

    return hourlyActivity.map((activity, hour) => ({ hour, activity }))
  }

  /**
   * 计算文档类型活动
   */
  private calculateDocumentTypeActivity(documents: BaseDocument[]): any[] {
    const typeActivity = new Map<DocumentType, number>()

    for (const doc of documents) {
      const activity = doc.createdAt.getTime() !== doc.updatedAt.getTime() ? 2 : 1
      typeActivity.set(doc.type, (typeActivity.get(doc.type) || 0) + activity)
    }

    return Array.from(typeActivity.entries())
      .map(([type, activity]) => ({ type, activity }))
      .sort((a, b) => b.activity - a.activity)
  }

  /**
   * 计算介数中心性（简化版）
   */
  private calculateBetweennessCentrality(nodeId: string, adjacencyList: Map<string, Set<string>>): number {
    // 简化的介数中心性计算
    return adjacencyList.get(nodeId)?.size || 0
  }

  /**
   * 计算接近中心性（简化版）
   */
  private calculateClosenessCentrality(nodeId: string, adjacencyList: Map<string, Set<string>>): number {
    // 简化的接近中心性计算
    const neighbors = adjacencyList.get(nodeId)?.size || 0
    return neighbors > 0 ? 1 / neighbors : 0
  }

  /**
   * 计算平均路径长度
   */
  private calculateAveragePathLength(adjacencyList: Map<string, Set<string>>): number {
    // 简化的平均路径长度计算
    const nodes = Array.from(adjacencyList.keys())
    if (nodes.length < 2) return 0

    let totalPath = 0
    let pathCount = 0

    for (const node of nodes) {
      const neighbors = adjacencyList.get(node)?.size || 0
      if (neighbors > 0) {
        totalPath += neighbors
        pathCount++
      }
    }

    return pathCount > 0 ? totalPath / pathCount : 0
  }

  /**
   * 计算聚类系数
   */
  private calculateClusteringCoefficient(adjacencyList: Map<string, Set<string>>): number {
    // 简化的聚类系数计算
    const nodes = Array.from(adjacencyList.keys())
    let totalCoefficient = 0

    for (const node of nodes) {
      const neighbors = adjacencyList.get(node)
      if (!neighbors || neighbors.size < 2) continue

      let triangles = 0
      const neighborArray = Array.from(neighbors)
      
      for (let i = 0; i < neighborArray.length; i++) {
        for (let j = i + 1; j < neighborArray.length; j++) {
          if (adjacencyList.get(neighborArray[i])?.has(neighborArray[j])) {
            triangles++
          }
        }
      }

      const possibleTriangles = neighbors.size * (neighbors.size - 1) / 2
      totalCoefficient += possibleTriangles > 0 ? triangles / possibleTriangles : 0
    }

    return nodes.length > 0 ? totalCoefficient / nodes.length : 0
  }

  /**
   * 检测社区（简化版）
   */
  private detectCommunities(adjacencyList: Map<string, Set<string>>, documents: BaseDocument[]): any[] {
    // 简化的社区检测：按文档类型分组
    const communities = new Map<DocumentType, BaseDocument[]>()

    for (const doc of documents) {
      if (!communities.has(doc.type)) {
        communities.set(doc.type, [])
      }
      communities.get(doc.type)!.push(doc)
    }

    return Array.from(communities.entries()).map(([type, docs], index) => ({
      id: `community-${type}`,
      documents: docs,
      connections: docs.reduce((sum, doc) => sum + (adjacencyList.get(doc.id)?.size || 0), 0)
    }))
  }

  /**
   * 查找重复内容
   */
  private findDuplicateContent(documents: BaseDocument[]): any[] {
    const duplicates: any[] = []
    const titleMap = new Map<string, BaseDocument[]>()

    // 按标题分组
    for (const doc of documents) {
      const normalizedTitle = doc.title.toLowerCase().trim()
      if (!titleMap.has(normalizedTitle)) {
        titleMap.set(normalizedTitle, [])
      }
      titleMap.get(normalizedTitle)!.push(doc)
    }

    // 找出重复的标题
    for (const [title, docs] of titleMap.entries()) {
      if (docs.length > 1) {
        duplicates.push({
          documents: docs,
          similarity: 1.0 // 标题完全相同
        })
      }
    }

    return duplicates
  }

  /**
   * 计算质量分数
   */
  private calculateQualityScore(metrics: {
    totalDocuments: number
    documentsWithoutTags: number
    documentsWithoutLinks: number
    shortDocuments: number
    duplicateGroups: number
  }): number {
    if (metrics.totalDocuments === 0) return 100

    const tagScore = (1 - metrics.documentsWithoutTags / metrics.totalDocuments) * 25
    const linkScore = (1 - metrics.documentsWithoutLinks / metrics.totalDocuments) * 25
    const lengthScore = (1 - metrics.shortDocuments / metrics.totalDocuments) * 25
    const duplicateScore = (1 - metrics.duplicateGroups / metrics.totalDocuments) * 25

    return Math.round(tagScore + linkScore + lengthScore + duplicateScore)
  }
}

/**
 * 导出数据分析服务实例
 */
export const analyticsService = AnalyticsService.getInstance()
