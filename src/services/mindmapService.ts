/**
 * 思维导图服务
 * 提供思维导图文档的创建、保存、加载等功能
 */

import { documentDAO } from './database/documentDAO'
import { generateUUID } from '@utils/index'
import { 
  MindMapDocument, 
  MindMapContent,
  DocumentType, 
  BaseDocument 
} from '@/types'

/**
 * 思维导图节点接口
 */
export interface MindMapNode {
  id: string
  text: string
  x: number
  y: number
  width: number
  height: number
  color: string
  backgroundColor: string
  fontSize: number
  children: string[]
  parent?: string
  collapsed: boolean
  level: number
}

/**
 * 思维导图连接线接口
 */
export interface MindMapConnection {
  id: string
  from: string
  to: string
  color: string
  width: number
}

/**
 * 思维导图数据接口
 */
export interface MindMapData {
  nodes: MindMapNode[]
  connections: MindMapConnection[]
}

/**
 * 思维导图服务类
 */
export class MindMapService {
  /**
   * 创建新的思维导图文档
   */
  async createMindMap(title: string = '新建思维导图'): Promise<MindMapDocument> {
    const now = new Date()
    
    // 创建默认的思维导图内容
    const mindmapContent: MindMapContent = {
      nodes: [{
        id: generateUUID(),
        text: '中心主题',
        x: 600,
        y: 400,
        width: 120,
        height: 40,
        color: '#000000',
        backgroundColor: '#ffffff',
        fontSize: 16,
        children: [],
        collapsed: false,
        level: 0
      }],
      connections: [],
      layout: {
        type: 'radial',
        direction: 'horizontal',
        spacing: 100
      },
      viewport: {
        x: 0,
        y: 0,
        zoom: 1
      },
      theme: {
        nodeColor: '#000000',
        nodeBackgroundColor: '#ffffff',
        connectionColor: '#666666',
        fontSize: 14
      }
    }

    const mindmapDoc: MindMapDocument = {
      id: generateUUID(),
      type: DocumentType.MINDMAP,
      title,
      content: mindmapContent,
      createdAt: now,
      updatedAt: now,
      tags: [],
      metadata: {
        version: '1.0.0',
        nodeCount: 1,
        maxLevel: 0
      }
    }

    // 转换为基础文档格式保存到数据库
    const baseDoc: BaseDocument = {
      id: mindmapDoc.id,
      type: DocumentType.MINDMAP,
      title: mindmapDoc.title,
      content: mindmapDoc.content,
      createdAt: mindmapDoc.createdAt,
      updatedAt: mindmapDoc.updatedAt,
      tags: mindmapDoc.tags,
      metadata: mindmapDoc.metadata
    }

    await documentDAO.create(baseDoc)
    console.log('思维导图文档创建成功:', mindmapDoc.title)
    
    return mindmapDoc
  }

  /**
   * 保存思维导图文档
   */
  async saveMindMap(mindmap: MindMapDocument): Promise<void> {
    const updatedMindMap = {
      ...mindmap,
      updatedAt: new Date(),
      metadata: {
        ...mindmap.metadata,
        nodeCount: mindmap.content.nodes.length,
        maxLevel: Math.max(...mindmap.content.nodes.map(node => node.level))
      }
    }

    const baseDoc: BaseDocument = {
      id: updatedMindMap.id,
      type: DocumentType.MINDMAP,
      title: updatedMindMap.title,
      content: updatedMindMap.content,
      createdAt: updatedMindMap.createdAt,
      updatedAt: updatedMindMap.updatedAt,
      tags: updatedMindMap.tags,
      metadata: updatedMindMap.metadata
    }

    await documentDAO.update(baseDoc)
    console.log('思维导图文档保存成功:', updatedMindMap.title)
  }

  /**
   * 加载思维导图文档
   */
  async loadMindMap(id: string): Promise<MindMapDocument | null> {
    try {
      const baseDoc = await documentDAO.getById(id)
      
      if (!baseDoc || baseDoc.type !== DocumentType.MINDMAP) {
        console.warn('思维导图文档不存在或类型不匹配:', id)
        return null
      }

      const mindmapDoc: MindMapDocument = {
        id: baseDoc.id,
        type: DocumentType.MINDMAP,
        title: baseDoc.title,
        content: baseDoc.content as MindMapContent,
        createdAt: baseDoc.createdAt,
        updatedAt: baseDoc.updatedAt,
        tags: baseDoc.tags,
        metadata: {
          version: baseDoc.metadata?.version || '1.0.0',
          nodeCount: baseDoc.metadata?.nodeCount || 0,
          maxLevel: baseDoc.metadata?.maxLevel || 0,
          thumbnail: baseDoc.metadata?.thumbnail
        }
      }

      console.log('思维导图文档加载成功:', mindmapDoc.title)
      return mindmapDoc
    } catch (error) {
      console.error('加载思维导图文档失败:', error)
      return null
    }
  }

  /**
   * 删除思维导图文档
   */
  async deleteMindMap(id: string): Promise<boolean> {
    try {
      await documentDAO.delete(id)
      console.log('思维导图文档删除成功:', id)
      return true
    } catch (error) {
      console.error('删除思维导图文档失败:', error)
      return false
    }
  }

  /**
   * 获取所有思维导图文档列表
   */
  async getMindMapList(): Promise<MindMapDocument[]> {
    try {
      const baseDocs = await documentDAO.getByType(DocumentType.MINDMAP)
      
      return baseDocs.map(baseDoc => ({
        id: baseDoc.id,
        type: DocumentType.MINDMAP,
        title: baseDoc.title,
        content: baseDoc.content as MindMapContent,
        createdAt: baseDoc.createdAt,
        updatedAt: baseDoc.updatedAt,
        tags: baseDoc.tags,
        metadata: {
          version: baseDoc.metadata?.version || '1.0.0',
          nodeCount: baseDoc.metadata?.nodeCount || 0,
          maxLevel: baseDoc.metadata?.maxLevel || 0,
          thumbnail: baseDoc.metadata?.thumbnail
        }
      }))
    } catch (error) {
      console.error('获取思维导图文档列表失败:', error)
      return []
    }
  }

  /**
   * 复制思维导图文档
   */
  async duplicateMindMap(id: string, newTitle?: string): Promise<MindMapDocument | null> {
    try {
      const originalMindMap = await this.loadMindMap(id)
      if (!originalMindMap) {
        return null
      }

      const duplicatedMindMap = await this.createMindMap(
        newTitle || `${originalMindMap.title} - 副本`
      )

      // 复制内容
      duplicatedMindMap.content = {
        ...originalMindMap.content,
        nodes: originalMindMap.content.nodes.map(node => ({
          ...node,
          id: generateUUID() // 生成新的ID
        })),
        connections: originalMindMap.content.connections.map(conn => ({
          ...conn,
          id: generateUUID() // 生成新的ID
        }))
      }

      duplicatedMindMap.tags = [...originalMindMap.tags]

      await this.saveMindMap(duplicatedMindMap)
      console.log('思维导图文档复制成功')
      
      return duplicatedMindMap
    } catch (error) {
      console.error('复制思维导图文档失败:', error)
      return null
    }
  }

  /**
   * 更新思维导图数据
   */
  async updateMindMapData(id: string, data: MindMapData): Promise<boolean> {
    try {
      const mindmap = await this.loadMindMap(id)
      if (!mindmap) return false

      mindmap.content.nodes = data.nodes
      mindmap.content.connections = data.connections
      
      await this.saveMindMap(mindmap)
      return true
    } catch (error) {
      console.error('更新思维导图数据失败:', error)
      return false
    }
  }

  /**
   * 搜索思维导图文档
   */
  async searchMindMaps(query: string): Promise<MindMapDocument[]> {
    try {
      const allMindMaps = await this.getMindMapList()
      
      if (!query.trim()) {
        return allMindMaps
      }

      const searchTerm = query.toLowerCase()
      
      return allMindMaps.filter(mindmap => 
        mindmap.title.toLowerCase().includes(searchTerm) ||
        mindmap.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
        mindmap.content.nodes.some(node => 
          node.text.toLowerCase().includes(searchTerm)
        )
      )
    } catch (error) {
      console.error('搜索思维导图文档失败:', error)
      return []
    }
  }

  /**
   * 导出思维导图为图片
   */
  async exportMindMapAsImage(
    mindmap: MindMapDocument, 
    options: { format: 'png' | 'jpg'; quality?: number } = { format: 'png' }
  ): Promise<string | null> {
    try {
      // 创建临时画布
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) return null

      // 计算画布尺寸
      const nodes = mindmap.content.nodes
      if (nodes.length === 0) return null

      const minX = Math.min(...nodes.map(n => n.x))
      const maxX = Math.max(...nodes.map(n => n.x + n.width))
      const minY = Math.min(...nodes.map(n => n.y))
      const maxY = Math.max(...nodes.map(n => n.y + n.height))

      const padding = 50
      canvas.width = maxX - minX + padding * 2
      canvas.height = maxY - minY + padding * 2

      // 设置背景
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // 绘制连接线
      mindmap.content.connections.forEach(connection => {
        const fromNode = nodes.find(n => n.id === connection.from)
        const toNode = nodes.find(n => n.id === connection.to)
        
        if (fromNode && toNode) {
          ctx.strokeStyle = connection.color
          ctx.lineWidth = connection.width
          ctx.beginPath()
          ctx.moveTo(
            fromNode.x - minX + padding + fromNode.width / 2,
            fromNode.y - minY + padding + fromNode.height / 2
          )
          ctx.lineTo(
            toNode.x - minX + padding + toNode.width / 2,
            toNode.y - minY + padding + toNode.height / 2
          )
          ctx.stroke()
        }
      })

      // 绘制节点
      nodes.forEach(node => {
        const x = node.x - minX + padding
        const y = node.y - minY + padding

        // 绘制节点背景
        ctx.fillStyle = node.backgroundColor
        ctx.strokeStyle = node.color
        ctx.lineWidth = 1
        ctx.fillRect(x, y, node.width, node.height)
        ctx.strokeRect(x, y, node.width, node.height)

        // 绘制节点文本
        ctx.fillStyle = node.color
        ctx.font = `${node.fontSize}px Arial`
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillText(
          node.text,
          x + node.width / 2,
          y + node.height / 2
        )
      })

      // 导出图片
      const quality = options.quality || 0.9
      const dataURL = canvas.toDataURL(`image/${options.format}`, quality)
      
      console.log('思维导图导出成功')
      return dataURL
    } catch (error) {
      console.error('导出思维导图失败:', error)
      return null
    }
  }

  /**
   * 生成思维导图缩略图
   */
  async generateThumbnail(mindmap: MindMapDocument): Promise<string | null> {
    try {
      const thumbnail = await this.exportMindMapAsImage(mindmap, {
        format: 'png',
        quality: 0.7
      })

      if (thumbnail && mindmap.metadata) {
        mindmap.metadata.thumbnail = thumbnail
        await this.saveMindMap(mindmap)
      }

      return thumbnail
    } catch (error) {
      console.error('生成缩略图失败:', error)
      return null
    }
  }
}

// 导出单例实例
export const mindmapService = new MindMapService()
