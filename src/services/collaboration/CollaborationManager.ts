/**
 * 协作编辑管理器
 * 实现多人实时协作编辑功能
 */

import { BaseDocument } from '@/types'

/**
 * 用户信息接口
 */
export interface CollaborationUser {
  id: string
  name: string
  avatar?: string
  color: string
  isOnline: boolean
  lastSeen: Date
  cursor?: {
    documentId: string
    position: number
    selection?: { start: number; end: number }
  }
}

/**
 * 编辑操作接口
 */
export interface EditOperation {
  id: string
  type: 'insert' | 'delete' | 'replace' | 'format'
  documentId: string
  userId: string
  position: number
  content?: string
  length?: number
  attributes?: Record<string, any>
  timestamp: Date
  applied: boolean
}

/**
 * 协作会话接口
 */
export interface CollaborationSession {
  id: string
  documentId: string
  participants: CollaborationUser[]
  operations: EditOperation[]
  createdAt: Date
  lastActivity: Date
  isActive: boolean
}

/**
 * 冲突解决策略
 */
export enum ConflictResolutionStrategy {
  LAST_WRITE_WINS = 'last_write_wins',
  OPERATIONAL_TRANSFORM = 'operational_transform',
  MANUAL_RESOLVE = 'manual_resolve'
}

/**
 * 协作编辑管理器类
 */
export class CollaborationManager {
  private sessions: Map<string, CollaborationSession> = new Map()
  private users: Map<string, CollaborationUser> = new Map()
  private operationQueue: Map<string, EditOperation[]> = new Map()
  private conflictResolutionStrategy = ConflictResolutionStrategy.OPERATIONAL_TRANSFORM

  /**
   * 创建协作会话
   */
  createSession(documentId: string, initiator: CollaborationUser): CollaborationSession {
    const session: CollaborationSession = {
      id: this.generateSessionId(),
      documentId,
      participants: [initiator],
      operations: [],
      createdAt: new Date(),
      lastActivity: new Date(),
      isActive: true
    }

    this.sessions.set(session.id, session)
    this.users.set(initiator.id, { ...initiator, isOnline: true })
    
    return session
  }

  /**
   * 加入协作会话
   */
  joinSession(sessionId: string, user: CollaborationUser): boolean {
    const session = this.sessions.get(sessionId)
    if (!session || !session.isActive) return false

    // 检查用户是否已在会话中
    const existingUser = session.participants.find(p => p.id === user.id)
    if (existingUser) {
      existingUser.isOnline = true
      existingUser.lastSeen = new Date()
    } else {
      session.participants.push(user)
    }

    this.users.set(user.id, { ...user, isOnline: true })
    session.lastActivity = new Date()

    return true
  }

  /**
   * 离开协作会话
   */
  leaveSession(sessionId: string, userId: string): boolean {
    const session = this.sessions.get(sessionId)
    if (!session) return false

    const user = session.participants.find(p => p.id === userId)
    if (user) {
      user.isOnline = false
      user.lastSeen = new Date()
    }

    // 如果没有在线用户，标记会话为非活跃
    const onlineUsers = session.participants.filter(p => p.isOnline)
    if (onlineUsers.length === 0) {
      session.isActive = false
    }

    return true
  }

  /**
   * 应用编辑操作
   */
  applyOperation(operation: EditOperation): boolean {
    const session = this.getSessionByDocumentId(operation.documentId)
    if (!session) return false

    // 检查用户是否有权限
    const user = session.participants.find(p => p.id === operation.userId)
    if (!user || !user.isOnline) return false

    // 添加到操作队列
    const queue = this.operationQueue.get(operation.documentId) || []
    queue.push(operation)
    this.operationQueue.set(operation.documentId, queue)

    // 处理操作冲突
    const transformedOperation = this.resolveConflicts(operation, queue)
    
    // 应用操作
    session.operations.push(transformedOperation)
    session.lastActivity = new Date()
    transformedOperation.applied = true

    return true
  }

  /**
   * 获取文档的协作会话
   */
  getSessionByDocumentId(documentId: string): CollaborationSession | null {
    for (const session of this.sessions.values()) {
      if (session.documentId === documentId && session.isActive) {
        return session
      }
    }
    return null
  }

  /**
   * 更新用户光标位置
   */
  updateUserCursor(
    userId: string,
    documentId: string,
    position: number,
    selection?: { start: number; end: number }
  ): boolean {
    const user = this.users.get(userId)
    if (!user) return false

    user.cursor = {
      documentId,
      position,
      selection
    }

    return true
  }

  /**
   * 获取文档的所有用户光标
   */
  getDocumentCursors(documentId: string): Map<string, CollaborationUser['cursor']> {
    const cursors = new Map()
    
    for (const [userId, user] of this.users) {
      if (user.cursor && user.cursor.documentId === documentId && user.isOnline) {
        cursors.set(userId, user.cursor)
      }
    }

    return cursors
  }

  /**
   * 解决操作冲突
   */
  private resolveConflicts(
    operation: EditOperation,
    operationQueue: EditOperation[]
  ): EditOperation {
    switch (this.conflictResolutionStrategy) {
      case ConflictResolutionStrategy.OPERATIONAL_TRANSFORM:
        return this.operationalTransform(operation, operationQueue)
      
      case ConflictResolutionStrategy.LAST_WRITE_WINS:
        return operation // 直接返回最新操作
      
      case ConflictResolutionStrategy.MANUAL_RESOLVE:
        // 标记为需要手动解决
        return { ...operation, attributes: { ...operation.attributes, needsManualResolve: true } }
      
      default:
        return operation
    }
  }

  /**
   * 操作变换算法
   */
  private operationalTransform(
    operation: EditOperation,
    operationQueue: EditOperation[]
  ): EditOperation {
    let transformedOperation = { ...operation }

    // 获取同时进行的操作
    const concurrentOps = operationQueue.filter(op => 
      op.timestamp >= operation.timestamp && 
      op.id !== operation.id &&
      !op.applied
    )

    // 对每个并发操作进行变换
    for (const concurrentOp of concurrentOps) {
      transformedOperation = this.transformOperation(transformedOperation, concurrentOp)
    }

    return transformedOperation
  }

  /**
   * 变换单个操作
   */
  private transformOperation(op1: EditOperation, op2: EditOperation): EditOperation {
    // 简化的操作变换逻辑
    if (op1.type === 'insert' && op2.type === 'insert') {
      if (op2.position <= op1.position) {
        return {
          ...op1,
          position: op1.position + (op2.content?.length || 0)
        }
      }
    } else if (op1.type === 'delete' && op2.type === 'insert') {
      if (op2.position <= op1.position) {
        return {
          ...op1,
          position: op1.position + (op2.content?.length || 0)
        }
      }
    } else if (op1.type === 'insert' && op2.type === 'delete') {
      if (op2.position < op1.position) {
        return {
          ...op1,
          position: Math.max(op2.position, op1.position - (op2.length || 0))
        }
      }
    } else if (op1.type === 'delete' && op2.type === 'delete') {
      if (op2.position < op1.position) {
        return {
          ...op1,
          position: Math.max(op2.position, op1.position - (op2.length || 0))
        }
      }
    }

    return op1
  }

  /**
   * 获取文档的操作历史
   */
  getDocumentOperations(documentId: string): EditOperation[] {
    const session = this.getSessionByDocumentId(documentId)
    return session ? session.operations : []
  }

  /**
   * 撤销操作
   */
  undoOperation(documentId: string, userId: string): EditOperation | null {
    const session = this.getSessionByDocumentId(documentId)
    if (!session) return null

    // 找到用户的最后一个操作
    const userOperations = session.operations
      .filter(op => op.userId === userId && op.applied)
      .reverse()

    if (userOperations.length === 0) return null

    const lastOperation = userOperations[0]
    
    // 创建反向操作
    const undoOperation: EditOperation = {
      id: this.generateOperationId(),
      type: this.getInverseOperationType(lastOperation.type),
      documentId,
      userId,
      position: lastOperation.position,
      content: lastOperation.type === 'delete' ? lastOperation.content : undefined,
      length: lastOperation.type === 'insert' ? lastOperation.content?.length : undefined,
      timestamp: new Date(),
      applied: false
    }

    return undoOperation
  }

  /**
   * 获取反向操作类型
   */
  private getInverseOperationType(type: EditOperation['type']): EditOperation['type'] {
    switch (type) {
      case 'insert': return 'delete'
      case 'delete': return 'insert'
      case 'replace': return 'replace'
      case 'format': return 'format'
      default: return type
    }
  }

  /**
   * 获取会话统计信息
   */
  getSessionStats(sessionId: string): {
    participantCount: number
    onlineCount: number
    operationCount: number
    duration: number
  } | null {
    const session = this.sessions.get(sessionId)
    if (!session) return null

    const onlineCount = session.participants.filter(p => p.isOnline).length
    const duration = Date.now() - session.createdAt.getTime()

    return {
      participantCount: session.participants.length,
      onlineCount,
      operationCount: session.operations.length,
      duration
    }
  }

  /**
   * 清理非活跃会话
   */
  cleanupInactiveSessions(maxInactiveTime = 24 * 60 * 60 * 1000): number {
    let cleanedCount = 0
    const now = Date.now()

    for (const [sessionId, session] of this.sessions) {
      const inactiveTime = now - session.lastActivity.getTime()
      
      if (!session.isActive || inactiveTime > maxInactiveTime) {
        this.sessions.delete(sessionId)
        this.operationQueue.delete(session.documentId)
        cleanedCount++
      }
    }

    return cleanedCount
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return 'session_' + Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 生成操作ID
   */
  private generateOperationId(): string {
    return 'op_' + Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 设置冲突解决策略
   */
  setConflictResolutionStrategy(strategy: ConflictResolutionStrategy): void {
    this.conflictResolutionStrategy = strategy
  }

  /**
   * 获取所有活跃会话
   */
  getActiveSessions(): CollaborationSession[] {
    return Array.from(this.sessions.values()).filter(session => session.isActive)
  }

  /**
   * 获取用户参与的会话
   */
  getUserSessions(userId: string): CollaborationSession[] {
    return Array.from(this.sessions.values()).filter(session =>
      session.participants.some(p => p.id === userId)
    )
  }
}

// 导出单例实例
export const collaborationManager = new CollaborationManager()
