/**
 * 跨格式关联管理器
 * 实现不同格式文档间的关联功能和统一引用管理
 */

import { BaseDocument, DocumentType } from '@/types'
import { DocumentLink, LinkType } from '@components/link/LinkCreator'

/**
 * 跨格式引用类型
 */
export enum CrossFormatReferenceType {
  EMBED = 'embed',           // 嵌入引用
  LINK = 'link',            // 链接引用
  EXTRACT = 'extract',      // 提取引用
  TRANSFORM = 'transform'   // 转换引用
}

/**
 * 跨格式引用接口
 */
export interface CrossFormatReference {
  id: string
  sourceDocumentId: string
  targetDocumentId: string
  sourceType: DocumentType
  targetType: DocumentType
  referenceType: CrossFormatReferenceType
  sourceSelection?: {
    start: number
    end: number
    content: string
  }
  targetSelection?: {
    nodeId?: string
    objectId?: string
    cardId?: string
    content: string
  }
  metadata?: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

/**
 * 格式转换配置
 */
export interface FormatConversionConfig {
  sourceType: DocumentType
  targetType: DocumentType
  converter: (sourceContent: string, options?: any) => string
  validator: (content: string) => boolean
}

/**
 * 跨格式关联管理器类
 */
export class CrossFormatLinkManager {
  private references: Map<string, CrossFormatReference> = new Map()
  private converters: Map<string, FormatConversionConfig> = new Map()

  constructor() {
    this.initializeConverters()
  }

  /**
   * 初始化格式转换器
   */
  private initializeConverters(): void {
    // 文本到思维导图转换
    this.registerConverter({
      sourceType: DocumentType.TEXT,
      targetType: DocumentType.MINDMAP,
      converter: this.textToMindMap.bind(this),
      validator: this.validateMindMapContent.bind(this)
    })

    // 文本到看板转换
    this.registerConverter({
      sourceType: DocumentType.TEXT,
      targetType: DocumentType.KANBAN,
      converter: this.textToKanban.bind(this),
      validator: this.validateKanbanContent.bind(this)
    })

    // 思维导图到文本转换
    this.registerConverter({
      sourceType: DocumentType.MINDMAP,
      targetType: DocumentType.TEXT,
      converter: this.mindMapToText.bind(this),
      validator: this.validateTextContent.bind(this)
    })

    // 看板到文本转换
    this.registerConverter({
      sourceType: DocumentType.KANBAN,
      targetType: DocumentType.TEXT,
      converter: this.kanbanToText.bind(this),
      validator: this.validateTextContent.bind(this)
    })
  }

  /**
   * 注册格式转换器
   */
  registerConverter(config: FormatConversionConfig): void {
    const key = `${config.sourceType}->${config.targetType}`
    this.converters.set(key, config)
  }

  /**
   * 创建跨格式引用
   */
  createReference(
    sourceDocument: BaseDocument,
    targetDocument: BaseDocument,
    referenceType: CrossFormatReferenceType,
    options?: {
      sourceSelection?: CrossFormatReference['sourceSelection']
      targetSelection?: CrossFormatReference['targetSelection']
      metadata?: Record<string, any>
    }
  ): CrossFormatReference {
    const reference: CrossFormatReference = {
      id: this.generateId(),
      sourceDocumentId: sourceDocument.id,
      targetDocumentId: targetDocument.id,
      sourceType: sourceDocument.type,
      targetType: targetDocument.type,
      referenceType,
      sourceSelection: options?.sourceSelection,
      targetSelection: options?.targetSelection,
      metadata: options?.metadata,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    this.references.set(reference.id, reference)
    return reference
  }

  /**
   * 获取文档的所有引用
   */
  getDocumentReferences(documentId: string): CrossFormatReference[] {
    return Array.from(this.references.values()).filter(
      ref => ref.sourceDocumentId === documentId || ref.targetDocumentId === documentId
    )
  }

  /**
   * 获取特定类型的引用
   */
  getReferencesByType(
    documentId: string,
    referenceType: CrossFormatReferenceType
  ): CrossFormatReference[] {
    return this.getDocumentReferences(documentId).filter(
      ref => ref.referenceType === referenceType
    )
  }

  /**
   * 转换文档格式
   */
  convertDocument(
    sourceDocument: BaseDocument,
    targetType: DocumentType,
    options?: any
  ): string | null {
    const key = `${sourceDocument.type}->${targetType}`
    const converter = this.converters.get(key)

    if (!converter) {
      console.warn(`没有找到从 ${sourceDocument.type} 到 ${targetType} 的转换器`)
      return null
    }

    try {
      const convertedContent = converter.converter(sourceDocument.content, options)
      
      if (converter.validator(convertedContent)) {
        return convertedContent
      } else {
        console.error('转换后的内容验证失败')
        return null
      }
    } catch (error) {
      console.error('文档格式转换失败:', error)
      return null
    }
  }

  /**
   * 文本到思维导图转换
   */
  private textToMindMap(textContent: string): string {
    const lines = textContent.split('\n').filter(line => line.trim())
    const nodes = []
    const edges = []

    // 创建根节点
    const rootId = 'root'
    nodes.push({
      id: rootId,
      type: 'root',
      position: { x: 400, y: 300 },
      data: { label: '主题', level: 0 }
    })

    // 解析文本结构
    let nodeIndex = 1
    lines.forEach((line, index) => {
      const trimmedLine = line.trim()
      if (trimmedLine) {
        const nodeId = `node-${nodeIndex++}`
        const level = this.getIndentLevel(line)
        
        nodes.push({
          id: nodeId,
          type: level === 0 ? 'branch' : 'leaf',
          position: { x: 400 + (level + 1) * 200, y: 300 + index * 60 },
          data: { label: trimmedLine, level: level + 1 }
        })

        // 连接到根节点或父节点
        const parentId = level === 0 ? rootId : `node-${nodeIndex - 2}`
        edges.push({
          id: `edge-${parentId}-${nodeId}`,
          source: parentId,
          target: nodeId,
          type: 'smoothstep'
        })
      }
    })

    return JSON.stringify({ nodes, edges })
  }

  /**
   * 文本到看板转换
   */
  private textToKanban(textContent: string): string {
    const lines = textContent.split('\n').filter(line => line.trim())
    const cards = []
    
    let cardIndex = 1
    lines.forEach(line => {
      const trimmedLine = line.trim()
      if (trimmedLine) {
        cards.push({
          id: `card-${cardIndex++}`,
          title: trimmedLine,
          description: '',
          status: 'todo',
          priority: 'medium',
          tags: [],
          createdAt: new Date(),
          updatedAt: new Date()
        })
      }
    })

    return JSON.stringify({
      columns: [
        { id: 'todo', title: '待办', status: 'todo', color: '#f0f0f0' },
        { id: 'in_progress', title: '进行中', status: 'in_progress', color: '#e6f7ff' },
        { id: 'review', title: '待审核', status: 'review', color: '#fff7e6' },
        { id: 'done', title: '已完成', status: 'done', color: '#f6ffed' }
      ],
      cards
    })
  }

  /**
   * 思维导图到文本转换
   */
  private mindMapToText(mindMapContent: string): string {
    try {
      const data = JSON.parse(mindMapContent)
      const { nodes, edges } = data

      if (!nodes || !edges) return ''

      // 构建节点层级关系
      const nodeMap = new Map(nodes.map((node: any) => [node.id, node]))
      const hierarchy = this.buildHierarchy(nodes, edges)
      
      return this.hierarchyToText(hierarchy, nodeMap)
    } catch (error) {
      console.error('思维导图转文本失败:', error)
      return ''
    }
  }

  /**
   * 看板到文本转换
   */
  private kanbanToText(kanbanContent: string): string {
    try {
      const data = JSON.parse(kanbanContent)
      const { cards, columns } = data

      if (!cards) return ''

      let text = ''
      
      // 按状态分组
      columns?.forEach((column: any) => {
        const columnCards = cards.filter((card: any) => card.status === column.status)
        if (columnCards.length > 0) {
          text += `## ${column.title}\n\n`
          columnCards.forEach((card: any) => {
            text += `- ${card.title}\n`
            if (card.description) {
              text += `  ${card.description}\n`
            }
          })
          text += '\n'
        }
      })

      return text
    } catch (error) {
      console.error('看板转文本失败:', error)
      return ''
    }
  }

  /**
   * 构建层级关系
   */
  private buildHierarchy(nodes: any[], edges: any[]): any {
    const rootNode = nodes.find(node => node.type === 'root' || node.id === 'root')
    if (!rootNode) return null

    const childrenMap = new Map()
    edges.forEach(edge => {
      if (!childrenMap.has(edge.source)) {
        childrenMap.set(edge.source, [])
      }
      childrenMap.get(edge.source).push(edge.target)
    })

    const buildTree = (nodeId: string): any => {
      const node = nodes.find(n => n.id === nodeId)
      const children = childrenMap.get(nodeId) || []
      
      return {
        ...node,
        children: children.map(buildTree)
      }
    }

    return buildTree(rootNode.id)
  }

  /**
   * 层级结构转文本
   */
  private hierarchyToText(hierarchy: any, nodeMap: Map<string, any>, level = 0): string {
    if (!hierarchy) return ''

    const indent = '  '.repeat(level)
    let text = level === 0 ? `# ${hierarchy.data.label}\n\n` : `${indent}- ${hierarchy.data.label}\n`

    if (hierarchy.children) {
      hierarchy.children.forEach((child: any) => {
        text += this.hierarchyToText(child, nodeMap, level + 1)
      })
    }

    return text
  }

  /**
   * 获取缩进级别
   */
  private getIndentLevel(line: string): number {
    const match = line.match(/^(\s*)/)
    return match ? Math.floor(match[1].length / 2) : 0
  }

  /**
   * 验证内容格式
   */
  private validateMindMapContent(content: string): boolean {
    try {
      const data = JSON.parse(content)
      return data.nodes && data.edges && Array.isArray(data.nodes) && Array.isArray(data.edges)
    } catch {
      return false
    }
  }

  private validateKanbanContent(content: string): boolean {
    try {
      const data = JSON.parse(content)
      return data.columns && data.cards && Array.isArray(data.columns) && Array.isArray(data.cards)
    } catch {
      return false
    }
  }

  private validateTextContent(content: string): boolean {
    return typeof content === 'string' && content.length > 0
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 删除引用
   */
  deleteReference(referenceId: string): boolean {
    return this.references.delete(referenceId)
  }

  /**
   * 更新引用
   */
  updateReference(referenceId: string, updates: Partial<CrossFormatReference>): boolean {
    const reference = this.references.get(referenceId)
    if (!reference) return false

    const updatedReference = {
      ...reference,
      ...updates,
      updatedAt: new Date()
    }

    this.references.set(referenceId, updatedReference)
    return true
  }

  /**
   * 获取所有引用
   */
  getAllReferences(): CrossFormatReference[] {
    return Array.from(this.references.values())
  }
}

// 导出单例实例
export const crossFormatLinkManager = new CrossFormatLinkManager()
