/**
 * 文档管理服务
 * 提供文档的高级管理功能，包括创建、组织、搜索、标签管理等
 */

import { documentDAO } from '@services/database/documentDAO'
import { linkDAO } from '@services/database/linkDAO'
import { 
  BaseDocument, 
  TextDocument, 
  DocumentType, 
  TextContent, 
  SearchOptions,
  DocumentLink,
  LinkType
} from '@/types'
import { generateUUID, calculateReadingTime, getStringByteLength } from '@utils/index'

/**
 * 文档创建选项接口
 */
export interface CreateDocumentOptions {
  title?: string
  type: DocumentType
  content?: any
  tags?: string[]
  parentId?: string
  template?: string
}

/**
 * 文档统计信息接口
 */
export interface DocumentStats {
  totalDocuments: number
  documentsByType: Record<DocumentType, number>
  totalTags: number
  mostUsedTags: Array<{ tag: string; count: number }>
  recentActivity: Array<{
    documentId: string
    title: string
    action: 'created' | 'updated' | 'deleted'
    timestamp: Date
  }>
}

/**
 * 文档管理器类
 */
export class DocumentManager {
  private static instance: DocumentManager

  /**
   * 获取文档管理器实例
   */
  public static getInstance(): DocumentManager {
    if (!DocumentManager.instance) {
      DocumentManager.instance = new DocumentManager()
    }
    return DocumentManager.instance
  }

  /**
   * 私有构造函数
   */
  private constructor() {}

  /**
   * 创建新文档
   * @param options 创建选项
   * @returns Promise<BaseDocument> 创建的文档
   */
  async createDocument(options: CreateDocumentOptions): Promise<BaseDocument> {
    try {
      console.log('开始创建文档:', options.type)

      // 根据文档类型创建相应的内容结构
      const content = this.createContentByType(options.type, options.content)
      
      // 构建文档对象
      const documentData: Omit<BaseDocument, 'id' | 'createdAt' | 'updatedAt'> = {
        title: options.title || this.generateDefaultTitle(options.type),
        type: options.type,
        content,
        metadata: this.generateMetadata(content),
        tags: options.tags || [],
        links: [],
        parentId: options.parentId
      }

      // 创建文档
      const document = await documentDAO.create(documentData)

      // 如果有父文档，创建父子关系链接
      if (options.parentId) {
        await this.createParentChildLink(options.parentId, document.id)
      }

      console.log(`文档创建成功: ${document.id} (${document.title})`)
      return document
    } catch (error) {
      console.error('创建文档失败:', error)
      throw new Error(`创建文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 复制文档
   * @param documentId 源文档ID
   * @param options 复制选项
   * @returns Promise<BaseDocument> 复制的文档
   */
  async duplicateDocument(
    documentId: string, 
    options: { title?: string; copyLinks?: boolean } = {}
  ): Promise<BaseDocument> {
    try {
      const sourceDocument = await documentDAO.getById(documentId)
      if (!sourceDocument) {
        throw new Error(`源文档不存在: ${documentId}`)
      }

      // 创建副本
      const duplicateData: Omit<BaseDocument, 'id' | 'createdAt' | 'updatedAt'> = {
        title: options.title || `${sourceDocument.title} - 副本`,
        type: sourceDocument.type,
        content: JSON.parse(JSON.stringify(sourceDocument.content)), // 深拷贝内容
        metadata: {
          ...sourceDocument.metadata,
          version: 1 // 重置版本号
        },
        tags: [...sourceDocument.tags],
        links: []
      }

      const duplicateDocument = await documentDAO.create(duplicateData)

      // 如果需要复制链接关系
      if (options.copyLinks) {
        await this.copyDocumentLinks(sourceDocument.id, duplicateDocument.id)
      }

      console.log(`文档复制成功: ${sourceDocument.id} -> ${duplicateDocument.id}`)
      return duplicateDocument
    } catch (error) {
      console.error('复制文档失败:', error)
      throw new Error(`复制文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 移动文档到回收站
   * @param documentId 文档ID
   * @returns Promise<void>
   */
  async moveToTrash(documentId: string): Promise<void> {
    try {
      await documentDAO.delete(documentId)
      console.log(`文档已移动到回收站: ${documentId}`)
    } catch (error) {
      console.error('移动文档到回收站失败:', error)
      throw new Error(`移动文档到回收站失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 从回收站恢复文档
   * @param documentId 文档ID
   * @returns Promise<void>
   */
  async restoreFromTrash(documentId: string): Promise<void> {
    try {
      await documentDAO.update(documentId, { 
        isDeleted: false,
        updatedAt: new Date()
      })
      console.log(`文档已从回收站恢复: ${documentId}`)
    } catch (error) {
      console.error('从回收站恢复文档失败:', error)
      throw new Error(`从回收站恢复文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 永久删除文档
   * @param documentId 文档ID
   * @returns Promise<void>
   */
  async permanentlyDelete(documentId: string): Promise<void> {
    try {
      // 删除所有相关链接
      await linkDAO.deleteDocumentLinks(documentId)
      
      // 永久删除文档
      await documentDAO.permanentDelete(documentId)
      
      console.log(`文档已永久删除: ${documentId}`)
    } catch (error) {
      console.error('永久删除文档失败:', error)
      throw new Error(`永久删除文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 根据ID获取文档
   * @param documentId 文档ID
   * @returns Promise<BaseDocument | null> 文档或null
   */
  async getDocumentById(documentId: string): Promise<BaseDocument | null> {
    try {
      const document = await documentDAO.getById(documentId)
      return document || null
    } catch (error) {
      console.error('获取文档失败:', error)
      return null
    }
  }

  /**
   * 更新文档
   * @param documentId 文档ID
   * @param updates 更新数据
   * @returns Promise<BaseDocument | null> 更新后的文档
   */
  async updateDocument(documentId: string, updates: Partial<BaseDocument>): Promise<BaseDocument | null> {
    try {
      const updatedDocument = await documentDAO.update(documentId, updates)
      if (updatedDocument) {
        console.log(`文档更新成功: ${documentId}`)
      }
      return updatedDocument || null
    } catch (error) {
      console.error('更新文档失败:', error)
      return null
    }
  }

  /**
   * 删除文档
   * @param documentId 文档ID
   * @returns Promise<boolean> 是否删除成功
   */
  async deleteDocument(documentId: string): Promise<boolean> {
    try {
      await this.moveToTrash(documentId)
      return true
    } catch (error) {
      console.error('删除文档失败:', error)
      return false
    }
  }

  /**
   * 根据类型获取文档
   * @param type 文档类型
   * @returns Promise<BaseDocument[]> 文档列表
   */
  async getDocumentsByType(type: DocumentType): Promise<BaseDocument[]> {
    try {
      const documents = await documentDAO.getByType(type)
      return documents
    } catch (error) {
      console.error('根据类型获取文档失败:', error)
      return []
    }
  }

  /**
   * 复制文档
   * @param documentId 文档ID
   * @returns Promise<BaseDocument | null> 复制的文档
   */
  async copyDocument(documentId: string): Promise<BaseDocument | null> {
    try {
      return await this.duplicateDocument(documentId, { copyLinks: true })
    } catch (error) {
      console.error('复制文档失败:', error)
      return null
    }
  }

  /**
   * 搜索文档
   * @param query 搜索查询
   * @param options 搜索选项
   * @returns Promise<BaseDocument[]> 搜索结果
   */
  async searchDocuments(query: string, options: SearchOptions = {}): Promise<BaseDocument[]> {
    try {
      // 如果查询为空，返回空数组
      if (!query || !query.trim()) {
        return []
      }

      const results = await documentDAO.search(query, options)
      console.log(`搜索完成: "${query}" 找到 ${results.length} 个结果`)
      return results
    } catch (error) {
      console.error('搜索文档失败:', error)
      throw new Error(`搜索文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取文档统计信息
   * @returns Promise<DocumentStats> 统计信息
   */
  async getDocumentStats(): Promise<DocumentStats> {
    try {
      const documents = await documentDAO.getAll()
      
      // 按类型统计
      const documentsByType: Record<DocumentType, number> = {
        [DocumentType.TEXT]: 0,
        [DocumentType.WHITEBOARD]: 0,
        [DocumentType.MINDMAP]: 0,
        [DocumentType.KANBAN]: 0
      }

      // 标签统计
      const tagCounts = new Map<string, number>()
      
      documents.forEach(doc => {
        documentsByType[doc.type]++
        
        doc.tags.forEach(tag => {
          tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1)
        })
      })

      // 最常用标签
      const mostUsedTags = Array.from(tagCounts.entries())
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)

      // 最近活动（简化版，基于更新时间）
      const recentActivity = documents
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
        .slice(0, 10)
        .map(doc => ({
          documentId: doc.id,
          title: doc.title,
          action: 'updated' as const,
          timestamp: doc.updatedAt
        }))

      return {
        totalDocuments: documents.length,
        documentsByType,
        totalTags: tagCounts.size,
        mostUsedTags,
        recentActivity
      }
    } catch (error) {
      console.error('获取文档统计失败:', error)
      throw new Error(`获取文档统计失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取回收站中的文档
   * @returns Promise<BaseDocument[]> 已删除的文档列表
   */
  async getTrashDocuments(): Promise<BaseDocument[]> {
    try {
      const allDocuments = await documentDAO.getAll(true)
      return allDocuments.filter(doc => doc.isDeleted)
    } catch (error) {
      console.error('获取回收站文档失败:', error)
      throw new Error(`获取回收站文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 清空回收站
   * @returns Promise<void>
   */
  async emptyTrash(): Promise<void> {
    try {
      const trashDocuments = await this.getTrashDocuments()
      
      for (const doc of trashDocuments) {
        await this.permanentlyDelete(doc.id)
      }
      
      console.log(`回收站已清空，删除了 ${trashDocuments.length} 个文档`)
    } catch (error) {
      console.error('清空回收站失败:', error)
      throw new Error(`清空回收站失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 根据文档类型创建内容
   */
  private createContentByType(type: DocumentType, content?: any): any {
    switch (type) {
      case DocumentType.TEXT:
        return {
          markdown: content?.markdown || '',
          outline: content?.outline || []
        } as TextContent
      
      case DocumentType.WHITEBOARD:
        return {
          canvas: content?.canvas || { width: 1920, height: 1080, backgroundColor: '#ffffff', zoom: 1 },
          objects: content?.objects || [],
          viewport: content?.viewport || { x: 0, y: 0, zoom: 1 },
          layers: content?.layers || [{ id: 'default', name: '默认图层', visible: true, locked: false, opacity: 1, order: 0 }]
        }
      
      case DocumentType.MINDMAP:
        return {
          nodes: content?.nodes || [],
          edges: content?.edges || [],
          layout: content?.layout || { type: 'tree', direction: 'TB', nodeSpacing: 50, levelSpacing: 100, alignment: 'center' },
          viewport: content?.viewport || { x: 0, y: 0, zoom: 1 }
        }
      
      case DocumentType.KANBAN:
        return {
          columns: content?.columns || [
            { id: 'todo', title: '待办', order: 0 },
            { id: 'doing', title: '进行中', order: 1 },
            { id: 'done', title: '已完成', order: 2 }
          ],
          cards: content?.cards || [],
          settings: content?.settings || {
            showCardCount: true,
            showColumnLimit: false,
            allowDragDrop: true,
            autoArchive: false,
            archiveDays: 30
          }
        }
      
      default:
        return content || {}
    }
  }

  /**
   * 生成默认标题
   */
  private generateDefaultTitle(type: DocumentType): string {
    const now = new Date()
    const dateStr = now.toLocaleDateString('zh-CN')
    const timeStr = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    
    const typeNames = {
      [DocumentType.TEXT]: '文本笔记',
      [DocumentType.WHITEBOARD]: '白板',
      [DocumentType.MINDMAP]: '思维导图',
      [DocumentType.KANBAN]: '看板'
    }
    
    return `${typeNames[type]} ${dateStr} ${timeStr}`
  }

  /**
   * 生成文档元数据
   */
  private generateMetadata(content: any): BaseDocument['metadata'] {
    let size = 0
    let wordCount = 0
    let readingTime = 0

    if (content && typeof content === 'object') {
      const contentStr = JSON.stringify(content)
      size = getStringByteLength(contentStr)
      
      // 对于文本内容，计算字数和阅读时间
      if (content.markdown) {
        wordCount = content.markdown.length
        readingTime = calculateReadingTime(content.markdown)
      }
    }

    return {
      version: 1,
      size,
      checksum: this.generateChecksum(JSON.stringify(content)),
      wordCount,
      readingTime
    }
  }

  /**
   * 生成内容校验和
   */
  private generateChecksum(content: string): string {
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash).toString(16)
  }

  /**
   * 创建父子关系链接
   */
  private async createParentChildLink(parentId: string, childId: string): Promise<void> {
    try {
      await linkDAO.create({
        sourceId: parentId,
        targetId: childId,
        type: LinkType.PARENT_CHILD,
        label: '父子关系'
      })
    } catch (error) {
      console.error('创建父子关系链接失败:', error)
    }
  }

  /**
   * 复制文档链接
   */
  private async copyDocumentLinks(sourceId: string, targetId: string): Promise<void> {
    try {
      const { outgoing } = await linkDAO.getDocumentLinks(sourceId)

      for (const link of outgoing) {
        await linkDAO.create({
          sourceId: targetId,
          targetId: link.targetId,
          type: link.type,
          label: link.label,
          metadata: link.metadata
        })
      }
    } catch (error) {
      console.error('复制文档链接失败:', error)
    }
  }

  /**
   * 获取所有标签
   * @returns Promise<string[]> 标签列表
   */
  async getAllTags(): Promise<string[]> {
    try {
      const allDocuments = await documentDAO.getAll()
      const tagSet = new Set<string>()

      // 收集所有文档的标签
      for (const doc of allDocuments) {
        if (doc.tags && doc.tags.length > 0) {
          doc.tags.forEach(tag => tagSet.add(tag))
        }
      }

      const tags = Array.from(tagSet).sort()
      console.log(`获取所有标签成功: ${tags.length} 个标签`)
      return tags
    } catch (error) {
      console.error('获取所有标签失败:', error)
      throw new Error(`获取所有标签失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取标签统计信息
   * @returns Promise<Array<{name: string, count: number}>> 标签统计
   */
  async getTagsWithCount(): Promise<Array<{name: string, count: number}>> {
    try {
      const allDocuments = await documentDAO.getAll()
      const tagCounts = new Map<string, number>()

      // 统计每个标签的使用次数
      for (const doc of allDocuments) {
        if (doc.tags && doc.tags.length > 0) {
          doc.tags.forEach(tag => {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1)
          })
        }
      }

      const tagsWithCount = Array.from(tagCounts.entries())
        .map(([name, count]) => ({ name, count }))
        .sort((a, b) => b.count - a.count) // 按使用次数降序排列

      console.log(`获取标签统计成功: ${tagsWithCount.length} 个标签`)
      return tagsWithCount
    } catch (error) {
      console.error('获取标签统计失败:', error)
      throw new Error(`获取标签统计失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 重命名标签
   * @param oldName 旧标签名
   * @param newName 新标签名
   * @returns Promise<number> 更新的文档数量
   */
  async renameTag(oldName: string, newName: string): Promise<number> {
    try {
      if (oldName === newName) {
        return 0
      }

      const allDocuments = await documentDAO.getAll()
      let updatedCount = 0

      for (const doc of allDocuments) {
        if (doc.tags && doc.tags.includes(oldName)) {
          const updatedTags = doc.tags.map(tag => tag === oldName ? newName : tag)
          await documentDAO.update(doc.id, { ...doc, tags: updatedTags })
          updatedCount++
        }
      }

      console.log(`标签重命名成功: "${oldName}" -> "${newName}", 更新了 ${updatedCount} 个文档`)
      return updatedCount
    } catch (error) {
      console.error('重命名标签失败:', error)
      throw new Error(`重命名标签失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 删除标签
   * @param tagName 标签名
   * @returns Promise<number> 更新的文档数量
   */
  async deleteTag(tagName: string): Promise<number> {
    try {
      const allDocuments = await documentDAO.getAll()
      let updatedCount = 0

      for (const doc of allDocuments) {
        if (doc.tags && doc.tags.includes(tagName)) {
          const updatedTags = doc.tags.filter(tag => tag !== tagName)
          await documentDAO.update(doc.id, { ...doc, tags: updatedTags })
          updatedCount++
        }
      }

      console.log(`标签删除成功: "${tagName}", 更新了 ${updatedCount} 个文档`)
      return updatedCount
    } catch (error) {
      console.error('删除标签失败:', error)
      throw new Error(`删除标签失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
}

/**
 * 导出文档管理器实例
 */
export const documentManager = DocumentManager.getInstance()
