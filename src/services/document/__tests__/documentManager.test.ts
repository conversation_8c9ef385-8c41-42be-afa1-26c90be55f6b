/**
 * DocumentManager 单元测试
 * 测试文档管理器的核心功能
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { documentManager } from '../documentManager'
import { DocumentType, BaseDocument } from '@/types'

// Mock IndexedDB
const mockDB = {
  documents: new Map<string, BaseDocument>(),
  clear() {
    this.documents.clear()
  },
  get(id: string) {
    return this.documents.get(id)
  },
  set(id: string, doc: BaseDocument) {
    this.documents.set(id, doc)
  },
  delete(id: string) {
    return this.documents.delete(id)
  },
  values() {
    return Array.from(this.documents.values())
  }
}

// Mock documentDAO
vi.mock('@services/database/documentDAO', () => ({
  documentDAO: {
    create: vi.fn((doc: Omit<BaseDocument, 'id' | 'createdAt' | 'updatedAt'>) => {
      const newDoc: BaseDocument = {
        ...doc,
        id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      mockDB.set(newDoc.id, newDoc)
      return Promise.resolve(newDoc)
    }),
    getById: vi.fn((id: string) => {
      const doc = mockDB.get(id)
      return Promise.resolve(doc || null)
    }),
    update: vi.fn((id: string, updates: Partial<BaseDocument>) => {
      const doc = mockDB.get(id)
      if (!doc) return Promise.resolve(null)
      
      const updatedDoc = {
        ...doc,
        ...updates,
        updatedAt: new Date()
      }
      mockDB.set(id, updatedDoc)
      return Promise.resolve(updatedDoc)
    }),
    delete: vi.fn((id: string) => {
      const deleted = mockDB.delete(id)
      return Promise.resolve(deleted)
    }),
    getAll: vi.fn(() => {
      return Promise.resolve(mockDB.values())
    }),
    search: vi.fn((query: string) => {
      const docs = mockDB.values()
      const filtered = docs.filter(doc => 
        doc.title.toLowerCase().includes(query.toLowerCase()) ||
        (doc.content?.markdown && doc.content.markdown.toLowerCase().includes(query.toLowerCase()))
      )
      return Promise.resolve(filtered)
    }),
    getByType: vi.fn((type: DocumentType) => {
      const docs = mockDB.values()
      const filtered = docs.filter(doc => doc.type === type)
      return Promise.resolve(filtered)
    })
  }
}))

describe('DocumentManager', () => {
  beforeEach(() => {
    mockDB.clear()
    vi.clearAllMocks()
  })

  afterEach(() => {
    mockDB.clear()
  })

  describe('createDocument', () => {
    it('应该成功创建文本文档', async () => {
      const docData = {
        title: '测试文档',
        type: DocumentType.TEXT,
        content: {
          markdown: '# 测试内容'
        },
        tags: ['测试']
      }

      const result = await documentManager.createDocument(docData)

      expect(result).toBeDefined()
      expect(result.title).toBe('测试文档')
      expect(result.type).toBe(DocumentType.TEXT)
      expect(result.id).toBeDefined()
      expect(result.createdAt).toBeInstanceOf(Date)
      expect(result.updatedAt).toBeInstanceOf(Date)
    })

    it('应该为空标题生成默认标题', async () => {
      const docData = {
        title: '',
        type: DocumentType.TEXT,
        content: { markdown: '' },
        tags: []
      }

      const result = await documentManager.createDocument(docData)

      expect(result.title).toBe('未命名文档')
    })

    it('应该正确设置文档元数据', async () => {
      const docData = {
        title: '测试文档',
        type: DocumentType.TEXT,
        content: {
          markdown: '这是一个测试文档，用于验证字数统计功能。'
        },
        tags: ['测试', '单元测试']
      }

      const result = await documentManager.createDocument(docData)

      expect(result.metadata).toBeDefined()
      expect(result.metadata.wordCount).toBeGreaterThan(0)
      expect(result.metadata.size).toBeGreaterThan(0)
      expect(result.metadata.version).toBe(1)
    })
  })

  describe('getDocumentById', () => {
    it('应该返回存在的文档', async () => {
      // 先创建一个文档
      const docData = {
        title: '测试文档',
        type: DocumentType.TEXT,
        content: { markdown: '测试内容' },
        tags: []
      }
      const created = await documentManager.createDocument(docData)

      // 获取文档
      const result = await documentManager.getDocumentById(created.id)

      expect(result).toBeDefined()
      expect(result?.id).toBe(created.id)
      expect(result?.title).toBe('测试文档')
    })

    it('应该对不存在的文档返回null', async () => {
      const result = await documentManager.getDocumentById('non-existent-id')
      expect(result).toBeNull()
    })
  })

  describe('updateDocument', () => {
    it('应该成功更新文档', async () => {
      // 先创建一个文档
      const docData = {
        title: '原始标题',
        type: DocumentType.TEXT,
        content: { markdown: '原始内容' },
        tags: ['原始']
      }
      const created = await documentManager.createDocument(docData)

      // 更新文档
      const updates = {
        title: '更新后的标题',
        content: { markdown: '更新后的内容' },
        tags: ['更新', '测试']
      }
      const result = await documentManager.updateDocument(created.id, updates)

      expect(result).toBeDefined()
      expect(result?.title).toBe('更新后的标题')
      expect(result?.content?.markdown).toBe('更新后的内容')
      expect(result?.tags).toEqual(['更新', '测试'])
      expect(result?.updatedAt).not.toEqual(created.updatedAt)
    })

    it('应该更新文档元数据', async () => {
      const docData = {
        title: '测试文档',
        type: DocumentType.TEXT,
        content: { markdown: '短内容' },
        tags: []
      }
      const created = await documentManager.createDocument(docData)

      const updates = {
        content: { markdown: '这是一个更长的内容，用于测试字数统计和元数据更新功能。' }
      }
      const result = await documentManager.updateDocument(created.id, updates)

      expect(result?.metadata.wordCount).toBeGreaterThan(created.metadata.wordCount || 0)
      expect(result?.metadata.version).toBe(2)
    })
  })

  describe('deleteDocument', () => {
    it('应该成功删除文档', async () => {
      // 先创建一个文档
      const docData = {
        title: '待删除文档',
        type: DocumentType.TEXT,
        content: { markdown: '内容' },
        tags: []
      }
      const created = await documentManager.createDocument(docData)

      // 删除文档
      const result = await documentManager.deleteDocument(created.id)
      expect(result).toBe(true)

      // 验证文档已被删除
      const deleted = await documentManager.getDocumentById(created.id)
      expect(deleted).toBeNull()
    })

    it('删除不存在的文档应该返回false', async () => {
      const result = await documentManager.deleteDocument('non-existent-id')
      expect(result).toBe(false)
    })
  })

  describe('searchDocuments', () => {
    beforeEach(async () => {
      // 创建测试文档
      await documentManager.createDocument({
        title: 'JavaScript 教程',
        type: DocumentType.TEXT,
        content: { markdown: '这是一个关于JavaScript的教程' },
        tags: ['编程', 'JavaScript']
      })

      await documentManager.createDocument({
        title: 'React 组件开发',
        type: DocumentType.TEXT,
        content: { markdown: '学习如何开发React组件' },
        tags: ['编程', 'React', 'JavaScript']
      })

      await documentManager.createDocument({
        title: '设计模式',
        type: DocumentType.TEXT,
        content: { markdown: '软件设计模式详解' },
        tags: ['设计', '模式']
      })
    })

    it('应该能够按标题搜索', async () => {
      const results = await documentManager.searchDocuments('JavaScript')
      expect(results).toHaveLength(1)
      expect(results[0].title).toBe('JavaScript 教程')
    })

    it('应该能够按内容搜索', async () => {
      const results = await documentManager.searchDocuments('React组件')
      expect(results).toHaveLength(1)
      expect(results[0].title).toBe('React 组件开发')
    })

    it('应该支持大小写不敏感搜索', async () => {
      const results = await documentManager.searchDocuments('javascript')
      expect(results).toHaveLength(1)
    })

    it('空查询应该返回空数组', async () => {
      const results = await documentManager.searchDocuments('')
      expect(results).toHaveLength(0)
    })
  })

  describe('getDocumentsByType', () => {
    beforeEach(async () => {
      await documentManager.createDocument({
        title: '文本文档1',
        type: DocumentType.TEXT,
        content: { markdown: '内容1' },
        tags: []
      })

      await documentManager.createDocument({
        title: '文本文档2',
        type: DocumentType.TEXT,
        content: { markdown: '内容2' },
        tags: []
      })

      await documentManager.createDocument({
        title: '白板文档',
        type: DocumentType.WHITEBOARD,
        content: { canvas: { width: 800, height: 600 } },
        tags: []
      })
    })

    it('应该返回指定类型的所有文档', async () => {
      const textDocs = await documentManager.getDocumentsByType(DocumentType.TEXT)
      expect(textDocs).toHaveLength(2)
      expect(textDocs.every(doc => doc.type === DocumentType.TEXT)).toBe(true)

      const whiteboardDocs = await documentManager.getDocumentsByType(DocumentType.WHITEBOARD)
      expect(whiteboardDocs).toHaveLength(1)
      expect(whiteboardDocs[0].type).toBe(DocumentType.WHITEBOARD)
    })

    it('不存在的类型应该返回空数组', async () => {
      const mindmapDocs = await documentManager.getDocumentsByType(DocumentType.MINDMAP)
      expect(mindmapDocs).toHaveLength(0)
    })
  })

  describe('copyDocument', () => {
    it('应该成功复制文档', async () => {
      const original = await documentManager.createDocument({
        title: '原始文档',
        type: DocumentType.TEXT,
        content: { markdown: '原始内容' },
        tags: ['原始', '测试']
      })

      const copied = await documentManager.copyDocument(original.id)

      expect(copied).toBeDefined()
      expect(copied?.id).not.toBe(original.id)
      expect(copied?.title).toBe('原始文档 (副本)')
      expect(copied?.content).toEqual(original.content)
      expect(copied?.tags).toEqual(original.tags)
      expect(copied?.type).toBe(original.type)
    })

    it('复制不存在的文档应该返回null', async () => {
      const result = await documentManager.copyDocument('non-existent-id')
      expect(result).toBeNull()
    })
  })

  describe('getDocumentStats', () => {
    beforeEach(async () => {
      await documentManager.createDocument({
        title: '文档1',
        type: DocumentType.TEXT,
        content: { markdown: '内容' },
        tags: ['标签1']
      })

      await documentManager.createDocument({
        title: '文档2',
        type: DocumentType.WHITEBOARD,
        content: { canvas: { width: 800, height: 600 } },
        tags: ['标签1', '标签2']
      })
    })

    it('应该返回正确的统计信息', async () => {
      const stats = await documentManager.getDocumentStats()

      expect(stats.totalDocuments).toBe(2)
      expect(stats.documentsByType[DocumentType.TEXT]).toBe(1)
      expect(stats.documentsByType[DocumentType.WHITEBOARD]).toBe(1)
      expect(stats.totalTags).toBe(2)
      expect(stats.tagUsage['标签1']).toBe(2)
      expect(stats.tagUsage['标签2']).toBe(1)
    })
  })
})
