/**
 * 搜索索引管理器
 * 提供搜索索引的创建、更新和查询功能，优化搜索性能
 */

import { BaseDocument, DocumentType, SearchOptions, SearchResult } from '@/types'
import { debounce } from '@utils/index'

/**
 * 搜索索引项接口
 */
interface SearchIndexItem {
  id: string
  title: string
  content: string
  tags: string[]
  type: DocumentType
  createdAt: Date
  updatedAt: Date
  wordCount: number
  size: number
  // 索引字段
  titleTokens: string[]
  contentTokens: string[]
  allTokens: string[]
}

/**
 * 搜索缓存项接口
 */
interface SearchCacheItem {
  query: string
  options: SearchOptions
  results: SearchResult[]
  timestamp: number
  hitCount: number
}

/**
 * 搜索统计接口
 */
interface SearchStats {
  totalQueries: number
  cacheHits: number
  averageResponseTime: number
  popularQueries: { query: string; count: number }[]
}

/**
 * 搜索索引管理器类
 */
class SearchIndexManager {
  private index: Map<string, SearchIndexItem> = new Map()
  private cache: Map<string, SearchCacheItem> = new Map()
  private stats: SearchStats = {
    totalQueries: 0,
    cacheHits: 0,
    averageResponseTime: 0,
    popularQueries: []
  }
  
  // 配置参数
  private readonly CACHE_SIZE = 100 // 最大缓存数量
  private readonly CACHE_TTL = 5 * 60 * 1000 // 缓存过期时间（5分钟）
  private readonly MIN_QUERY_LENGTH = 2 // 最小查询长度
  
  // 防抖的索引更新函数
  private debouncedIndexUpdate = debounce(this.rebuildIndex.bind(this), 1000)

  /**
   * 初始化索引
   */
  async initialize(documents: BaseDocument[]): Promise<void> {
    console.log('初始化搜索索引...')
    const startTime = Date.now()
    
    this.index.clear()
    
    for (const doc of documents) {
      await this.addToIndex(doc)
    }
    
    const endTime = Date.now()
    console.log(`搜索索引初始化完成，耗时 ${endTime - startTime}ms，索引了 ${documents.length} 个文档`)
  }

  /**
   * 添加文档到索引
   */
  async addToIndex(document: BaseDocument): Promise<void> {
    const content = this.extractTextContent(document)
    const titleTokens = this.tokenize(document.title)
    const contentTokens = this.tokenize(content)
    const allTokens = [...new Set([...titleTokens, ...contentTokens, ...document.tags])]

    const indexItem: SearchIndexItem = {
      id: document.id,
      title: document.title,
      content,
      tags: document.tags,
      type: document.type,
      createdAt: new Date(document.createdAt),
      updatedAt: new Date(document.updatedAt),
      wordCount: document.metadata.wordCount || 0,
      size: document.metadata.size || 0,
      titleTokens,
      contentTokens,
      allTokens
    }

    this.index.set(document.id, indexItem)
    
    // 清除相关缓存
    this.invalidateCache()
  }

  /**
   * 从索引中移除文档
   */
  removeFromIndex(documentId: string): void {
    this.index.delete(documentId)
    this.invalidateCache()
  }

  /**
   * 更新索引中的文档
   */
  async updateIndex(document: BaseDocument): Promise<void> {
    await this.addToIndex(document)
  }

  /**
   * 搜索文档
   */
  async search(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    const startTime = Date.now()
    this.stats.totalQueries++

    // 检查查询长度
    if (query.length < this.MIN_QUERY_LENGTH) {
      return []
    }

    // 检查缓存
    const cacheKey = this.getCacheKey(query, options)
    const cached = this.getFromCache(cacheKey)
    if (cached) {
      this.stats.cacheHits++
      return cached.results
    }

    // 执行搜索
    const results = await this.performSearch(query, options)
    
    // 缓存结果
    this.addToCache(cacheKey, query, options, results)
    
    // 更新统计
    const endTime = Date.now()
    const responseTime = endTime - startTime
    this.updateStats(query, responseTime)

    return results
  }

  /**
   * 执行实际搜索
   */
  private async performSearch(query: string, options: SearchOptions): Promise<SearchResult[]> {
    const queryTokens = this.tokenize(query.toLowerCase())
    const results: Array<{ item: SearchIndexItem; score: number; highlights: string[] }> = []

    for (const [id, item] of this.index) {
      // 应用过滤条件
      if (!this.matchesFilters(item, options)) {
        continue
      }

      // 计算匹配分数
      const scoreResult = this.calculateScore(item, queryTokens, query)
      if (scoreResult.score > 0) {
        results.push({
          item,
          score: scoreResult.score,
          highlights: scoreResult.highlights
        })
      }
    }

    // 排序结果
    results.sort((a, b) => b.score - a.score)

    // 应用分页
    const offset = options.offset || 0
    const limit = options.limit || 50
    const paginatedResults = results.slice(offset, offset + limit)

    // 转换为搜索结果格式
    return paginatedResults.map(result => ({
      id: result.item.id,
      title: result.item.title,
      type: result.item.type,
      tags: result.item.tags,
      createdAt: result.item.createdAt.toISOString(),
      updatedAt: result.item.updatedAt.toISOString(),
      metadata: {
        wordCount: result.item.wordCount,
        size: result.item.size,
        readingTime: Math.ceil(result.item.wordCount / 200)
      },
      score: result.score,
      highlights: result.highlights,
      matchedFields: this.getMatchedFields(result.item, queryTokens)
    }))
  }

  /**
   * 计算匹配分数
   */
  private calculateScore(
    item: SearchIndexItem, 
    queryTokens: string[], 
    originalQuery: string
  ): { score: number; highlights: string[] } {
    let score = 0
    const highlights: string[] = []
    const lowerQuery = originalQuery.toLowerCase()

    // 标题匹配（权重最高）
    const titleScore = this.calculateFieldScore(item.title.toLowerCase(), queryTokens, lowerQuery)
    score += titleScore * 3

    if (titleScore > 0) {
      highlights.push(this.extractHighlight(item.title, originalQuery))
    }

    // 内容匹配
    const contentScore = this.calculateFieldScore(item.content.toLowerCase(), queryTokens, lowerQuery)
    score += contentScore

    if (contentScore > 0) {
      const contentHighlights = this.extractContentHighlights(item.content, originalQuery, 3)
      highlights.push(...contentHighlights)
    }

    // 标签匹配（权重较高）
    const tagScore = this.calculateTagScore(item.tags, queryTokens, lowerQuery)
    score += tagScore * 2

    // 文档类型权重调整
    if (item.type === DocumentType.TEXT) {
      score *= 1.1 // 文本文档稍微提高权重
    }

    // 新文档权重调整
    const daysSinceUpdate = (Date.now() - item.updatedAt.getTime()) / (1000 * 60 * 60 * 24)
    if (daysSinceUpdate < 7) {
      score *= 1.05 // 最近更新的文档稍微提高权重
    }

    return { score, highlights }
  }

  /**
   * 计算字段匹配分数
   */
  private calculateFieldScore(fieldText: string, queryTokens: string[], originalQuery: string): number {
    let score = 0

    // 精确匹配
    if (fieldText.includes(originalQuery)) {
      score += 10
    }

    // 词汇匹配
    for (const token of queryTokens) {
      if (fieldText.includes(token)) {
        score += 1
      }
    }

    // 前缀匹配
    for (const token of queryTokens) {
      const words = fieldText.split(/\s+/)
      for (const word of words) {
        if (word.startsWith(token)) {
          score += 0.5
        }
      }
    }

    return score
  }

  /**
   * 计算标签匹配分数
   */
  private calculateTagScore(tags: string[], queryTokens: string[], originalQuery: string): number {
    let score = 0

    for (const tag of tags) {
      const lowerTag = tag.toLowerCase()
      
      // 精确匹配
      if (lowerTag === originalQuery) {
        score += 5
      }
      
      // 包含匹配
      if (lowerTag.includes(originalQuery)) {
        score += 3
      }

      // 词汇匹配
      for (const token of queryTokens) {
        if (lowerTag.includes(token)) {
          score += 1
        }
      }
    }

    return score
  }

  /**
   * 提取文本内容
   */
  private extractTextContent(document: BaseDocument): string {
    // 这里应该根据文档类型提取实际内容
    // 目前简化处理，实际应该解析不同格式的文档内容
    return document.content || ''
  }

  /**
   * 文本分词
   */
  private tokenize(text: string): string[] {
    // 简单的分词实现，实际项目中可能需要更复杂的分词算法
    return text
      .toLowerCase()
      .replace(/[^\w\u4e00-\u9fff\s]/g, ' ') // 保留中文、英文、数字
      .split(/\s+/)
      .filter(token => token.length > 0)
  }

  /**
   * 检查是否匹配过滤条件
   */
  private matchesFilters(item: SearchIndexItem, options: SearchOptions): boolean {
    // 文档类型过滤
    if (options.types && !options.types.includes(item.type)) {
      return false
    }

    // 标签过滤
    if (options.tags && options.tags.length > 0) {
      const hasMatchingTag = options.tags.some(tag => item.tags.includes(tag))
      if (!hasMatchingTag) {
        return false
      }
    }

    // 日期过滤
    if (options.createdAfter && item.createdAt < new Date(options.createdAfter)) {
      return false
    }
    if (options.createdBefore && item.createdAt > new Date(options.createdBefore)) {
      return false
    }

    return true
  }

  /**
   * 提取高亮片段
   */
  private extractHighlight(text: string, query: string, maxLength: number = 200): string {
    const lowerText = text.toLowerCase()
    const lowerQuery = query.toLowerCase()
    const index = lowerText.indexOf(lowerQuery)

    if (index === -1) return text.slice(0, maxLength)

    const start = Math.max(0, index - 50)
    const end = Math.min(text.length, index + query.length + 50)
    
    let highlight = text.slice(start, end)
    if (start > 0) highlight = '...' + highlight
    if (end < text.length) highlight = highlight + '...'

    return highlight
  }

  /**
   * 提取内容高亮片段
   */
  private extractContentHighlights(content: string, query: string, maxHighlights: number = 3): string[] {
    const highlights: string[] = []
    const lowerContent = content.toLowerCase()
    const lowerQuery = query.toLowerCase()
    
    let searchIndex = 0
    
    while (highlights.length < maxHighlights && searchIndex < content.length) {
      const index = lowerContent.indexOf(lowerQuery, searchIndex)
      if (index === -1) break

      const highlight = this.extractHighlight(content.slice(index), query, 150)
      highlights.push(highlight)
      
      searchIndex = index + query.length
    }

    return highlights
  }

  /**
   * 获取匹配的字段
   */
  private getMatchedFields(item: SearchIndexItem, queryTokens: string[]): string[] {
    const fields: string[] = []

    // 检查标题匹配
    if (queryTokens.some(token => item.title.toLowerCase().includes(token))) {
      fields.push('title')
    }

    // 检查内容匹配
    if (queryTokens.some(token => item.content.toLowerCase().includes(token))) {
      fields.push('content')
    }

    // 检查标签匹配
    if (queryTokens.some(token => 
      item.tags.some(tag => tag.toLowerCase().includes(token))
    )) {
      fields.push('tags')
    }

    return fields
  }

  /**
   * 生成缓存键
   */
  private getCacheKey(query: string, options: SearchOptions): string {
    return `${query}:${JSON.stringify(options)}`
  }

  /**
   * 从缓存获取结果
   */
  private getFromCache(cacheKey: string): SearchCacheItem | null {
    const cached = this.cache.get(cacheKey)
    if (!cached) return null

    // 检查是否过期
    if (Date.now() - cached.timestamp > this.CACHE_TTL) {
      this.cache.delete(cacheKey)
      return null
    }

    cached.hitCount++
    return cached
  }

  /**
   * 添加到缓存
   */
  private addToCache(
    cacheKey: string, 
    query: string, 
    options: SearchOptions, 
    results: SearchResult[]
  ): void {
    // 如果缓存已满，删除最少使用的项
    if (this.cache.size >= this.CACHE_SIZE) {
      const leastUsed = Array.from(this.cache.entries())
        .sort((a, b) => a[1].hitCount - b[1].hitCount)[0]
      this.cache.delete(leastUsed[0])
    }

    this.cache.set(cacheKey, {
      query,
      options,
      results,
      timestamp: Date.now(),
      hitCount: 0
    })
  }

  /**
   * 清除缓存
   */
  private invalidateCache(): void {
    this.cache.clear()
  }

  /**
   * 重建索引
   */
  private async rebuildIndex(): Promise<void> {
    // 这里应该重新从数据源加载所有文档并重建索引
    console.log('重建搜索索引...')
  }

  /**
   * 更新统计信息
   */
  private updateStats(query: string, responseTime: number): void {
    // 更新平均响应时间
    this.stats.averageResponseTime = 
      (this.stats.averageResponseTime * (this.stats.totalQueries - 1) + responseTime) / this.stats.totalQueries

    // 更新热门查询
    const existingQuery = this.stats.popularQueries.find(q => q.query === query)
    if (existingQuery) {
      existingQuery.count++
    } else {
      this.stats.popularQueries.push({ query, count: 1 })
    }

    // 保持热门查询列表大小
    this.stats.popularQueries.sort((a, b) => b.count - a.count)
    this.stats.popularQueries = this.stats.popularQueries.slice(0, 20)
  }

  /**
   * 获取搜索统计
   */
  getStats(): SearchStats {
    return { ...this.stats }
  }

  /**
   * 清除统计信息
   */
  clearStats(): void {
    this.stats = {
      totalQueries: 0,
      cacheHits: 0,
      averageResponseTime: 0,
      popularQueries: []
    }
  }
}

// 导出单例实例
export const searchIndexManager = new SearchIndexManager()
export default searchIndexManager
