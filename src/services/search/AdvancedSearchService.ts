/**
 * 高级搜索服务
 * 提供语义搜索、搜索结果排序、搜索历史和建议功能
 */

import { BaseDocument, DocumentType } from '@/types'
import { documentDAO } from '@services/database/documentDAO'
import { linkService } from '@services/link/linkService'

/**
 * 搜索结果接口
 */
export interface SearchResult {
  document: BaseDocument
  score: number
  highlights: string[]
  matchType: 'title' | 'content' | 'tags' | 'metadata'
  relevanceReason: string
}

/**
 * 搜索过滤器接口
 */
export interface SearchFilter {
  types?: DocumentType[]
  tags?: string[]
  dateRange?: {
    start?: Date
    end?: Date
  }
  hasLinks?: boolean
  minWordCount?: number
  maxWordCount?: number
}

/**
 * 搜索选项接口
 */
export interface SearchOptions {
  query: string
  filters?: SearchFilter
  sortBy?: 'relevance' | 'date' | 'title' | 'size'
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
  includeContent?: boolean
}

/**
 * 搜索历史记录接口
 */
export interface SearchHistory {
  id: string
  query: string
  filters?: SearchFilter
  timestamp: Date
  resultCount: number
}

/**
 * 搜索建议接口
 */
export interface SearchSuggestion {
  text: string
  type: 'query' | 'tag' | 'title'
  frequency: number
}

/**
 * 高级搜索服务类
 */
export class AdvancedSearchService {
  private static instance: AdvancedSearchService
  private searchHistory: SearchHistory[] = []
  private searchSuggestions: Map<string, SearchSuggestion> = new Map()

  /**
   * 获取实例
   */
  public static getInstance(): AdvancedSearchService {
    if (!AdvancedSearchService.instance) {
      AdvancedSearchService.instance = new AdvancedSearchService()
    }
    return AdvancedSearchService.instance
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    this.loadSearchHistory()
    this.loadSearchSuggestions()
  }

  /**
   * 执行高级搜索
   */
  async search(options: SearchOptions): Promise<{
    results: SearchResult[]
    total: number
    suggestions: SearchSuggestion[]
  }> {
    try {
      const { query, filters, sortBy = 'relevance', sortOrder = 'desc', limit = 20, offset = 0 } = options

      // 获取所有文档
      let documents = await documentDAO.getAll()

      // 应用过滤器
      if (filters) {
        documents = this.applyFilters(documents, filters)
      }

      // 执行搜索匹配
      const searchResults = await this.performSearch(documents, query, options.includeContent)

      // 排序结果
      const sortedResults = this.sortResults(searchResults, sortBy, sortOrder)

      // 分页
      const paginatedResults = sortedResults.slice(offset, offset + limit)

      // 记录搜索历史
      this.addToSearchHistory(query, filters, sortedResults.length)

      // 更新搜索建议
      this.updateSearchSuggestions(query)

      // 获取搜索建议
      const suggestions = this.getSearchSuggestions(query)

      return {
        results: paginatedResults,
        total: sortedResults.length,
        suggestions
      }
    } catch (error) {
      console.error('高级搜索失败:', error)
      throw new Error(`搜索失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 应用搜索过滤器
   */
  private applyFilters(documents: BaseDocument[], filters: SearchFilter): BaseDocument[] {
    return documents.filter(doc => {
      // 文档类型过滤
      if (filters.types && filters.types.length > 0) {
        if (!filters.types.includes(doc.type)) {
          return false
        }
      }

      // 标签过滤
      if (filters.tags && filters.tags.length > 0) {
        const hasMatchingTag = filters.tags.some(tag => 
          doc.tags.some(docTag => docTag.toLowerCase().includes(tag.toLowerCase()))
        )
        if (!hasMatchingTag) {
          return false
        }
      }

      // 日期范围过滤
      if (filters.dateRange) {
        if (filters.dateRange.start && doc.createdAt < filters.dateRange.start) {
          return false
        }
        if (filters.dateRange.end && doc.createdAt > filters.dateRange.end) {
          return false
        }
      }

      // 字数过滤
      const wordCount = this.getWordCount(doc)
      if (filters.minWordCount && wordCount < filters.minWordCount) {
        return false
      }
      if (filters.maxWordCount && wordCount > filters.maxWordCount) {
        return false
      }

      return true
    })
  }

  /**
   * 执行搜索匹配
   */
  private async performSearch(
    documents: BaseDocument[],
    query: string,
    includeContent = false
  ): Promise<SearchResult[]> {
    const results: SearchResult[] = []
    const queryTerms = this.tokenizeQuery(query)

    for (const doc of documents) {
      const searchResult = await this.scoreDocument(doc, queryTerms, includeContent)
      if (searchResult.score > 0) {
        results.push(searchResult)
      }
    }

    return results
  }

  /**
   * 为文档评分
   */
  private async scoreDocument(
    document: BaseDocument,
    queryTerms: string[],
    includeContent: boolean
  ): Promise<SearchResult> {
    let score = 0
    const highlights: string[] = []
    const matchTypes: Array<'title' | 'content' | 'tags' | 'metadata'> = []
    const reasons: string[] = []

    // 标题匹配（权重最高）
    const titleScore = this.calculateTextScore(document.title, queryTerms)
    if (titleScore > 0) {
      score += titleScore * 3
      matchTypes.push('title')
      highlights.push(this.highlightText(document.title, queryTerms))
      reasons.push('标题匹配')
    }

    // 标签匹配（权重较高）
    const tagScore = this.calculateTagScore(document.tags, queryTerms)
    if (tagScore > 0) {
      score += tagScore * 2
      matchTypes.push('tags')
      reasons.push('标签匹配')
    }

    // 内容匹配（如果启用）
    if (includeContent) {
      const contentScore = this.calculateContentScore(document, queryTerms)
      if (contentScore > 0) {
        score += contentScore
        matchTypes.push('content')
        reasons.push('内容匹配')
      }
    }

    // 元数据匹配
    const metadataScore = this.calculateMetadataScore(document, queryTerms)
    if (metadataScore > 0) {
      score += metadataScore * 0.5
      matchTypes.push('metadata')
      reasons.push('元数据匹配')
    }

    // 链接关系加分
    const linkBonus = await this.calculateLinkBonus(document, queryTerms)
    score += linkBonus

    return {
      document,
      score,
      highlights,
      matchType: matchTypes[0] || 'content',
      relevanceReason: reasons.join(', ') || '相关性匹配'
    }
  }

  /**
   * 计算文本匹配分数
   */
  private calculateTextScore(text: string, queryTerms: string[]): number {
    const lowerText = text.toLowerCase()
    let score = 0

    for (const term of queryTerms) {
      const lowerTerm = term.toLowerCase()
      
      // 完全匹配
      if (lowerText === lowerTerm) {
        score += 10
      }
      // 开头匹配
      else if (lowerText.startsWith(lowerTerm)) {
        score += 8
      }
      // 包含匹配
      else if (lowerText.includes(lowerTerm)) {
        score += 5
      }
      // 模糊匹配
      else if (this.fuzzyMatch(lowerText, lowerTerm)) {
        score += 2
      }
    }

    return score
  }

  /**
   * 计算标签匹配分数
   */
  private calculateTagScore(tags: string[], queryTerms: string[]): number {
    let score = 0

    for (const tag of tags) {
      for (const term of queryTerms) {
        if (tag.toLowerCase().includes(term.toLowerCase())) {
          score += 3
        }
      }
    }

    return score
  }

  /**
   * 计算内容匹配分数
   */
  private calculateContentScore(document: BaseDocument, queryTerms: string[]): number {
    // 这里需要根据不同文档类型提取内容
    let content = ''
    
    switch (document.type) {
      case DocumentType.TEXT:
        content = (document as any).content?.markdown || ''
        break
      case DocumentType.WHITEBOARD:
        // 从白板对象中提取文本
        content = this.extractWhiteboardText(document)
        break
      case DocumentType.MINDMAP:
        // 从思维导图节点中提取文本
        content = this.extractMindmapText(document)
        break
      case DocumentType.KANBAN:
        // 从看板卡片中提取文本
        content = this.extractKanbanText(document)
        break
    }

    return this.calculateTextScore(content, queryTerms) * 0.5
  }

  /**
   * 计算元数据匹配分数
   */
  private calculateMetadataScore(document: BaseDocument, queryTerms: string[]): number {
    let score = 0
    const metadata = document.metadata || {}

    for (const [key, value] of Object.entries(metadata)) {
      if (typeof value === 'string') {
        score += this.calculateTextScore(value, queryTerms) * 0.3
      }
    }

    return score
  }

  /**
   * 计算链接关系加分
   */
  private async calculateLinkBonus(document: BaseDocument, queryTerms: string[]): Promise<number> {
    try {
      const { outgoing, incoming } = await linkService.getLinkedDocuments(document.id)
      let bonus = 0

      // 检查链接的文档是否与查询相关
      for (const { document: linkedDoc } of [...outgoing, ...incoming]) {
        const linkedScore = this.calculateTextScore(linkedDoc.title, queryTerms)
        if (linkedScore > 0) {
          bonus += 1
        }
      }

      return bonus
    } catch (error) {
      return 0
    }
  }

  /**
   * 排序搜索结果
   */
  private sortResults(
    results: SearchResult[],
    sortBy: string,
    sortOrder: string
  ): SearchResult[] {
    return results.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'relevance':
          comparison = b.score - a.score
          break
        case 'date':
          comparison = b.document.updatedAt.getTime() - a.document.updatedAt.getTime()
          break
        case 'title':
          comparison = a.document.title.localeCompare(b.document.title)
          break
        case 'size':
          comparison = this.getWordCount(b.document) - this.getWordCount(a.document)
          break
        default:
          comparison = b.score - a.score
      }

      return sortOrder === 'asc' ? -comparison : comparison
    })
  }

  /**
   * 分词查询
   */
  private tokenizeQuery(query: string): string[] {
    return query
      .toLowerCase()
      .split(/\s+/)
      .filter(term => term.length > 0)
      .map(term => term.replace(/[^\w\u4e00-\u9fff]/g, ''))
      .filter(term => term.length > 0)
  }

  /**
   * 模糊匹配
   */
  private fuzzyMatch(text: string, pattern: string): boolean {
    const threshold = 0.8
    const distance = this.levenshteinDistance(text, pattern)
    const maxLength = Math.max(text.length, pattern.length)
    const similarity = 1 - distance / maxLength
    return similarity >= threshold
  }

  /**
   * 计算编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = []

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }

    return matrix[str2.length][str1.length]
  }

  /**
   * 高亮文本
   */
  private highlightText(text: string, queryTerms: string[]): string {
    let highlightedText = text

    for (const term of queryTerms) {
      const regex = new RegExp(`(${term})`, 'gi')
      highlightedText = highlightedText.replace(regex, '<mark>$1</mark>')
    }

    return highlightedText
  }

  /**
   * 获取文档字数
   */
  private getWordCount(document: BaseDocument): number {
    switch (document.type) {
      case DocumentType.TEXT:
        return (document as any).content?.markdown?.length || 0
      default:
        return document.title.length
    }
  }

  /**
   * 提取白板文本
   */
  private extractWhiteboardText(document: BaseDocument): string {
    // 从白板对象中提取文本内容
    const content = (document as any).content
    if (!content || !content.objects) return ''

    return content.objects
      .filter((obj: any) => obj.type === 'text' || obj.type === 'textbox')
      .map((obj: any) => obj.text || '')
      .join(' ')
  }

  /**
   * 提取思维导图文本
   */
  private extractMindmapText(document: BaseDocument): string {
    // 从思维导图节点中提取文本内容
    const content = (document as any).content
    if (!content || !content.nodes) return ''

    return content.nodes
      .map((node: any) => node.data?.label || '')
      .join(' ')
  }

  /**
   * 提取看板文本
   */
  private extractKanbanText(document: BaseDocument): string {
    // 从看板卡片中提取文本内容
    const content = (document as any).content
    if (!content || !content.cards) return ''

    return content.cards
      .map((card: any) => `${card.title} ${card.description || ''}`)
      .join(' ')
  }

  /**
   * 添加搜索历史
   */
  private addToSearchHistory(query: string, filters?: SearchFilter, resultCount = 0): void {
    const historyItem: SearchHistory = {
      id: Date.now().toString(),
      query,
      filters,
      timestamp: new Date(),
      resultCount
    }

    this.searchHistory.unshift(historyItem)
    
    // 限制历史记录数量
    if (this.searchHistory.length > 100) {
      this.searchHistory = this.searchHistory.slice(0, 100)
    }

    this.saveSearchHistory()
  }

  /**
   * 获取搜索历史
   */
  getSearchHistory(limit = 10): SearchHistory[] {
    return this.searchHistory.slice(0, limit)
  }

  /**
   * 清除搜索历史
   */
  clearSearchHistory(): void {
    this.searchHistory = []
    this.saveSearchHistory()
  }

  /**
   * 更新搜索建议
   */
  private updateSearchSuggestions(query: string): void {
    const terms = this.tokenizeQuery(query)
    
    for (const term of terms) {
      if (term.length < 2) continue

      const existing = this.searchSuggestions.get(term)
      if (existing) {
        existing.frequency++
      } else {
        this.searchSuggestions.set(term, {
          text: term,
          type: 'query',
          frequency: 1
        })
      }
    }

    this.saveSearchSuggestions()
  }

  /**
   * 获取搜索建议
   */
  private getSearchSuggestions(query: string, limit = 5): SearchSuggestion[] {
    const queryLower = query.toLowerCase()
    
    return Array.from(this.searchSuggestions.values())
      .filter(suggestion => 
        suggestion.text.toLowerCase().includes(queryLower) ||
        queryLower.includes(suggestion.text.toLowerCase())
      )
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, limit)
  }

  /**
   * 保存搜索历史
   */
  private saveSearchHistory(): void {
    try {
      localStorage.setItem('search_history', JSON.stringify(this.searchHistory))
    } catch (error) {
      console.error('保存搜索历史失败:', error)
    }
  }

  /**
   * 加载搜索历史
   */
  private loadSearchHistory(): void {
    try {
      const saved = localStorage.getItem('search_history')
      if (saved) {
        this.searchHistory = JSON.parse(saved).map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }))
      }
    } catch (error) {
      console.error('加载搜索历史失败:', error)
      this.searchHistory = []
    }
  }

  /**
   * 保存搜索建议
   */
  private saveSearchSuggestions(): void {
    try {
      const suggestions = Array.from(this.searchSuggestions.entries())
      localStorage.setItem('search_suggestions', JSON.stringify(suggestions))
    } catch (error) {
      console.error('保存搜索建议失败:', error)
    }
  }

  /**
   * 加载搜索建议
   */
  private loadSearchSuggestions(): void {
    try {
      const saved = localStorage.getItem('search_suggestions')
      if (saved) {
        const suggestions = JSON.parse(saved)
        this.searchSuggestions = new Map(suggestions)
      }
    } catch (error) {
      console.error('加载搜索建议失败:', error)
      this.searchSuggestions = new Map()
    }
  }
}

/**
 * 导出高级搜索服务实例
 */
export const advancedSearchService = AdvancedSearchService.getInstance()
