/**
 * 搜索索引管理器单元测试
 */

import { searchIndexManager } from '../searchIndexManager'
import { BaseDocument, DocumentType } from '@/types'

// Mock 文档数据
const mockDocuments: BaseDocument[] = [
  {
    id: '1',
    title: 'React 开发指南',
    content: '这是一个关于 React 开发的详细指南，包含了组件、状态管理、路由等内容。',
    type: DocumentType.TEXT,
    tags: ['React', '前端开发', '教程'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
    metadata: {
      wordCount: 500,
      size: 2048,
      readingTime: 3
    }
  },
  {
    id: '2',
    title: 'TypeScript 最佳实践',
    content: 'TypeScript 是 JavaScript 的超集，提供了静态类型检查功能。本文介绍了 TypeScript 的最佳实践。',
    type: DocumentType.TEXT,
    tags: ['TypeScript', '前端开发', '最佳实践'],
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-04T00:00:00Z',
    metadata: {
      wordCount: 800,
      size: 3072,
      readingTime: 4
    }
  },
  {
    id: '3',
    title: '项目管理白板',
    content: '项目管理流程图和思维导图',
    type: DocumentType.WHITEBOARD,
    tags: ['项目管理', '流程图'],
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-06T00:00:00Z',
    metadata: {
      wordCount: 100,
      size: 1024,
      readingTime: 1
    }
  }
]

describe('SearchIndexManager', () => {
  beforeEach(async () => {
    // 清除索引和缓存
    searchIndexManager['index'].clear()
    searchIndexManager['cache'].clear()
    searchIndexManager.clearStats()
    
    // 初始化测试数据
    await searchIndexManager.initialize(mockDocuments)
  })

  describe('索引初始化', () => {
    it('应该正确初始化索引', async () => {
      const indexSize = searchIndexManager['index'].size
      expect(indexSize).toBe(3)
    })

    it('应该为每个文档创建正确的索引项', () => {
      const indexItem = searchIndexManager['index'].get('1')
      expect(indexItem).toBeDefined()
      expect(indexItem?.title).toBe('React 开发指南')
      expect(indexItem?.titleTokens).toContain('react')
      expect(indexItem?.titleTokens).toContain('开发')
      expect(indexItem?.titleTokens).toContain('指南')
    })
  })

  describe('文档搜索', () => {
    it('应该能够搜索标题中的关键词', async () => {
      const results = await searchIndexManager.search('React')
      
      expect(results).toHaveLength(1)
      expect(results[0].id).toBe('1')
      expect(results[0].title).toBe('React 开发指南')
      expect(results[0].score).toBeGreaterThan(0)
    })

    it('应该能够搜索内容中的关键词', async () => {
      const results = await searchIndexManager.search('TypeScript')
      
      expect(results).toHaveLength(1)
      expect(results[0].id).toBe('2')
      expect(results[0].matchedFields).toContain('title')
    })

    it('应该能够搜索标签', async () => {
      const results = await searchIndexManager.search('前端开发')
      
      expect(results).toHaveLength(2)
      expect(results.map(r => r.id)).toContain('1')
      expect(results.map(r => r.id)).toContain('2')
    })

    it('应该按相关性排序结果', async () => {
      const results = await searchIndexManager.search('开发')
      
      expect(results).toHaveLength(2)
      // 标题匹配应该排在前面
      expect(results[0].id).toBe('1') // "React 开发指南" 标题匹配
    })

    it('应该支持中文搜索', async () => {
      const results = await searchIndexManager.search('指南')
      
      expect(results).toHaveLength(1)
      expect(results[0].id).toBe('1')
    })

    it('应该忽略大小写', async () => {
      const results1 = await searchIndexManager.search('react')
      const results2 = await searchIndexManager.search('REACT')
      
      expect(results1).toHaveLength(results2.length)
      expect(results1[0].id).toBe(results2[0].id)
    })

    it('应该处理空查询', async () => {
      const results = await searchIndexManager.search('')
      expect(results).toHaveLength(0)
    })

    it('应该处理短查询', async () => {
      const results = await searchIndexManager.search('a')
      expect(results).toHaveLength(0)
    })
  })

  describe('搜索过滤', () => {
    it('应该支持按文档类型过滤', async () => {
      const results = await searchIndexManager.search('项目', {
        types: [DocumentType.WHITEBOARD]
      })
      
      expect(results).toHaveLength(1)
      expect(results[0].type).toBe(DocumentType.WHITEBOARD)
    })

    it('应该支持按标签过滤', async () => {
      const results = await searchIndexManager.search('开发', {
        tags: ['React']
      })
      
      expect(results).toHaveLength(1)
      expect(results[0].tags).toContain('React')
    })

    it('应该支持按日期过滤', async () => {
      const results = await searchIndexManager.search('开发', {
        createdAfter: new Date('2024-01-02T00:00:00Z'),
        createdBefore: new Date('2024-01-04T00:00:00Z')
      })
      
      expect(results).toHaveLength(1)
      expect(results[0].id).toBe('2')
    })

    it('应该支持分页', async () => {
      const results1 = await searchIndexManager.search('开发', {
        limit: 1,
        offset: 0
      })
      
      const results2 = await searchIndexManager.search('开发', {
        limit: 1,
        offset: 1
      })
      
      expect(results1).toHaveLength(1)
      expect(results2).toHaveLength(1)
      expect(results1[0].id).not.toBe(results2[0].id)
    })
  })

  describe('搜索缓存', () => {
    it('应该缓存搜索结果', async () => {
      // 第一次搜索
      const results1 = await searchIndexManager.search('React')
      
      // 第二次搜索应该从缓存返回
      const results2 = await searchIndexManager.search('React')
      
      expect(results1).toEqual(results2)
      
      const stats = searchIndexManager.getStats()
      expect(stats.cacheHits).toBe(1)
    })

    it('应该为不同的查询条件使用不同的缓存', async () => {
      await searchIndexManager.search('React')
      await searchIndexManager.search('React', { types: [DocumentType.TEXT] })
      
      const stats = searchIndexManager.getStats()
      expect(stats.totalQueries).toBe(2)
      expect(stats.cacheHits).toBe(0)
    })
  })

  describe('索引更新', () => {
    it('应该能够添加新文档到索引', async () => {
      const newDoc: BaseDocument = {
        id: '4',
        title: 'Vue.js 入门',
        content: 'Vue.js 是一个渐进式 JavaScript 框架',
        type: DocumentType.TEXT,
        tags: ['Vue.js', '前端开发'],
        createdAt: '2024-01-07T00:00:00Z',
        updatedAt: '2024-01-07T00:00:00Z',
        metadata: {
          wordCount: 300,
          size: 1536,
          readingTime: 2
        }
      }

      await searchIndexManager.addToIndex(newDoc)
      
      const results = await searchIndexManager.search('Vue')
      expect(results).toHaveLength(1)
      expect(results[0].id).toBe('4')
    })

    it('应该能够从索引中移除文档', () => {
      searchIndexManager.removeFromIndex('1')
      
      const indexSize = searchIndexManager['index'].size
      expect(indexSize).toBe(2)
    })

    it('应该能够更新索引中的文档', async () => {
      const updatedDoc: BaseDocument = {
        ...mockDocuments[0],
        title: '更新的 React 指南',
        updatedAt: '2024-01-08T00:00:00Z'
      }

      await searchIndexManager.updateIndex(updatedDoc)
      
      const results = await searchIndexManager.search('更新')
      expect(results).toHaveLength(1)
      expect(results[0].title).toBe('更新的 React 指南')
    })

    it('应该在索引更新后清除缓存', async () => {
      // 先搜索并缓存结果
      await searchIndexManager.search('React')
      
      // 更新索引
      const updatedDoc: BaseDocument = {
        ...mockDocuments[0],
        title: '新的 React 指南'
      }
      await searchIndexManager.updateIndex(updatedDoc)
      
      // 再次搜索应该不会命中缓存
      await searchIndexManager.search('React')
      
      const stats = searchIndexManager.getStats()
      expect(stats.cacheHits).toBe(0)
    })
  })

  describe('搜索统计', () => {
    it('应该正确统计搜索次数', async () => {
      await searchIndexManager.search('React')
      await searchIndexManager.search('TypeScript')
      
      const stats = searchIndexManager.getStats()
      expect(stats.totalQueries).toBe(2)
    })

    it('应该统计缓存命中次数', async () => {
      await searchIndexManager.search('React')
      await searchIndexManager.search('React') // 缓存命中
      
      const stats = searchIndexManager.getStats()
      expect(stats.cacheHits).toBe(1)
    })

    it('应该记录热门查询', async () => {
      await searchIndexManager.search('React')
      await searchIndexManager.search('React')
      await searchIndexManager.search('TypeScript')
      
      const stats = searchIndexManager.getStats()
      expect(stats.popularQueries).toHaveLength(2)
      expect(stats.popularQueries[0].query).toBe('React')
      expect(stats.popularQueries[0].count).toBe(2)
    })

    it('应该能够清除统计信息', async () => {
      await searchIndexManager.search('React')
      searchIndexManager.clearStats()
      
      const stats = searchIndexManager.getStats()
      expect(stats.totalQueries).toBe(0)
      expect(stats.cacheHits).toBe(0)
      expect(stats.popularQueries).toHaveLength(0)
    })
  })

  describe('高亮功能', () => {
    it('应该返回高亮片段', async () => {
      const results = await searchIndexManager.search('React')
      
      expect(results[0].highlights).toBeDefined()
      expect(results[0].highlights!.length).toBeGreaterThan(0)
    })

    it('应该标识匹配的字段', async () => {
      const results = await searchIndexManager.search('React')
      
      expect(results[0].matchedFields).toBeDefined()
      expect(results[0].matchedFields).toContain('title')
    })
  })
})
