/**
 * 链接服务
 * 提供双向链接、反向链接等关联系统功能
 */

import { linkDAO } from '@services/database/linkDAO'
import { documentDAO } from '@services/database/documentDAO'
import { DocumentLink, LinkType, BaseDocument } from '../../types/index'
import { generateUUID } from '@utils/index'

/**
 * 链接建议接口
 */
export interface LinkSuggestion {
  document: BaseDocument
  score: number
  reason: string
  type: LinkType
}

/**
 * 链接统计接口
 */
export interface LinkStats {
  totalLinks: number
  linksByType: Record<LinkType, number>
  mostLinkedDocuments: Array<{
    document: BaseDocument
    linkCount: number
  }>
  orphanDocuments: BaseDocument[]
}

/**
 * 链接服务类
 */
export class LinkService {
  private static instance: LinkService

  /**
   * 获取链接服务实例
   */
  public static getInstance(): LinkService {
    if (!LinkService.instance) {
      LinkService.instance = new LinkService()
    }
    return LinkService.instance
  }

  /**
   * 私有构造函数
   */
  private constructor() {}

  /**
   * 创建双向链接
   * @param sourceId 源文档ID
   * @param targetId 目标文档ID
   * @param type 链接类型
   * @param label 链接标签
   * @returns Promise<DocumentLink> 创建的链接
   */
  async createBidirectionalLink(
    sourceId: string,
    targetId: string,
    type: LinkType = LinkType.REFERENCE,
    label?: string
  ): Promise<DocumentLink> {
    try {
      // 验证文档存在
      const [sourceDoc, targetDoc] = await Promise.all([
        documentDAO.getById(sourceId),
        documentDAO.getById(targetId)
      ])

      if (!sourceDoc) {
        throw new Error(`源文档不存在: ${sourceId}`)
      }
      if (!targetDoc) {
        throw new Error(`目标文档不存在: ${targetId}`)
      }

      // 创建正向链接
      const forwardLink = await linkDAO.create({
        sourceId,
        targetId,
        type,
        label: label || `链接到 ${targetDoc.title}`
      })

      // 如果不是父子关系，创建反向链接
      if (type !== LinkType.PARENT_CHILD) {
        await linkDAO.create({
          sourceId: targetId,
          targetId: sourceId,
          type,
          label: `来自 ${sourceDoc.title}`
        })
      }

      console.log(`双向链接创建成功: ${sourceId} <-> ${targetId}`)
      return forwardLink
    } catch (error) {
      console.error('创建双向链接失败:', error)
      throw new Error(`创建双向链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 删除双向链接
   * @param sourceId 源文档ID
   * @param targetId 目标文档ID
   * @param type 链接类型
   * @returns Promise<void>
   */
  async removeBidirectionalLink(
    sourceId: string,
    targetId: string,
    type: LinkType
  ): Promise<void> {
    try {
      const [outgoingLinks, incomingLinks] = await Promise.all([
        linkDAO.getOutgoingLinks(sourceId),
        linkDAO.getIncomingLinks(sourceId)
      ])

      // 找到并删除正向链接
      const forwardLink = outgoingLinks.find(
        link => link.targetId === targetId && link.type === type
      )
      if (forwardLink) {
        await linkDAO.delete(forwardLink.id)
      }

      // 找到并删除反向链接（如果不是父子关系）
      if (type !== LinkType.PARENT_CHILD) {
        const backwardLink = incomingLinks.find(
          link => link.sourceId === targetId && link.type === type
        )
        if (backwardLink) {
          await linkDAO.delete(backwardLink.id)
        }
      }

      console.log(`双向链接删除成功: ${sourceId} <-> ${targetId}`)
    } catch (error) {
      console.error('删除双向链接失败:', error)
      throw new Error(`删除双向链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取文档的所有关联文档
   * @param documentId 文档ID
   * @returns Promise<{outgoing: BaseDocument[], incoming: BaseDocument[]}> 关联文档
   */
  async getLinkedDocuments(documentId: string): Promise<{
    outgoing: Array<{ document: BaseDocument; link: DocumentLink }>
    incoming: Array<{ document: BaseDocument; link: DocumentLink }>
  }> {
    try {
      const { outgoing, incoming } = await linkDAO.getDocumentLinks(documentId)

      // 获取关联的文档信息
      const outgoingDocs = await Promise.all(
        outgoing.map(async (link) => {
          const document = await documentDAO.getById(link.targetId)
          return document ? { document, link } : null
        })
      )

      const incomingDocs = await Promise.all(
        incoming.map(async (link) => {
          const document = await documentDAO.getById(link.sourceId)
          return document ? { document, link } : null
        })
      )

      return {
        outgoing: outgoingDocs.filter(Boolean) as Array<{ document: BaseDocument; link: DocumentLink }>,
        incoming: incomingDocs.filter(Boolean) as Array<{ document: BaseDocument; link: DocumentLink }>
      }
    } catch (error) {
      console.error('获取关联文档失败:', error)
      throw new Error(`获取关联文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取链接建议
   * @param documentId 文档ID
   * @param limit 建议数量限制
   * @returns Promise<LinkSuggestion[]> 链接建议列表
   */
  async getLinkSuggestions(documentId: string, limit = 10): Promise<LinkSuggestion[]> {
    try {
      const currentDoc = await documentDAO.getById(documentId)
      if (!currentDoc) {
        throw new Error(`文档不存在: ${documentId}`)
      }

      const allDocuments = await documentDAO.getAll()
      const { outgoing, incoming } = await linkDAO.getDocumentLinks(documentId)
      
      // 已链接的文档ID集合
      const linkedDocIds = new Set([
        ...outgoing.map(link => link.targetId),
        ...incoming.map(link => link.sourceId),
        documentId // 排除自己
      ])

      const suggestions: LinkSuggestion[] = []

      for (const doc of allDocuments) {
        if (linkedDocIds.has(doc.id)) continue

        const score = this.calculateLinkScore(currentDoc, doc)
        if (score > 0) {
          suggestions.push({
            document: doc,
            score,
            reason: this.getLinkReason(currentDoc, doc, score),
            type: this.suggestLinkType(currentDoc, doc)
          })
        }
      }

      // 按分数排序并限制数量
      return suggestions
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)
    } catch (error) {
      console.error('获取链接建议失败:', error)
      throw new Error(`获取链接建议失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取链接统计信息
   * @returns Promise<LinkStats> 链接统计
   */
  async getLinkStats(): Promise<LinkStats> {
    try {
      const [allLinks, allDocuments] = await Promise.all([
        linkDAO.getAll(),
        documentDAO.getAll()
      ])

      // 按类型统计链接
      const linksByType: Record<LinkType, number> = {
        [LinkType.REFERENCE]: 0,
        [LinkType.EMBED]: 0,
        [LinkType.RELATED]: 0,
        [LinkType.PARENT_CHILD]: 0
      }

      allLinks.forEach(link => {
        linksByType[link.type]++
      })

      // 统计每个文档的链接数量
      const documentLinkCounts = new Map<string, number>()
      allLinks.forEach(link => {
        documentLinkCounts.set(
          link.sourceId,
          (documentLinkCounts.get(link.sourceId) || 0) + 1
        )
        documentLinkCounts.set(
          link.targetId,
          (documentLinkCounts.get(link.targetId) || 0) + 1
        )
      })

      // 最多链接的文档
      const mostLinkedDocuments = Array.from(documentLinkCounts.entries())
        .map(([docId, count]) => ({
          docId,
          count
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)

      const mostLinkedDocsWithInfo = await Promise.all(
        mostLinkedDocuments.map(async ({ docId, count }) => {
          const document = await documentDAO.getById(docId)
          return document ? { document, linkCount: count } : null
        })
      )

      // 孤立文档（没有任何链接的文档）
      const orphanDocuments = allDocuments.filter(
        doc => !documentLinkCounts.has(doc.id)
      )

      return {
        totalLinks: allLinks.length,
        linksByType,
        mostLinkedDocuments: mostLinkedDocsWithInfo.filter(Boolean) as Array<{
          document: BaseDocument
          linkCount: number
        }>,
        orphanDocuments
      }
    } catch (error) {
      console.error('获取链接统计失败:', error)
      throw new Error(`获取链接统计失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 计算链接分数
   */
  private calculateLinkScore(doc1: BaseDocument, doc2: BaseDocument): number {
    let score = 0

    // 相同类型的文档更容易关联
    if (doc1.type === doc2.type) {
      score += 20
    }

    // 共同标签
    const commonTags = doc1.tags.filter(tag => doc2.tags.includes(tag))
    score += commonTags.length * 15

    // 标题相似性（简单的关键词匹配）
    const title1Words = doc1.title.toLowerCase().split(/\s+/)
    const title2Words = doc2.title.toLowerCase().split(/\s+/)
    const commonWords = title1Words.filter(word => 
      word.length > 2 && title2Words.includes(word)
    )
    score += commonWords.length * 10

    // 时间相近性（创建时间相近的文档更可能相关）
    const timeDiff = Math.abs(doc1.createdAt.getTime() - doc2.createdAt.getTime())
    const daysDiff = timeDiff / (1000 * 60 * 60 * 24)
    if (daysDiff < 7) {
      score += 10 - daysDiff
    }

    return Math.round(score)
  }

  /**
   * 获取链接原因
   */
  private getLinkReason(doc1: BaseDocument, doc2: BaseDocument, score: number): string {
    const reasons = []

    if (doc1.type === doc2.type) {
      reasons.push('相同类型')
    }

    const commonTags = doc1.tags.filter(tag => doc2.tags.includes(tag))
    if (commonTags.length > 0) {
      reasons.push(`共同标签: ${commonTags.slice(0, 3).join(', ')}`)
    }

    const title1Words = doc1.title.toLowerCase().split(/\s+/)
    const title2Words = doc2.title.toLowerCase().split(/\s+/)
    const commonWords = title1Words.filter(word => 
      word.length > 2 && title2Words.includes(word)
    )
    if (commonWords.length > 0) {
      reasons.push(`相似标题`)
    }

    const timeDiff = Math.abs(doc1.createdAt.getTime() - doc2.createdAt.getTime())
    const daysDiff = timeDiff / (1000 * 60 * 60 * 24)
    if (daysDiff < 7) {
      reasons.push('创建时间相近')
    }

    return reasons.length > 0 ? reasons.join(', ') : '可能相关'
  }

  /**
   * 建议链接类型
   */
  private suggestLinkType(doc1: BaseDocument, doc2: BaseDocument): LinkType {
    // 根据文档类型和内容建议链接类型
    if (doc1.type === doc2.type) {
      return LinkType.RELATED
    }

    // 如果一个是文本，一个是其他类型，可能是引用关系
    if (doc1.type === 'text' || doc2.type === 'text') {
      return LinkType.REFERENCE
    }

    return LinkType.RELATED
  }

  /**
   * 获取所有链接
   * @returns Promise<DocumentLink[]> 所有链接列表
   */
  async getAllLinks(): Promise<DocumentLink[]> {
    try {
      return await linkDAO.getAll()
    } catch (error) {
      console.error('获取所有链接失败:', error)
      throw new Error(`获取所有链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 更新链接
   * @param linkId 链接ID
   * @param updates 更新数据
   * @returns Promise<DocumentLink> 更新后的链接
   */
  async updateLink(linkId: string, updates: Partial<DocumentLink>): Promise<DocumentLink> {
    try {
      const existingLink = await linkDAO.getById(linkId)
      if (!existingLink) {
        throw new Error(`链接不存在: ${linkId}`)
      }

      const updatedLink = await linkDAO.update(linkId, {
        ...existingLink,
        ...updates,
        updatedAt: new Date()
      })

      console.log(`链接更新成功: ${linkId}`)
      return updatedLink
    } catch (error) {
      console.error('更新链接失败:', error)
      throw new Error(`更新链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 删除链接
   * @param linkId 链接ID
   * @returns Promise<void>
   */
  async deleteLink(linkId: string): Promise<void> {
    try {
      await linkDAO.delete(linkId)
      console.log(`链接删除成功: ${linkId}`)
    } catch (error) {
      console.error('删除链接失败:', error)
      throw new Error(`删除链接失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
}

/**
 * 导出链接服务实例
 */
export const linkService = LinkService.getInstance()
