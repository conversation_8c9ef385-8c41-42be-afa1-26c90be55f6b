/**
 * LinkService 单元测试
 * 测试链接服务的核心功能
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { linkService } from '../linkService'
import { DocumentLink, LinkType } from '@/types'

// Mock数据存储
const mockLinks = new Map<string, DocumentLink>()

// Mock linkDAO
vi.mock('@services/database/linkDAO', () => ({
  linkDAO: {
    create: vi.fn((link: Omit<DocumentLink, 'id' | 'createdAt' | 'updatedAt'>) => {
      const newLink: DocumentLink = {
        ...link,
        id: `link_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      mockLinks.set(newLink.id, newLink)
      return Promise.resolve(newLink)
    }),
    getById: vi.fn((id: string) => {
      const link = mockLinks.get(id)
      return Promise.resolve(link || null)
    }),
    update: vi.fn((id: string, updates: Partial<DocumentLink>) => {
      const link = mockLinks.get(id)
      if (!link) return Promise.resolve(null)
      
      const updatedLink = {
        ...link,
        ...updates,
        updatedAt: new Date()
      }
      mockLinks.set(id, updatedLink)
      return Promise.resolve(updatedLink)
    }),
    delete: vi.fn((id: string) => {
      const deleted = mockLinks.delete(id)
      return Promise.resolve(deleted)
    }),
    getAll: vi.fn(() => {
      return Promise.resolve(Array.from(mockLinks.values()))
    }),
    getBySourceId: vi.fn((sourceId: string) => {
      const links = Array.from(mockLinks.values())
      return Promise.resolve(links.filter(link => link.sourceId === sourceId))
    }),
    getByTargetId: vi.fn((targetId: string) => {
      const links = Array.from(mockLinks.values())
      return Promise.resolve(links.filter(link => link.targetId === targetId))
    }),
    getByDocumentId: vi.fn((documentId: string) => {
      const links = Array.from(mockLinks.values())
      return Promise.resolve(links.filter(link => 
        link.sourceId === documentId || link.targetId === documentId
      ))
    })
  }
}))

describe('LinkService', () => {
  beforeEach(() => {
    mockLinks.clear()
    vi.clearAllMocks()
  })

  afterEach(() => {
    mockLinks.clear()
  })

  describe('createLink', () => {
    it('应该成功创建链接', async () => {
      const linkData = {
        sourceId: 'doc1',
        targetId: 'doc2',
        type: LinkType.REFERENCE,
        label: '参考链接'
      }

      const result = await linkService.createLink(linkData)

      expect(result).toBeDefined()
      expect(result.sourceId).toBe('doc1')
      expect(result.targetId).toBe('doc2')
      expect(result.type).toBe(LinkType.REFERENCE)
      expect(result.label).toBe('参考链接')
      expect(result.id).toBeDefined()
      expect(result.createdAt).toBeInstanceOf(Date)
    })

    it('应该防止创建重复链接', async () => {
      const linkData = {
        sourceId: 'doc1',
        targetId: 'doc2',
        type: LinkType.REFERENCE
      }

      // 创建第一个链接
      await linkService.createLink(linkData)

      // 尝试创建重复链接
      await expect(linkService.createLink(linkData)).rejects.toThrow('链接已存在')
    })

    it('应该防止创建自引用链接', async () => {
      const linkData = {
        sourceId: 'doc1',
        targetId: 'doc1',
        type: LinkType.REFERENCE
      }

      await expect(linkService.createLink(linkData)).rejects.toThrow('不能创建自引用链接')
    })
  })

  describe('getLinkById', () => {
    it('应该返回存在的链接', async () => {
      const linkData = {
        sourceId: 'doc1',
        targetId: 'doc2',
        type: LinkType.REFERENCE,
        label: '测试链接'
      }
      const created = await linkService.createLink(linkData)

      const result = await linkService.getLinkById(created.id)

      expect(result).toBeDefined()
      expect(result?.id).toBe(created.id)
      expect(result?.label).toBe('测试链接')
    })

    it('应该对不存在的链接返回null', async () => {
      const result = await linkService.getLinkById('non-existent-id')
      expect(result).toBeNull()
    })
  })

  describe('updateLink', () => {
    it('应该成功更新链接', async () => {
      const linkData = {
        sourceId: 'doc1',
        targetId: 'doc2',
        type: LinkType.REFERENCE,
        label: '原始标签'
      }
      const created = await linkService.createLink(linkData)

      const updates = {
        label: '更新后的标签',
        type: LinkType.RELATED
      }
      const result = await linkService.updateLink(created.id, updates)

      expect(result).toBeDefined()
      expect(result?.label).toBe('更新后的标签')
      expect(result?.type).toBe(LinkType.RELATED)
      expect(result?.updatedAt).not.toEqual(created.updatedAt)
    })

    it('更新不存在的链接应该返回null', async () => {
      const result = await linkService.updateLink('non-existent-id', { label: '新标签' })
      expect(result).toBeNull()
    })
  })

  describe('deleteLink', () => {
    it('应该成功删除链接', async () => {
      const linkData = {
        sourceId: 'doc1',
        targetId: 'doc2',
        type: LinkType.REFERENCE
      }
      const created = await linkService.createLink(linkData)

      const result = await linkService.deleteLink(created.id)
      expect(result).toBe(true)

      const deleted = await linkService.getLinkById(created.id)
      expect(deleted).toBeNull()
    })

    it('删除不存在的链接应该返回false', async () => {
      const result = await linkService.deleteLink('non-existent-id')
      expect(result).toBe(false)
    })
  })

  describe('getLinksForDocument', () => {
    beforeEach(async () => {
      // 创建测试链接
      await linkService.createLink({
        sourceId: 'doc1',
        targetId: 'doc2',
        type: LinkType.REFERENCE,
        label: '链接1'
      })

      await linkService.createLink({
        sourceId: 'doc2',
        targetId: 'doc3',
        type: LinkType.RELATED,
        label: '链接2'
      })

      await linkService.createLink({
        sourceId: 'doc3',
        targetId: 'doc1',
        type: LinkType.EMBED,
        label: '链接3'
      })
    })

    it('应该返回文档的所有相关链接', async () => {
      const links = await linkService.getLinksForDocument('doc1')
      expect(links).toHaveLength(2) // doc1作为源和目标各一个
    })

    it('应该正确区分出站和入站链接', async () => {
      const outgoingLinks = await linkService.getOutgoingLinks('doc1')
      const incomingLinks = await linkService.getIncomingLinks('doc1')

      expect(outgoingLinks).toHaveLength(1)
      expect(outgoingLinks[0].sourceId).toBe('doc1')
      
      expect(incomingLinks).toHaveLength(1)
      expect(incomingLinks[0].targetId).toBe('doc1')
    })
  })

  describe('getBacklinks', () => {
    beforeEach(async () => {
      // 创建反向链接测试数据
      await linkService.createLink({
        sourceId: 'doc1',
        targetId: 'target',
        type: LinkType.REFERENCE,
        label: '引用'
      })

      await linkService.createLink({
        sourceId: 'doc2',
        targetId: 'target',
        type: LinkType.RELATED,
        label: '相关'
      })

      await linkService.createLink({
        sourceId: 'doc3',
        targetId: 'target',
        type: LinkType.EMBED,
        label: '嵌入'
      })
    })

    it('应该返回所有指向目标文档的链接', async () => {
      const backlinks = await linkService.getBacklinks('target')
      expect(backlinks).toHaveLength(3)
      expect(backlinks.every(link => link.targetId === 'target')).toBe(true)
    })

    it('应该按类型筛选反向链接', async () => {
      const referenceBacklinks = await linkService.getBacklinks('target', LinkType.REFERENCE)
      expect(referenceBacklinks).toHaveLength(1)
      expect(referenceBacklinks[0].type).toBe(LinkType.REFERENCE)
    })
  })

  describe('findOrphanDocuments', () => {
    it('应该识别孤立文档', async () => {
      // 创建一些链接
      await linkService.createLink({
        sourceId: 'doc1',
        targetId: 'doc2',
        type: LinkType.REFERENCE
      })

      await linkService.createLink({
        sourceId: 'doc2',
        targetId: 'doc3',
        type: LinkType.RELATED
      })

      const allDocumentIds = ['doc1', 'doc2', 'doc3', 'doc4', 'doc5']
      const orphans = await linkService.findOrphanDocuments(allDocumentIds)

      expect(orphans).toEqual(['doc4', 'doc5'])
    })

    it('没有孤立文档时应该返回空数组', async () => {
      await linkService.createLink({
        sourceId: 'doc1',
        targetId: 'doc2',
        type: LinkType.REFERENCE
      })

      const allDocumentIds = ['doc1', 'doc2']
      const orphans = await linkService.findOrphanDocuments(allDocumentIds)

      expect(orphans).toEqual([])
    })
  })

  describe('getLinkStats', () => {
    beforeEach(async () => {
      await linkService.createLink({
        sourceId: 'doc1',
        targetId: 'doc2',
        type: LinkType.REFERENCE
      })

      await linkService.createLink({
        sourceId: 'doc2',
        targetId: 'doc3',
        type: LinkType.REFERENCE
      })

      await linkService.createLink({
        sourceId: 'doc3',
        targetId: 'doc4',
        type: LinkType.RELATED
      })

      await linkService.createLink({
        sourceId: 'doc4',
        targetId: 'doc5',
        type: LinkType.EMBED
      })
    })

    it('应该返回正确的链接统计信息', async () => {
      const stats = await linkService.getLinkStats()

      expect(stats.totalLinks).toBe(4)
      expect(stats.linksByType[LinkType.REFERENCE]).toBe(2)
      expect(stats.linksByType[LinkType.RELATED]).toBe(1)
      expect(stats.linksByType[LinkType.EMBED]).toBe(1)
      expect(stats.mostLinkedDocuments).toBeDefined()
      expect(stats.averageLinksPerDocument).toBeGreaterThan(0)
    })
  })

  describe('validateLink', () => {
    it('应该验证有效的链接数据', () => {
      const validLink = {
        sourceId: 'doc1',
        targetId: 'doc2',
        type: LinkType.REFERENCE,
        label: '有效链接'
      }

      expect(() => linkService.validateLink(validLink)).not.toThrow()
    })

    it('应该拒绝无效的链接数据', () => {
      const invalidLink = {
        sourceId: '',
        targetId: 'doc2',
        type: LinkType.REFERENCE
      }

      expect(() => linkService.validateLink(invalidLink)).toThrow('源文档ID不能为空')
    })

    it('应该拒绝自引用链接', () => {
      const selfReferenceLink = {
        sourceId: 'doc1',
        targetId: 'doc1',
        type: LinkType.REFERENCE
      }

      expect(() => linkService.validateLink(selfReferenceLink)).toThrow('不能创建自引用链接')
    })
  })
})
