/**
 * 文档数据访问对象测试
 * 测试文档CRUD操作的各种场景
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { documentDAO } from '../documentDAO'
import { DocumentType, BaseDocument, TextContent } from '@/types'

// 模拟数据库连接
vi.mock('../connection', () => ({
  getDB: vi.fn(() => Promise.resolve({
    add: vi.fn(),
    get: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    getAll: vi.fn(),
    getAllFromIndex: vi.fn(),
    transaction: vi.fn()
  }))
}))

describe('DocumentDAO', () => {
  let mockDocument: Omit<BaseDocument, 'id' | 'createdAt' | 'updatedAt'>

  beforeEach(() => {
    // 重置模拟数据
    mockDocument = {
      title: '测试文档',
      type: DocumentType.TEXT,
      content: {
        markdown: '# 测试标题\n\n这是测试内容',
        outline: []
      } as TextContent,
      metadata: {
        version: 1,
        size: 100,
        checksum: 'abc123',
        wordCount: 10,
        readingTime: 1
      },
      tags: ['测试', '文档'],
      links: []
    }
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('create', () => {
    it('应该成功创建文档', async () => {
      const { getDB } = await import('../connection')
      const mockDB = await getDB()
      
      // 模拟数据库添加操作成功
      vi.mocked(mockDB.add).mockResolvedValue(undefined)

      const result = await documentDAO.create(mockDocument)

      expect(result).toMatchObject({
        title: mockDocument.title,
        type: mockDocument.type,
        content: mockDocument.content,
        tags: mockDocument.tags
      })
      expect(result.id).toBeDefined()
      expect(result.createdAt).toBeInstanceOf(Date)
      expect(result.updatedAt).toBeInstanceOf(Date)
      expect(mockDB.add).toHaveBeenCalledWith('documents', expect.any(Object))
    })

    it('应该在标题为空时抛出错误', async () => {
      const invalidDocument = {
        ...mockDocument,
        title: ''
      }

      await expect(documentDAO.create(invalidDocument)).rejects.toThrow('文档标题不能为空')
    })

    it('应该在文档类型无效时抛出错误', async () => {
      const invalidDocument = {
        ...mockDocument,
        type: 'invalid-type' as DocumentType
      }

      await expect(documentDAO.create(invalidDocument)).rejects.toThrow('无效的文档类型')
    })
  })

  describe('getById', () => {
    it('应该成功获取存在的文档', async () => {
      const { getDB } = await import('../connection')
      const mockDB = await getDB()
      
      const mockExistingDocument: BaseDocument = {
        ...mockDocument,
        id: 'test-id',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      vi.mocked(mockDB.get).mockResolvedValue(mockExistingDocument)

      const result = await documentDAO.getById('test-id')

      expect(result).toEqual(mockExistingDocument)
      expect(mockDB.get).toHaveBeenCalledWith('documents', 'test-id')
    })

    it('应该在文档不存在时返回undefined', async () => {
      const { getDB } = await import('../connection')
      const mockDB = await getDB()
      
      vi.mocked(mockDB.get).mockResolvedValue(undefined)

      const result = await documentDAO.getById('non-existent-id')

      expect(result).toBeUndefined()
      expect(mockDB.get).toHaveBeenCalledWith('documents', 'non-existent-id')
    })
  })

  describe('update', () => {
    it('应该成功更新文档', async () => {
      const { getDB } = await import('../connection')
      const mockDB = await getDB()
      
      const existingDocument: BaseDocument = {
        ...mockDocument,
        id: 'test-id',
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-01')
      }

      const updates = {
        title: '更新后的标题',
        content: {
          markdown: '# 更新后的内容',
          outline: []
        } as TextContent
      }

      // 模拟获取现有文档
      vi.mocked(mockDB.get).mockResolvedValue(existingDocument)
      // 模拟更新操作成功
      vi.mocked(mockDB.put).mockResolvedValue(undefined)

      const result = await documentDAO.update('test-id', updates)

      expect(result.title).toBe(updates.title)
      expect(result.content).toEqual(updates.content)
      expect(result.id).toBe('test-id')
      expect(result.createdAt).toEqual(existingDocument.createdAt)
      expect(result.updatedAt).not.toEqual(existingDocument.updatedAt)
      expect(mockDB.put).toHaveBeenCalledWith('documents', expect.any(Object))
    })

    it('应该在文档不存在时抛出错误', async () => {
      const { getDB } = await import('../connection')
      const mockDB = await getDB()
      
      vi.mocked(mockDB.get).mockResolvedValue(undefined)

      await expect(documentDAO.update('non-existent-id', { title: '新标题' }))
        .rejects.toThrow('文档不存在: non-existent-id')
    })
  })

  describe('delete', () => {
    it('应该成功软删除文档', async () => {
      const { getDB } = await import('../connection')
      const mockDB = await getDB()
      
      const existingDocument: BaseDocument = {
        ...mockDocument,
        id: 'test-id',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      vi.mocked(mockDB.get).mockResolvedValue(existingDocument)
      vi.mocked(mockDB.put).mockResolvedValue(undefined)

      await documentDAO.delete('test-id')

      expect(mockDB.put).toHaveBeenCalledWith('documents', expect.objectContaining({
        id: 'test-id',
        isDeleted: true
      }))
    })
  })

  describe('getAll', () => {
    it('应该获取所有未删除的文档', async () => {
      const { getDB } = await import('../connection')
      const mockDB = await getDB()
      
      const mockDocuments: BaseDocument[] = [
        {
          ...mockDocument,
          id: 'doc1',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          ...mockDocument,
          id: 'doc2',
          createdAt: new Date(),
          updatedAt: new Date(),
          isDeleted: true
        },
        {
          ...mockDocument,
          id: 'doc3',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      vi.mocked(mockDB.getAll).mockResolvedValue(mockDocuments)

      const result = await documentDAO.getAll()

      expect(result).toHaveLength(2)
      expect(result.every(doc => !doc.isDeleted)).toBe(true)
      expect(mockDB.getAll).toHaveBeenCalledWith('documents')
    })

    it('应该在includeDeleted为true时包含已删除的文档', async () => {
      const { getDB } = await import('../connection')
      const mockDB = await getDB()
      
      const mockDocuments: BaseDocument[] = [
        {
          ...mockDocument,
          id: 'doc1',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          ...mockDocument,
          id: 'doc2',
          createdAt: new Date(),
          updatedAt: new Date(),
          isDeleted: true
        }
      ]

      vi.mocked(mockDB.getAll).mockResolvedValue(mockDocuments)

      const result = await documentDAO.getAll(true)

      expect(result).toHaveLength(2)
      expect(mockDB.getAll).toHaveBeenCalledWith('documents')
    })
  })

  describe('getByType', () => {
    it('应该根据类型获取文档', async () => {
      const { getDB } = await import('../connection')
      const mockDB = await getDB()
      
      const mockDocuments: BaseDocument[] = [
        {
          ...mockDocument,
          id: 'doc1',
          type: DocumentType.TEXT,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          ...mockDocument,
          id: 'doc2',
          type: DocumentType.TEXT,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      vi.mocked(mockDB.getAllFromIndex).mockResolvedValue(mockDocuments)

      const result = await documentDAO.getByType(DocumentType.TEXT)

      expect(result).toHaveLength(2)
      expect(result.every(doc => doc.type === DocumentType.TEXT)).toBe(true)
      expect(mockDB.getAllFromIndex).toHaveBeenCalledWith('documents', 'by-type', DocumentType.TEXT)
    })
  })

  describe('search', () => {
    it('应该根据标题搜索文档', async () => {
      const { getDB } = await import('../connection')
      const mockDB = await getDB()
      
      const mockDocuments: BaseDocument[] = [
        {
          ...mockDocument,
          id: 'doc1',
          title: '测试文档1',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          ...mockDocument,
          id: 'doc2',
          title: '其他文档',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          ...mockDocument,
          id: 'doc3',
          title: '测试文档2',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      vi.mocked(mockDB.getAll).mockResolvedValue(mockDocuments)

      const result = await documentDAO.search('测试')

      expect(result).toHaveLength(2)
      expect(result.every(doc => doc.title.includes('测试'))).toBe(true)
    })

    it('应该支持按类型过滤搜索结果', async () => {
      const { getDB } = await import('../connection')
      const mockDB = await getDB()
      
      const mockDocuments: BaseDocument[] = [
        {
          ...mockDocument,
          id: 'doc1',
          title: '测试文档',
          type: DocumentType.TEXT,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          ...mockDocument,
          id: 'doc2',
          title: '测试白板',
          type: DocumentType.WHITEBOARD,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      vi.mocked(mockDB.getAll).mockResolvedValue(mockDocuments)

      const result = await documentDAO.search('测试', {
        types: [DocumentType.TEXT]
      })

      expect(result).toHaveLength(1)
      expect(result[0].type).toBe(DocumentType.TEXT)
    })
  })
})
