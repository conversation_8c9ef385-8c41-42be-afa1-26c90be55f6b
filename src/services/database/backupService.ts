/**
 * 数据备份和恢复服务
 * 提供数据导出、导入、备份、恢复等功能
 */

import { getDB } from './connection'
import { documentDAO } from './documentDAO'
import { linkDAO } from './linkDAO'
import { BaseDocument, DocumentLink, ExportFormat, ImportFormat } from '@/types'
import { downloadFile, formatDate } from '@utils/index'

/**
 * 备份数据接口
 */
export interface BackupData {
  version: string // 备份格式版本
  timestamp: string // 备份时间戳
  documents: BaseDocument[] // 文档数据
  links: DocumentLink[] // 链接数据
  metadata: BackupMetadata // 备份元数据
}

/**
 * 备份元数据接口
 */
export interface BackupMetadata {
  totalDocuments: number // 文档总数
  totalLinks: number // 链接总数
  documentTypes: Record<string, number> // 各类型文档数量统计
  exportFormat: ExportFormat // 导出格式
  appVersion: string // 应用版本
}

/**
 * 导入结果接口
 */
export interface ImportResult {
  success: boolean // 是否成功
  documentsImported: number // 导入的文档数量
  linksImported: number // 导入的链接数量
  errors: string[] // 错误信息列表
  warnings: string[] // 警告信息列表
}

/**
 * 数据备份和恢复服务类
 */
export class BackupService {
  private readonly BACKUP_VERSION = '1.0'
  private readonly APP_VERSION = '0.1.0'

  /**
   * 导出所有数据为JSON格式
   * @param includeDeleted 是否包含已删除的文档
   * @returns Promise<BackupData> 备份数据
   */
  async exportData(includeDeleted = false): Promise<BackupData> {
    try {
      console.log('开始导出数据...')
      
      // 获取所有文档和链接
      const [documents, links] = await Promise.all([
        documentDAO.getAll(includeDeleted),
        linkDAO.getAll()
      ])

      // 生成统计信息
      const documentTypes: Record<string, number> = {}
      documents.forEach(doc => {
        documentTypes[doc.type] = (documentTypes[doc.type] || 0) + 1
      })

      // 构建备份数据
      const backupData: BackupData = {
        version: this.BACKUP_VERSION,
        timestamp: new Date().toISOString(),
        documents,
        links,
        metadata: {
          totalDocuments: documents.length,
          totalLinks: links.length,
          documentTypes,
          exportFormat: ExportFormat.JSON,
          appVersion: this.APP_VERSION
        }
      }

      console.log(`数据导出成功: ${documents.length} 个文档, ${links.length} 个链接`)
      return backupData
    } catch (error) {
      console.error('导出数据失败:', error)
      throw new Error(`导出数据失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 导入数据
   * @param backupData 备份数据
   * @param options 导入选项
   * @returns Promise<ImportResult> 导入结果
   */
  async importData(
    backupData: BackupData, 
    options: {
      overwrite?: boolean // 是否覆盖现有数据
      skipDuplicates?: boolean // 是否跳过重复数据
    } = {}
  ): Promise<ImportResult> {
    const result: ImportResult = {
      success: false,
      documentsImported: 0,
      linksImported: 0,
      errors: [],
      warnings: []
    }

    try {
      console.log('开始导入数据...')
      
      // 验证备份数据格式
      this.validateBackupData(backupData)

      // 如果选择覆盖，先清空现有数据
      if (options.overwrite) {
        await this.clearAllData()
        result.warnings.push('已清空现有数据')
      }

      // 导入文档
      for (const document of backupData.documents) {
        try {
          // 检查是否已存在
          if (options.skipDuplicates) {
            const existing = await documentDAO.getById(document.id)
            if (existing) {
              result.warnings.push(`跳过重复文档: ${document.title}`)
              continue
            }
          }

          // 创建或更新文档
          const db = await getDB()
          await db.put('documents', document)
          result.documentsImported++
        } catch (error) {
          const errorMsg = `导入文档失败 "${document.title}": ${error instanceof Error ? error.message : '未知错误'}`
          result.errors.push(errorMsg)
          console.error(errorMsg)
        }
      }

      // 导入链接
      for (const link of backupData.links) {
        try {
          // 检查源文档和目标文档是否存在
          const [sourceExists, targetExists] = await Promise.all([
            documentDAO.getById(link.sourceId),
            documentDAO.getById(link.targetId)
          ])

          if (!sourceExists || !targetExists) {
            result.warnings.push(`跳过无效链接: 源文档或目标文档不存在`)
            continue
          }

          // 检查是否已存在
          if (options.skipDuplicates) {
            const existing = await linkDAO.getById(link.id)
            if (existing) {
              result.warnings.push(`跳过重复链接: ${link.id}`)
              continue
            }
          }

          // 创建或更新链接
          const db = await getDB()
          await db.put('links', link)
          result.linksImported++
        } catch (error) {
          const errorMsg = `导入链接失败 "${link.id}": ${error instanceof Error ? error.message : '未知错误'}`
          result.errors.push(errorMsg)
          console.error(errorMsg)
        }
      }

      result.success = result.errors.length === 0
      console.log(`数据导入完成: ${result.documentsImported} 个文档, ${result.linksImported} 个链接`)
      
      if (result.errors.length > 0) {
        console.warn(`导入过程中发生 ${result.errors.length} 个错误`)
      }

      return result
    } catch (error) {
      result.errors.push(`导入数据失败: ${error instanceof Error ? error.message : '未知错误'}`)
      console.error('导入数据失败:', error)
      return result
    }
  }

  /**
   * 下载备份文件
   * @param includeDeleted 是否包含已删除的文档
   * @param filename 文件名（可选）
   * @returns Promise<void>
   */
  async downloadBackup(includeDeleted = false, filename?: string): Promise<void> {
    try {
      const backupData = await this.exportData(includeDeleted)
      const jsonString = JSON.stringify(backupData, null, 2)
      
      const defaultFilename = `multidimensional-notes-backup-${formatDate(new Date(), 'YYYY-MM-DD-HH-mm-ss')}.json`
      const finalFilename = filename || defaultFilename
      
      downloadFile(jsonString, finalFilename, 'application/json')
      console.log(`备份文件下载成功: ${finalFilename}`)
    } catch (error) {
      console.error('下载备份文件失败:', error)
      throw new Error(`下载备份文件失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 从文件导入数据
   * @param file 备份文件
   * @param options 导入选项
   * @returns Promise<ImportResult> 导入结果
   */
  async importFromFile(
    file: File, 
    options: {
      overwrite?: boolean
      skipDuplicates?: boolean
    } = {}
  ): Promise<ImportResult> {
    try {
      console.log(`开始从文件导入数据: ${file.name}`)
      
      // 读取文件内容
      const fileContent = await this.readFileAsText(file)
      
      // 解析JSON数据
      let backupData: BackupData
      try {
        backupData = JSON.parse(fileContent)
      } catch (error) {
        throw new Error('无效的JSON格式')
      }

      // 导入数据
      return await this.importData(backupData, options)
    } catch (error) {
      console.error('从文件导入数据失败:', error)
      return {
        success: false,
        documentsImported: 0,
        linksImported: 0,
        errors: [`从文件导入数据失败: ${error instanceof Error ? error.message : '未知错误'}`],
        warnings: []
      }
    }
  }

  /**
   * 清空所有数据
   * @returns Promise<void>
   */
  async clearAllData(): Promise<void> {
    try {
      console.log('开始清空所有数据...')
      
      const db = await getDB()
      const tx = db.transaction(['documents', 'links'], 'readwrite')
      
      await Promise.all([
        tx.objectStore('documents').clear(),
        tx.objectStore('links').clear()
      ])
      
      await tx.done
      console.log('所有数据清空成功')
    } catch (error) {
      console.error('清空数据失败:', error)
      throw new Error(`清空数据失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取数据库统计信息
   * @returns Promise<DatabaseStats> 统计信息
   */
  async getDatabaseStats(): Promise<DatabaseStats> {
    try {
      const [documents, links] = await Promise.all([
        documentDAO.getAll(),
        linkDAO.getAll()
      ])

      const documentTypes: Record<string, number> = {}
      const linkTypes: Record<string, number> = {}
      
      documents.forEach(doc => {
        documentTypes[doc.type] = (documentTypes[doc.type] || 0) + 1
      })
      
      links.forEach(link => {
        linkTypes[link.type] = (linkTypes[link.type] || 0) + 1
      })

      return {
        totalDocuments: documents.length,
        totalLinks: links.length,
        documentTypes,
        linkTypes,
        lastBackup: null // TODO: 实现最后备份时间记录
      }
    } catch (error) {
      console.error('获取数据库统计信息失败:', error)
      throw new Error(`获取数据库统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 验证备份数据格式
   * @param backupData 备份数据
   */
  private validateBackupData(backupData: BackupData): void {
    if (!backupData.version) {
      throw new Error('备份数据缺少版本信息')
    }
    
    if (!backupData.documents || !Array.isArray(backupData.documents)) {
      throw new Error('备份数据中的文档数据无效')
    }
    
    if (!backupData.links || !Array.isArray(backupData.links)) {
      throw new Error('备份数据中的链接数据无效')
    }
    
    if (!backupData.metadata) {
      throw new Error('备份数据缺少元数据')
    }
  }

  /**
   * 读取文件为文本
   * @param file 文件对象
   * @returns Promise<string> 文件内容
   */
  private readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (event) => {
        if (event.target?.result) {
          resolve(event.target.result as string)
        } else {
          reject(new Error('文件读取失败'))
        }
      }
      
      reader.onerror = () => {
        reject(new Error('文件读取错误'))
      }
      
      reader.readAsText(file, 'utf-8')
    })
  }
}

/**
 * 数据库统计信息接口
 */
export interface DatabaseStats {
  totalDocuments: number
  totalLinks: number
  documentTypes: Record<string, number>
  linkTypes: Record<string, number>
  lastBackup: Date | null
}

/**
 * 导出备份服务实例
 */
export const backupService = new BackupService()
