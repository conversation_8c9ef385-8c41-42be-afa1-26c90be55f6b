/**
 * 文档数据访问对象 (Document DAO)
 * 提供文档的创建、读取、更新、删除等数据库操作
 */

import { getDB } from './connection'
import { BaseDocument, DocumentType, TextDocument, SearchOptions } from '@/types'
import { generateUUID } from '@utils/index'

/**
 * 文档数据访问对象类
 * 封装所有与文档相关的数据库操作
 */
export class DocumentDAO {
  /**
   * 创建新文档
   * @param document 文档数据（不包含ID，会自动生成）
   * @returns Promise<BaseDocument> 创建的文档（包含生成的ID）
   */
  async create(document: Omit<BaseDocument, 'id' | 'createdAt' | 'updatedAt'>): Promise<BaseDocument> {
    try {
      const db = await getDB()
      
      // 生成文档ID和时间戳
      const now = new Date()
      const newDocument: BaseDocument = {
        ...document,
        id: generateUUID(),
        createdAt: now,
        updatedAt: now,
        links: document.links || [],
        tags: document.tags || []
      }

      // 验证文档数据
      this.validateDocument(newDocument)

      // 保存到数据库
      await db.add('documents', newDocument)
      
      console.log(`文档创建成功: ${newDocument.id} (${newDocument.title})`)
      return newDocument
    } catch (error) {
      console.error('创建文档失败:', error)
      throw new Error(`创建文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 根据ID获取文档
   * @param id 文档ID
   * @returns Promise<BaseDocument | undefined> 文档数据或undefined
   */
  async getById(id: string): Promise<BaseDocument | undefined> {
    try {
      const db = await getDB()
      const document = await db.get('documents', id)
      
      if (document) {
        console.log(`获取文档成功: ${id}`)
      } else {
        console.warn(`文档不存在: ${id}`)
      }
      
      return document
    } catch (error) {
      console.error('获取文档失败:', error)
      throw new Error(`获取文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 更新文档
   * @param id 文档ID
   * @param updates 要更新的字段
   * @returns Promise<BaseDocument> 更新后的文档
   */
  async update(id: string, updates: Partial<BaseDocument>): Promise<BaseDocument> {
    try {
      const db = await getDB()
      
      // 获取现有文档
      const existingDocument = await this.getById(id)
      if (!existingDocument) {
        throw new Error(`文档不存在: ${id}`)
      }

      // 合并更新数据
      const updatedDocument: BaseDocument = {
        ...existingDocument,
        ...updates,
        id, // 确保ID不被修改
        createdAt: existingDocument.createdAt, // 确保创建时间不被修改
        updatedAt: new Date() // 更新修改时间
      }

      // 验证更新后的文档数据
      this.validateDocument(updatedDocument)

      // 保存到数据库
      await db.put('documents', updatedDocument)
      
      console.log(`文档更新成功: ${id} (${updatedDocument.title})`)
      return updatedDocument
    } catch (error) {
      console.error('更新文档失败:', error)
      throw new Error(`更新文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 删除文档（软删除）
   * @param id 文档ID
   * @returns Promise<void>
   */
  async delete(id: string): Promise<void> {
    try {
      // 使用软删除，只标记为已删除
      await this.update(id, { 
        isDeleted: true,
        updatedAt: new Date()
      })
      
      console.log(`文档软删除成功: ${id}`)
    } catch (error) {
      console.error('删除文档失败:', error)
      throw new Error(`删除文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 永久删除文档
   * @param id 文档ID
   * @returns Promise<void>
   */
  async permanentDelete(id: string): Promise<void> {
    try {
      const db = await getDB()
      await db.delete('documents', id)
      
      console.log(`文档永久删除成功: ${id}`)
    } catch (error) {
      console.error('永久删除文档失败:', error)
      throw new Error(`永久删除文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取所有文档
   * @param includeDeleted 是否包含已删除的文档
   * @returns Promise<BaseDocument[]> 文档列表
   */
  async getAll(includeDeleted = false): Promise<BaseDocument[]> {
    try {
      const db = await getDB()
      const documents = await db.getAll('documents')
      
      // 过滤已删除的文档（除非明确要求包含）
      const filteredDocuments = includeDeleted 
        ? documents 
        : documents.filter(doc => !doc.isDeleted)
      
      console.log(`获取文档列表成功: ${filteredDocuments.length} 个文档`)
      return filteredDocuments
    } catch (error) {
      console.error('获取文档列表失败:', error)
      throw new Error(`获取文档列表失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 根据类型获取文档
   * @param type 文档类型
   * @param includeDeleted 是否包含已删除的文档
   * @returns Promise<BaseDocument[]> 文档列表
   */
  async getByType(type: DocumentType, includeDeleted = false): Promise<BaseDocument[]> {
    try {
      const db = await getDB()
      const documents = await db.getAllFromIndex('documents', 'by-type', type)
      
      // 过滤已删除的文档
      const filteredDocuments = includeDeleted 
        ? documents 
        : documents.filter(doc => !doc.isDeleted)
      
      console.log(`获取${type}类型文档成功: ${filteredDocuments.length} 个文档`)
      return filteredDocuments
    } catch (error) {
      console.error('根据类型获取文档失败:', error)
      throw new Error(`根据类型获取文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 根据标签获取文档
   * @param tag 标签名称
   * @param includeDeleted 是否包含已删除的文档
   * @returns Promise<BaseDocument[]> 文档列表
   */
  async getByTag(tag: string, includeDeleted = false): Promise<BaseDocument[]> {
    try {
      const db = await getDB()
      const documents = await db.getAllFromIndex('documents', 'by-tags', tag)
      
      // 过滤已删除的文档
      const filteredDocuments = includeDeleted 
        ? documents 
        : documents.filter(doc => !doc.isDeleted)
      
      console.log(`获取标签"${tag}"的文档成功: ${filteredDocuments.length} 个文档`)
      return filteredDocuments
    } catch (error) {
      console.error('根据标签获取文档失败:', error)
      throw new Error(`根据标签获取文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 搜索文档
   * @param query 搜索关键词
   * @param options 搜索选项
   * @returns Promise<BaseDocument[]> 匹配的文档列表
   */
  async search(query: string, options: SearchOptions = {}): Promise<BaseDocument[]> {
    try {
      const db = await getDB()
      let documents = await db.getAll('documents')
      
      // 过滤已删除的文档
      documents = documents.filter(doc => !doc.isDeleted)
      
      // 根据类型过滤
      if (options.types && options.types.length > 0) {
        documents = documents.filter(doc => options.types!.includes(doc.type))
      }
      
      // 根据标签过滤
      if (options.tags && options.tags.length > 0) {
        documents = documents.filter(doc => 
          options.tags!.some(tag => doc.tags.includes(tag))
        )
      }
      
      // 根据日期范围过滤
      if (options.dateRange) {
        const { start, end } = options.dateRange
        documents = documents.filter(doc => 
          doc.createdAt >= start && doc.createdAt <= end
        )
      }
      
      // 文本搜索（简单的包含匹配）
      if (query.trim()) {
        const searchTerm = query.toLowerCase()
        documents = documents.filter(doc => 
          doc.title.toLowerCase().includes(searchTerm) ||
          doc.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
          (doc.metadata.description && doc.metadata.description.toLowerCase().includes(searchTerm))
        )
      }
      
      // 排序
      if (options.sortBy) {
        documents.sort((a, b) => {
          const aValue = this.getDocumentSortValue(a, options.sortBy!)
          const bValue = this.getDocumentSortValue(b, options.sortBy!)
          
          if (options.sortOrder === 'desc') {
            return bValue > aValue ? 1 : -1
          } else {
            return aValue > bValue ? 1 : -1
          }
        })
      }
      
      // 分页
      if (options.limit || options.offset) {
        const offset = options.offset || 0
        const limit = options.limit || documents.length
        documents = documents.slice(offset, offset + limit)
      }
      
      console.log(`搜索文档成功: 找到 ${documents.length} 个匹配的文档`)
      return documents
    } catch (error) {
      console.error('搜索文档失败:', error)
      throw new Error(`搜索文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取最近修改的文档
   * @param limit 返回数量限制
   * @returns Promise<BaseDocument[]> 最近修改的文档列表
   */
  async getRecentlyModified(limit = 10): Promise<BaseDocument[]> {
    try {
      const db = await getDB()
      const tx = db.transaction('documents', 'readonly')
      const index = tx.store.index('by-updated-at')
      
      const documents: BaseDocument[] = []
      let cursor = await index.openCursor(null, 'prev') // 降序遍历
      
      while (cursor && documents.length < limit) {
        const document = cursor.value
        if (!document.isDeleted) {
          documents.push(document)
        }
        cursor = await cursor.continue()
      }
      
      console.log(`获取最近修改的文档成功: ${documents.length} 个文档`)
      return documents
    } catch (error) {
      console.error('获取最近修改的文档失败:', error)
      throw new Error(`获取最近修改的文档失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 验证文档数据
   * @param document 文档数据
   */
  private validateDocument(document: BaseDocument): void {
    if (!document.id) {
      throw new Error('文档ID不能为空')
    }
    
    if (!document.title || document.title.trim() === '') {
      throw new Error('文档标题不能为空')
    }
    
    if (!Object.values(DocumentType).includes(document.type)) {
      throw new Error(`无效的文档类型: ${document.type}`)
    }
    
    if (!document.createdAt || !document.updatedAt) {
      throw new Error('文档创建时间和更新时间不能为空')
    }
  }

  /**
   * 获取文档排序值
   * @param document 文档
   * @param sortBy 排序字段
   * @returns 排序值
   */
  private getDocumentSortValue(document: BaseDocument, sortBy: string): any {
    switch (sortBy) {
      case 'title':
        return document.title
      case 'createdAt':
        return document.createdAt
      case 'updatedAt':
        return document.updatedAt
      case 'type':
        return document.type
      default:
        return document.updatedAt
    }
  }
}

/**
 * 导出文档DAO实例
 */
export const documentDAO = new DocumentDAO()
