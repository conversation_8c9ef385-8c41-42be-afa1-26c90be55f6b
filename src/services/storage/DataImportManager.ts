/**
 * 数据导入管理器
 * 提供多种格式的数据导入功能，包括JSON、CSV、Markdown等
 */

import { BaseDocument, DocumentType, TextDocument } from '@/types'
import { documentDAO } from '@services/database/documentDAO'
import { linkDAO } from '@services/database/linkDAO'
import { generateUUID } from '@utils/index'

/**
 * 导入格式枚举
 */
export enum ImportFormat {
  JSON = 'json',
  CSV = 'csv',
  MARKDOWN = 'markdown',
  TEXT = 'text'
}

/**
 * 导入选项接口
 */
export interface ImportOptions {
  format: ImportFormat
  overwriteExisting?: boolean
  skipDuplicates?: boolean
  createBackup?: boolean
  preserveIds?: boolean
  defaultTags?: string[]
}

/**
 * 导入结果接口
 */
export interface ImportResult {
  success: boolean
  documentsImported: number
  documentsSkipped: number
  linksImported: number
  errors: string[]
  warnings: string[]
  backupCreated?: string
}

/**
 * 文件解析结果接口
 */
interface ParsedFileData {
  documents: Partial<BaseDocument>[]
  links?: any[]
  metadata?: any
}

/**
 * 数据导入管理器类
 */
export class DataImportManager {
  /**
   * 导入数据
   * @param file 要导入的文件
   * @param options 导入选项
   * @returns Promise<ImportResult> 导入结果
   */
  async importData(file: File, options: ImportOptions): Promise<ImportResult> {
    const result: ImportResult = {
      success: false,
      documentsImported: 0,
      documentsSkipped: 0,
      linksImported: 0,
      errors: [],
      warnings: []
    }

    try {
      console.log('开始导入数据...', { filename: file.name, size: file.size, options })

      // 创建备份（如果需要）
      if (options.createBackup) {
        result.backupCreated = await this.createBackup()
        result.warnings.push(`已创建备份: ${result.backupCreated}`)
      }

      // 读取文件内容
      const fileContent = await this.readFile(file)
      
      // 解析文件数据
      const parsedData = await this.parseFileContent(fileContent, options.format, file.name)

      // 验证数据
      this.validateParsedData(parsedData)

      // 导入文档
      for (const docData of parsedData.documents) {
        try {
          const importedDoc = await this.importDocument(docData, options)
          if (importedDoc) {
            result.documentsImported++
          } else {
            result.documentsSkipped++
          }
        } catch (error) {
          const errorMsg = `导入文档失败 "${docData.title}": ${error instanceof Error ? error.message : '未知错误'}`
          result.errors.push(errorMsg)
          console.error(errorMsg)
        }
      }

      // 导入链接（如果有）
      if (parsedData.links && parsedData.links.length > 0) {
        for (const linkData of parsedData.links) {
          try {
            await linkDAO.create(linkData)
            result.linksImported++
          } catch (error) {
            const errorMsg = `导入链接失败: ${error instanceof Error ? error.message : '未知错误'}`
            result.errors.push(errorMsg)
            console.error(errorMsg)
          }
        }
      }

      result.success = result.errors.length === 0 || result.documentsImported > 0
      
      console.log('数据导入完成:', result)
      return result
    } catch (error) {
      console.error('导入数据失败:', error)
      result.errors.push(error instanceof Error ? error.message : '未知错误')
      return result
    }
  }

  /**
   * 读取文件内容
   */
  private async readFile(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file, 'utf-8')
    })
  }

  /**
   * 解析文件内容
   */
  private async parseFileContent(
    content: string, 
    format: ImportFormat, 
    filename: string
  ): Promise<ParsedFileData> {
    switch (format) {
      case ImportFormat.JSON:
        return this.parseJSON(content)
      case ImportFormat.CSV:
        return this.parseCSV(content)
      case ImportFormat.MARKDOWN:
        return this.parseMarkdown(content, filename)
      case ImportFormat.TEXT:
        return this.parseText(content, filename)
      default:
        throw new Error(`不支持的导入格式: ${format}`)
    }
  }

  /**
   * 解析JSON格式
   */
  private parseJSON(content: string): ParsedFileData {
    try {
      const data = JSON.parse(content)
      
      // 检查是否是我们的导出格式
      if (data.documents && Array.isArray(data.documents)) {
        return {
          documents: data.documents,
          links: data.links || [],
          metadata: data.metadata
        }
      }
      
      // 尝试作为单个文档数组处理
      if (Array.isArray(data)) {
        return { documents: data }
      }
      
      // 作为单个文档处理
      return { documents: [data] }
    } catch (error) {
      throw new Error(`JSON解析失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 解析CSV格式
   */
  private parseCSV(content: string): ParsedFileData {
    const lines = content.split('\n').filter(line => line.trim())
    if (lines.length < 2) {
      throw new Error('CSV文件格式无效：至少需要标题行和一行数据')
    }

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    const documents: Partial<BaseDocument>[] = []

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))
      const doc: Partial<BaseDocument> = {}

      headers.forEach((header, index) => {
        const value = values[index] || ''
        switch (header.toLowerCase()) {
          case 'id':
            doc.id = value
            break
          case '标题':
          case 'title':
            doc.title = value
            break
          case '类型':
          case 'type':
            doc.type = value as DocumentType
            break
          case '标签':
          case 'tags':
            doc.tags = value ? value.split(';') : []
            break
          case '内容':
          case 'content':
            doc.content = { text: value }
            break
        }
      })

      if (doc.title) {
        documents.push(doc)
      }
    }

    return { documents }
  }

  /**
   * 解析Markdown格式
   */
  private parseMarkdown(content: string, filename: string): ParsedFileData {
    const documents: Partial<BaseDocument>[] = []
    
    // 按标题分割文档
    const sections = content.split(/^#{1,3}\s+/m).filter(section => section.trim())
    
    sections.forEach((section, index) => {
      const lines = section.split('\n')
      const title = lines[0]?.trim() || `导入文档 ${index + 1}`
      const content = lines.slice(1).join('\n').trim()
      
      if (title && content) {
        documents.push({
          title,
          type: DocumentType.TEXT,
          content: { text: content },
          tags: this.extractTagsFromMarkdown(content)
        })
      }
    })

    // 如果没有找到分节，将整个文件作为一个文档
    if (documents.length === 0) {
      documents.push({
        title: filename.replace(/\.[^/.]+$/, ''), // 移除文件扩展名
        type: DocumentType.TEXT,
        content: { text: content },
        tags: this.extractTagsFromMarkdown(content)
      })
    }

    return { documents }
  }

  /**
   * 解析纯文本格式
   */
  private parseText(content: string, filename: string): ParsedFileData {
    return {
      documents: [{
        title: filename.replace(/\.[^/.]+$/, ''), // 移除文件扩展名
        type: DocumentType.TEXT,
        content: { text: content },
        tags: []
      }]
    }
  }

  /**
   * 从Markdown内容中提取标签
   */
  private extractTagsFromMarkdown(content: string): string[] {
    const tagRegex = /#(\w+)/g
    const tags: string[] = []
    let match

    while ((match = tagRegex.exec(content)) !== null) {
      if (!tags.includes(match[1])) {
        tags.push(match[1])
      }
    }

    return tags
  }

  /**
   * 验证解析的数据
   */
  private validateParsedData(data: ParsedFileData): void {
    if (!data.documents || !Array.isArray(data.documents)) {
      throw new Error('无效的数据格式：缺少文档数组')
    }

    if (data.documents.length === 0) {
      throw new Error('没有找到可导入的文档')
    }

    // 验证每个文档的基本字段
    data.documents.forEach((doc, index) => {
      if (!doc.title) {
        throw new Error(`文档 ${index + 1} 缺少标题`)
      }
    })
  }

  /**
   * 导入单个文档
   */
  private async importDocument(
    docData: Partial<BaseDocument>, 
    options: ImportOptions
  ): Promise<BaseDocument | null> {
    // 检查是否已存在
    if (docData.id && !options.preserveIds) {
      const existing = await documentDAO.getById(docData.id)
      if (existing) {
        if (options.skipDuplicates) {
          return null // 跳过重复文档
        }
        if (!options.overwriteExisting) {
          // 生成新ID
          docData.id = generateUUID()
        }
      }
    }

    // 补充必要字段
    const document: Omit<BaseDocument, 'id' | 'createdAt' | 'updatedAt'> = {
      title: docData.title || '未命名文档',
      type: docData.type || DocumentType.TEXT,
      content: docData.content || { text: '' },
      tags: [...(docData.tags || []), ...(options.defaultTags || [])],
      links: docData.links || [],
      version: docData.version || 1,
      metadata: docData.metadata || {}
    }

    // 创建文档
    return await documentDAO.create(document)
  }

  /**
   * 创建备份
   */
  private async createBackup(): Promise<string> {
    // 这里可以调用备份服务创建备份
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    return `backup-${timestamp}.json`
  }
}

// 导出单例实例
export const dataImportManager = new DataImportManager()
