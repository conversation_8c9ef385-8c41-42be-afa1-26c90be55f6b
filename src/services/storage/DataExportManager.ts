/**
 * 数据导出管理器
 * 提供多种格式的数据导出功能，包括JSON、CSV、Markdown等
 */

import { BaseDocument, DocumentType, TextDocument, WhiteboardDocument, MindMapDocument, KanbanDocument } from '@/types'
import { documentDAO } from '@services/database/documentDAO'
import { linkDAO } from '@services/database/linkDAO'
import { formatDate, downloadFile } from '@utils/index'

/**
 * 导出格式枚举
 */
export enum ExportFormat {
  JSON = 'json',
  CSV = 'csv',
  MARKDOWN = 'markdown',
  HTML = 'html',
  PDF = 'pdf'
}

/**
 * 导出选项接口
 */
export interface ExportOptions {
  format: ExportFormat
  includeLinks?: boolean
  includeMetadata?: boolean
  includeDeleted?: boolean
  documentTypes?: DocumentType[]
  dateRange?: {
    start: Date
    end: Date
  }
  tags?: string[]
}

/**
 * 导出结果接口
 */
export interface ExportResult {
  success: boolean
  filename: string
  size: number
  documentsCount: number
  linksCount: number
  errors: string[]
}

/**
 * 数据导出管理器类
 */
export class DataExportManager {
  /**
   * 导出数据
   * @param options 导出选项
   * @returns Promise<ExportResult> 导出结果
   */
  async exportData(options: ExportOptions): Promise<ExportResult> {
    try {
      console.log('开始导出数据...', options)

      // 获取要导出的文档
      const documents = await this.getDocumentsForExport(options)
      
      // 获取链接数据
      const links = options.includeLinks ? await linkDAO.getAll() : []

      // 根据格式生成导出内容
      let content: string
      let filename: string
      let mimeType: string

      switch (options.format) {
        case ExportFormat.JSON:
          ({ content, filename, mimeType } = await this.exportAsJSON(documents, links, options))
          break
        case ExportFormat.CSV:
          ({ content, filename, mimeType } = await this.exportAsCSV(documents, options))
          break
        case ExportFormat.MARKDOWN:
          ({ content, filename, mimeType } = await this.exportAsMarkdown(documents, options))
          break
        case ExportFormat.HTML:
          ({ content, filename, mimeType } = await this.exportAsHTML(documents, options))
          break
        default:
          throw new Error(`不支持的导出格式: ${options.format}`)
      }

      // 下载文件
      await downloadFile(content, filename, mimeType)

      return {
        success: true,
        filename,
        size: new Blob([content]).size,
        documentsCount: documents.length,
        linksCount: links.length,
        errors: []
      }
    } catch (error) {
      console.error('导出数据失败:', error)
      return {
        success: false,
        filename: '',
        size: 0,
        documentsCount: 0,
        linksCount: 0,
        errors: [error instanceof Error ? error.message : '未知错误']
      }
    }
  }

  /**
   * 获取要导出的文档
   */
  private async getDocumentsForExport(options: ExportOptions): Promise<BaseDocument[]> {
    let documents = await documentDAO.getAll(options.includeDeleted)

    // 按文档类型筛选
    if (options.documentTypes && options.documentTypes.length > 0) {
      documents = documents.filter(doc => options.documentTypes!.includes(doc.type))
    }

    // 按日期范围筛选
    if (options.dateRange) {
      const { start, end } = options.dateRange
      documents = documents.filter(doc => {
        const docDate = new Date(doc.updatedAt)
        return docDate >= start && docDate <= end
      })
    }

    // 按标签筛选
    if (options.tags && options.tags.length > 0) {
      documents = documents.filter(doc => 
        options.tags!.some(tag => doc.tags.includes(tag))
      )
    }

    return documents
  }

  /**
   * 导出为JSON格式
   */
  private async exportAsJSON(
    documents: BaseDocument[], 
    links: any[], 
    options: ExportOptions
  ): Promise<{ content: string; filename: string; mimeType: string }> {
    const exportData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      metadata: {
        totalDocuments: documents.length,
        totalLinks: links.length,
        exportOptions: options,
        appVersion: '0.1.0'
      },
      documents: options.includeMetadata ? documents : documents.map(doc => ({
        id: doc.id,
        title: doc.title,
        type: doc.type,
        content: doc.content,
        tags: doc.tags,
        createdAt: doc.createdAt,
        updatedAt: doc.updatedAt
      })),
      links: options.includeLinks ? links : []
    }

    const content = JSON.stringify(exportData, null, 2)
    const filename = `multidimensional-notes-export-${formatDate(new Date(), 'YYYY-MM-DD-HH-mm-ss')}.json`
    
    return {
      content,
      filename,
      mimeType: 'application/json'
    }
  }

  /**
   * 导出为CSV格式
   */
  private async exportAsCSV(
    documents: BaseDocument[], 
    options: ExportOptions
  ): Promise<{ content: string; filename: string; mimeType: string }> {
    const headers = ['ID', '标题', '类型', '标签', '创建时间', '更新时间']
    if (options.includeMetadata) {
      headers.push('内容长度', '版本')
    }

    const rows = [headers.join(',')]

    documents.forEach(doc => {
      const row = [
        `"${doc.id}"`,
        `"${doc.title.replace(/"/g, '""')}"`,
        `"${doc.type}"`,
        `"${doc.tags.join(';')}"`,
        `"${formatDate(doc.createdAt)}"`,
        `"${formatDate(doc.updatedAt)}"`
      ]

      if (options.includeMetadata) {
        row.push(`"${JSON.stringify(doc.content).length}"`)
        row.push(`"${doc.version || 1}"`)
      }

      rows.push(row.join(','))
    })

    const content = rows.join('\n')
    const filename = `multidimensional-notes-export-${formatDate(new Date(), 'YYYY-MM-DD-HH-mm-ss')}.csv`
    
    return {
      content,
      filename,
      mimeType: 'text/csv'
    }
  }

  /**
   * 导出为Markdown格式
   */
  private async exportAsMarkdown(
    documents: BaseDocument[], 
    options: ExportOptions
  ): Promise<{ content: string; filename: string; mimeType: string }> {
    const lines = [
      '# 多维度笔记导出',
      '',
      `导出时间: ${formatDate(new Date())}`,
      `文档数量: ${documents.length}`,
      '',
      '---',
      ''
    ]

    // 按类型分组
    const documentsByType = documents.reduce((acc, doc) => {
      if (!acc[doc.type]) acc[doc.type] = []
      acc[doc.type].push(doc)
      return acc
    }, {} as Record<string, BaseDocument[]>)

    Object.entries(documentsByType).forEach(([type, docs]) => {
      lines.push(`## ${this.getTypeDisplayName(type)} (${docs.length})`)
      lines.push('')

      docs.forEach(doc => {
        lines.push(`### ${doc.title}`)
        lines.push('')
        
        if (doc.tags.length > 0) {
          lines.push(`**标签**: ${doc.tags.map(tag => `\`${tag}\``).join(', ')}`)
          lines.push('')
        }

        lines.push(`**创建时间**: ${formatDate(doc.createdAt)}`)
        lines.push(`**更新时间**: ${formatDate(doc.updatedAt)}`)
        lines.push('')

        // 根据文档类型处理内容
        if (doc.type === DocumentType.TEXT) {
          const textDoc = doc as TextDocument
          if (textDoc.content && textDoc.content.text) {
            lines.push(textDoc.content.text)
          }
        } else {
          lines.push('*此文档类型的内容无法在Markdown中完整显示*')
        }

        lines.push('')
        lines.push('---')
        lines.push('')
      })
    })

    const content = lines.join('\n')
    const filename = `multidimensional-notes-export-${formatDate(new Date(), 'YYYY-MM-DD-HH-mm-ss')}.md`
    
    return {
      content,
      filename,
      mimeType: 'text/markdown'
    }
  }

  /**
   * 导出为HTML格式
   */
  private async exportAsHTML(
    documents: BaseDocument[], 
    options: ExportOptions
  ): Promise<{ content: string; filename: string; mimeType: string }> {
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多维度笔记导出</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; margin: 40px; }
        .header { border-bottom: 2px solid #eee; padding-bottom: 20px; margin-bottom: 30px; }
        .document { margin-bottom: 40px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .document-title { color: #333; margin-bottom: 10px; }
        .document-meta { color: #666; font-size: 14px; margin-bottom: 15px; }
        .tag { background: #f0f0f0; padding: 2px 8px; border-radius: 4px; margin-right: 5px; }
        .content { margin-top: 15px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>多维度笔记导出</h1>
        <p>导出时间: ${formatDate(new Date())}</p>
        <p>文档数量: ${documents.length}</p>
    </div>
    
    ${documents.map(doc => `
        <div class="document">
            <h2 class="document-title">${doc.title}</h2>
            <div class="document-meta">
                <span>类型: ${this.getTypeDisplayName(doc.type)}</span> | 
                <span>创建: ${formatDate(doc.createdAt)}</span> | 
                <span>更新: ${formatDate(doc.updatedAt)}</span>
                ${doc.tags.length > 0 ? `<br><span>标签: ${doc.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}</span>` : ''}
            </div>
            <div class="content">
                ${this.formatContentForHTML(doc)}
            </div>
        </div>
    `).join('')}
</body>
</html>`

    const filename = `multidimensional-notes-export-${formatDate(new Date(), 'YYYY-MM-DD-HH-mm-ss')}.html`
    
    return {
      content: html,
      filename,
      mimeType: 'text/html'
    }
  }

  /**
   * 获取文档类型显示名称
   */
  private getTypeDisplayName(type: string): string {
    const typeMap = {
      [DocumentType.TEXT]: '文本文档',
      [DocumentType.WHITEBOARD]: '白板',
      [DocumentType.MINDMAP]: '思维导图',
      [DocumentType.KANBAN]: '看板'
    }
    return typeMap[type as DocumentType] || type
  }

  /**
   * 格式化内容为HTML
   */
  private formatContentForHTML(doc: BaseDocument): string {
    if (doc.type === DocumentType.TEXT) {
      const textDoc = doc as TextDocument
      if (textDoc.content && textDoc.content.text) {
        return `<pre>${textDoc.content.text.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>`
      }
    }
    return '<p><em>此文档类型的内容无法在HTML中完整显示</em></p>'
  }
}

// 导出单例实例
export const dataExportManager = new DataExportManager()
