/**
 * 存储提供者接口
 * 定义统一的存储操作接口，支持多种存储实现
 */

import { BaseDocument, DocumentFilter, BackupData, BackupResult, RestoreResult } from '@/types'

/**
 * 存储提供者接口
 * 所有存储实现都必须实现此接口
 */
export interface IStorageProvider {
  /**
   * 初始化存储提供者
   * @returns Promise<void>
   */
  initialize(): Promise<void>

  /**
   * 检查存储提供者是否可用
   * @returns boolean 是否可用
   */
  isAvailable(): boolean

  /**
   * 保存文档
   * @param document 要保存的文档
   * @returns Promise<void>
   */
  save(document: BaseDocument): Promise<void>

  /**
   * 加载文档
   * @param id 文档ID
   * @returns Promise<BaseDocument | null> 文档对象或null
   */
  load(id: string): Promise<BaseDocument | null>

  /**
   * 删除文档
   * @param id 文档ID
   * @returns Promise<void>
   */
  delete(id: string): Promise<void>

  /**
   * 列出文档
   * @param filter 过滤条件（可选）
   * @returns Promise<BaseDocument[]> 文档列表
   */
  list(filter?: DocumentFilter): Promise<BaseDocument[]>

  /**
   * 备份所有数据
   * @returns Promise<BackupResult> 备份结果
   */
  backup(): Promise<BackupResult>

  /**
   * 恢复数据
   * @param backupData 备份数据
   * @returns Promise<RestoreResult> 恢复结果
   */
  restore(backupData: BackupData): Promise<RestoreResult>

  /**
   * 获取存储统计信息
   * @returns Promise<StorageStats> 存储统计
   */
  getStats(): Promise<StorageStats>

  /**
   * 清理存储（可选实现）
   * @returns Promise<void>
   */
  cleanup?(): Promise<void>
}

/**
 * 存储统计信息接口
 */
export interface StorageStats {
  totalDocuments: number
  totalSize: number // 字节
  lastModified: Date
  storageType: string
}

/**
 * 存储错误类型
 */
export enum StorageErrorType {
  INITIALIZATION_FAILED = 'initialization_failed',
  PERMISSION_DENIED = 'permission_denied',
  STORAGE_FULL = 'storage_full',
  DOCUMENT_NOT_FOUND = 'document_not_found',
  INVALID_DOCUMENT = 'invalid_document',
  BACKUP_FAILED = 'backup_failed',
  RESTORE_FAILED = 'restore_failed',
  UNKNOWN_ERROR = 'unknown_error'
}

/**
 * 存储错误类
 */
export class StorageError extends Error {
  public readonly type: StorageErrorType
  public readonly originalError?: Error

  constructor(type: StorageErrorType, message: string, originalError?: Error) {
    super(message)
    this.name = 'StorageError'
    this.type = type
    this.originalError = originalError
  }

  /**
   * 创建初始化失败错误
   */
  static initializationFailed(message: string, originalError?: Error): StorageError {
    return new StorageError(StorageErrorType.INITIALIZATION_FAILED, message, originalError)
  }

  /**
   * 创建权限拒绝错误
   */
  static permissionDenied(message: string): StorageError {
    return new StorageError(StorageErrorType.PERMISSION_DENIED, message)
  }

  /**
   * 创建存储空间不足错误
   */
  static storageFull(message: string): StorageError {
    return new StorageError(StorageErrorType.STORAGE_FULL, message)
  }

  /**
   * 创建文档未找到错误
   */
  static documentNotFound(id: string): StorageError {
    return new StorageError(StorageErrorType.DOCUMENT_NOT_FOUND, `文档未找到: ${id}`)
  }

  /**
   * 创建无效文档错误
   */
  static invalidDocument(message: string): StorageError {
    return new StorageError(StorageErrorType.INVALID_DOCUMENT, message)
  }

  /**
   * 创建备份失败错误
   */
  static backupFailed(message: string, originalError?: Error): StorageError {
    return new StorageError(StorageErrorType.BACKUP_FAILED, message, originalError)
  }

  /**
   * 创建恢复失败错误
   */
  static restoreFailed(message: string, originalError?: Error): StorageError {
    return new StorageError(StorageErrorType.RESTORE_FAILED, message, originalError)
  }
}

/**
 * 存储提供者工厂接口
 */
export interface IStorageProviderFactory {
  /**
   * 创建存储提供者
   * @param type 存储类型
   * @param config 配置参数
   * @returns IStorageProvider 存储提供者实例
   */
  createProvider(type: string, config?: any): IStorageProvider

  /**
   * 获取支持的存储类型列表
   * @returns string[] 支持的存储类型
   */
  getSupportedTypes(): string[]

  /**
   * 检查存储类型是否可用
   * @param type 存储类型
   * @returns boolean 是否可用
   */
  isTypeAvailable(type: string): boolean
}

/**
 * 存储事件类型
 */
export enum StorageEventType {
  DOCUMENT_SAVED = 'document_saved',
  DOCUMENT_LOADED = 'document_loaded',
  DOCUMENT_DELETED = 'document_deleted',
  BACKUP_COMPLETED = 'backup_completed',
  RESTORE_COMPLETED = 'restore_completed',
  ERROR_OCCURRED = 'error_occurred'
}

/**
 * 存储事件接口
 */
export interface StorageEvent {
  type: StorageEventType
  timestamp: Date
  data?: any
  error?: StorageError
}

/**
 * 存储事件监听器接口
 */
export interface IStorageEventListener {
  /**
   * 处理存储事件
   * @param event 存储事件
   */
  onStorageEvent(event: StorageEvent): void
}

/**
 * 可观察的存储提供者接口
 */
export interface IObservableStorageProvider extends IStorageProvider {
  /**
   * 添加事件监听器
   * @param listener 事件监听器
   */
  addEventListener(listener: IStorageEventListener): void

  /**
   * 移除事件监听器
   * @param listener 事件监听器
   */
  removeEventListener(listener: IStorageEventListener): void

  /**
   * 触发存储事件
   * @param event 存储事件
   */
  emitEvent(event: StorageEvent): void
}
