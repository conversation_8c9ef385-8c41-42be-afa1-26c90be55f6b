/**
 * File System Access API 存储提供者
 * 使用浏览器的File System Access API将数据保存到用户选择的本地目录
 */

import { BaseDocument, DocumentFilter, BackupData, BackupResult, RestoreResult } from '@/types'
import { IStorageProvider, StorageStats, StorageError, StorageErrorType } from '../interfaces/IStorageProvider'

/**
 * File System Access API 存储提供者类
 */
export class FileSystemProvider implements IStorageProvider {
  private directoryHandle: FileSystemDirectoryHandle | null = null
  private initialized = false
  private readonly DOCUMENTS_FOLDER = 'documents'
  private readonly METADATA_FILE = 'metadata.json'
  private readonly BACKUP_FOLDER = 'backups'

  /**
   * 检查File System Access API是否可用
   */
  static isAvailable(): boolean {
    return typeof window !== 'undefined' && 
           'showDirectoryPicker' in window &&
           'FileSystemDirectoryHandle' in window
  }

  /**
   * 初始化存储提供者
   */
  async initialize(): Promise<void> {
    if (this.initialized && this.directoryHandle) {
      return
    }

    if (!FileSystemProvider.isAvailable()) {
      throw StorageError.initializationFailed('File System Access API 不受支持')
    }

    try {
      // 请求用户选择目录
      this.directoryHandle = await window.showDirectoryPicker({
        mode: 'readwrite',
        startIn: 'documents'
      })

      // 创建必要的子目录
      await this.createDirectoryStructure()
      
      this.initialized = true
      console.log('File System 存储提供者初始化成功')
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw StorageError.permissionDenied('用户取消了目录选择')
        }
        if (error.name === 'NotAllowedError') {
          throw StorageError.permissionDenied('没有访问文件系统的权限')
        }
      }
      throw StorageError.initializationFailed('初始化File System存储失败', error as Error)
    }
  }

  /**
   * 检查存储提供者是否可用
   */
  isAvailable(): boolean {
    return FileSystemProvider.isAvailable() && this.initialized && this.directoryHandle !== null
  }

  /**
   * 保存文档
   */
  async save(document: BaseDocument): Promise<void> {
    await this.ensureInitialized()

    try {
      // 获取文档目录
      const documentsDir = await this.getDocumentsDirectory()
      
      // 创建文档文件
      const fileName = `${document.id}.json`
      const fileHandle = await documentsDir.getFileHandle(fileName, { create: true })
      
      // 写入文档数据
      const writable = await fileHandle.createWritable()
      const documentData = {
        ...document,
        savedAt: new Date().toISOString(),
        version: '1.0'
      }
      
      await writable.write(JSON.stringify(documentData, null, 2))
      await writable.close()

      // 更新元数据
      await this.updateMetadata(document)
      
      console.log(`文档 ${document.id} 已保存到文件系统`)
    } catch (error) {
      console.error('保存文档到文件系统失败:', error)
      throw StorageError.backupFailed(`保存文档失败: ${document.id}`, error as Error)
    }
  }

  /**
   * 加载文档
   */
  async load(id: string): Promise<BaseDocument | null> {
    await this.ensureInitialized()

    try {
      const documentsDir = await this.getDocumentsDirectory()
      const fileName = `${id}.json`
      
      const fileHandle = await documentsDir.getFileHandle(fileName)
      const file = await fileHandle.getFile()
      const content = await file.text()
      
      const documentData = JSON.parse(content)
      
      // 移除文件系统特有的字段
      const { savedAt, version, ...document } = documentData
      
      return document as BaseDocument
    } catch (error) {
      if (error instanceof Error && error.name === 'NotFoundError') {
        return null
      }
      console.error('从文件系统加载文档失败:', error)
      throw StorageError.documentNotFound(id)
    }
  }

  /**
   * 删除文档
   */
  async delete(id: string): Promise<void> {
    await this.ensureInitialized()

    try {
      const documentsDir = await this.getDocumentsDirectory()
      const fileName = `${id}.json`
      
      await documentsDir.removeEntry(fileName)
      
      // 更新元数据
      await this.removeFromMetadata(id)
      
      console.log(`文档 ${id} 已从文件系统删除`)
    } catch (error) {
      if (error instanceof Error && error.name === 'NotFoundError') {
        // 文件不存在，认为删除成功
        return
      }
      console.error('从文件系统删除文档失败:', error)
      throw new Error(`删除文档失败: ${id}`)
    }
  }

  /**
   * 列出文档
   */
  async list(filter?: DocumentFilter): Promise<BaseDocument[]> {
    await this.ensureInitialized()

    try {
      const documentsDir = await this.getDocumentsDirectory()
      const documents: BaseDocument[] = []

      // 遍历文档目录
      for await (const [name, handle] of documentsDir.entries()) {
        if (handle.kind === 'file' && name.endsWith('.json')) {
          try {
            const file = await handle.getFile()
            const content = await file.text()
            const documentData = JSON.parse(content)
            
            // 移除文件系统特有的字段
            const { savedAt, version, ...document } = documentData
            
            // 应用过滤器
            if (this.matchesFilter(document, filter)) {
              documents.push(document as BaseDocument)
            }
          } catch (error) {
            console.warn(`跳过无效文档文件: ${name}`, error)
          }
        }
      }

      return documents
    } catch (error) {
      console.error('列出文档失败:', error)
      throw new Error('列出文档失败')
    }
  }

  /**
   * 备份所有数据
   */
  async backup(): Promise<BackupResult> {
    await this.ensureInitialized()

    try {
      const documents = await this.list()
      const links: any[] = [] // TODO: 实现链接数据的读取
      
      const backupData: BackupData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        documents,
        links,
        metadata: {
          totalDocuments: documents.length,
          totalLinks: links.length,
          documentTypes: this.getDocumentTypeStats(documents),
          exportFormat: 'JSON' as any,
          appVersion: '1.0.0'
        }
      }

      // 保存备份文件
      await this.saveBackupFile(backupData)

      return {
        success: true,
        data: backupData,
        timestamp: new Date(),
        size: JSON.stringify(backupData).length
      }
    } catch (error) {
      console.error('备份失败:', error)
      throw StorageError.backupFailed('创建备份失败', error as Error)
    }
  }

  /**
   * 恢复数据
   */
  async restore(backupData: BackupData): Promise<RestoreResult> {
    await this.ensureInitialized()

    const result: RestoreResult = {
      success: false,
      documentsImported: 0,
      linksImported: 0,
      errors: [],
      warnings: []
    }

    try {
      // 恢复文档
      for (const document of backupData.documents) {
        try {
          await this.save(document)
          result.documentsImported++
        } catch (error) {
          const errorMsg = `恢复文档失败 "${document.title}": ${error instanceof Error ? error.message : '未知错误'}`
          result.errors.push(errorMsg)
        }
      }

      // TODO: 恢复链接数据

      result.success = result.errors.length === 0
      console.log(`数据恢复完成: ${result.documentsImported} 个文档`)
      
      return result
    } catch (error) {
      result.errors.push(`恢复数据失败: ${error instanceof Error ? error.message : '未知错误'}`)
      return result
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStats(): Promise<StorageStats> {
    await this.ensureInitialized()

    try {
      const documents = await this.list()
      const totalSize = documents.reduce((size, doc) => {
        return size + JSON.stringify(doc).length
      }, 0)

      return {
        totalDocuments: documents.length,
        totalSize,
        lastModified: new Date(),
        storageType: 'filesystem'
      }
    } catch (error) {
      console.error('获取存储统计失败:', error)
      throw new Error('获取存储统计失败')
    }
  }

  /**
   * 确保已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize()
    }
  }

  /**
   * 创建目录结构
   */
  private async createDirectoryStructure(): Promise<void> {
    if (!this.directoryHandle) return

    try {
      // 创建文档目录
      await this.directoryHandle.getDirectoryHandle(this.DOCUMENTS_FOLDER, { create: true })
      
      // 创建备份目录
      await this.directoryHandle.getDirectoryHandle(this.BACKUP_FOLDER, { create: true })
      
      console.log('目录结构创建成功')
    } catch (error) {
      console.error('创建目录结构失败:', error)
      throw error
    }
  }

  /**
   * 获取文档目录
   */
  private async getDocumentsDirectory(): Promise<FileSystemDirectoryHandle> {
    if (!this.directoryHandle) {
      throw new Error('目录句柄未初始化')
    }
    
    return await this.directoryHandle.getDirectoryHandle(this.DOCUMENTS_FOLDER, { create: true })
  }

  /**
   * 更新元数据
   */
  private async updateMetadata(document: BaseDocument): Promise<void> {
    // TODO: 实现元数据更新逻辑
    console.log(`更新文档 ${document.id} 的元数据`)
  }

  /**
   * 从元数据中移除文档
   */
  private async removeFromMetadata(id: string): Promise<void> {
    // TODO: 实现元数据移除逻辑
    console.log(`从元数据中移除文档 ${id}`)
  }

  /**
   * 检查文档是否匹配过滤器
   */
  private matchesFilter(document: BaseDocument, filter?: DocumentFilter): boolean {
    if (!filter) return true
    
    // TODO: 实现过滤逻辑
    return true
  }

  /**
   * 获取文档类型统计
   */
  private getDocumentTypeStats(documents: BaseDocument[]): Record<string, number> {
    const stats: Record<string, number> = {}
    documents.forEach(doc => {
      stats[doc.type] = (stats[doc.type] || 0) + 1
    })
    return stats
  }

  /**
   * 保存备份文件
   */
  private async saveBackupFile(backupData: BackupData): Promise<void> {
    if (!this.directoryHandle) return

    try {
      const backupDir = await this.directoryHandle.getDirectoryHandle(this.BACKUP_FOLDER, { create: true })
      const fileName = `backup-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
      const fileHandle = await backupDir.getFileHandle(fileName, { create: true })
      
      const writable = await fileHandle.createWritable()
      await writable.write(JSON.stringify(backupData, null, 2))
      await writable.close()
      
      console.log(`备份文件已保存: ${fileName}`)
    } catch (error) {
      console.error('保存备份文件失败:', error)
      throw error
    }
  }
}
