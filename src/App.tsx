import React, { Suspense } from 'react'
import { Routes, Route } from 'react-router-dom'
import { Layout, Spin } from 'antd'
import { ErrorBoundary } from 'react-error-boundary'

import AppLayout from '@components/layout/AppLayout'
import ErrorFallback from '@components/common/ErrorFallback'
import LoadingSpinner from '@components/common/LoadingSpinner'

// 懒加载页面组件
const HomePage = React.lazy(() => import('@pages/HomePage'))
const DocumentsPage = React.lazy(() => import('@pages/DocumentsPage'))
const SearchPage = React.lazy(() => import('@pages/SearchPage'))
const LinkManagePage = React.lazy(() => import('@pages/LinkManagePage'))
const TextEditorPage = React.lazy(() => import('@pages/TextEditorPage'))
const WhiteboardPage = React.lazy(() => import('@pages/WhiteboardPage'))
const MindmapPage = React.lazy(() => import('@pages/MindmapPage'))
const KanbanPage = React.lazy(() => import('@pages/KanbanPage'))
const GraphPage = React.lazy(() => import('@pages/GraphPage'))
const AnalyticsPage = React.lazy(() => import('@pages/AnalyticsPage'))
const SettingsPage = React.lazy(() => import('@pages/SettingsPage'))
const NotFoundPage = React.lazy(() => import('@pages/NotFoundPage'))

/**
 * 应用主组件
 * 负责路由配置和全局错误处理
 */
const App: React.FC = () => {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        // 错误日志记录
        console.error('应用错误:', error, errorInfo)
        // 这里可以添加错误上报逻辑
      }}
    >
      <Layout className="min-h-screen">
        <Routes>
          <Route path="/" element={<AppLayout />}>
            <Route
              index
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <HomePage />
                </Suspense>
              }
            />
            <Route
              path="documents"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <DocumentsPage />
                </Suspense>
              }
            />
            <Route
              path="search"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <SearchPage />
                </Suspense>
              }
            />
            <Route
              path="links"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <LinkManagePage />
                </Suspense>
              }
            />
            <Route
              path="editor/:id?"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <TextEditorPage />
                </Suspense>
              }
            />
            <Route 
              path="whiteboard/:id?" 
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <WhiteboardPage />
                </Suspense>
              } 
            />
            <Route 
              path="mindmap/:id?" 
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <MindmapPage />
                </Suspense>
              } 
            />
            <Route 
              path="kanban/:id?" 
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <KanbanPage />
                </Suspense>
              } 
            />
            <Route
              path="graph"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <GraphPage />
                </Suspense>
              }
            />
            <Route
              path="analytics"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <AnalyticsPage />
                </Suspense>
              }
            />
            <Route
              path="settings"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <SettingsPage />
                </Suspense>
              }
            />
          </Route>
          <Route 
            path="*" 
            element={
              <Suspense fallback={<LoadingSpinner />}>
                <NotFoundPage />
              </Suspense>
            } 
          />
        </Routes>
      </Layout>
    </ErrorBoundary>
  )
}

export default App
