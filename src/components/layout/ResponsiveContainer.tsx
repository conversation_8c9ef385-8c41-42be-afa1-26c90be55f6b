/**
 * 响应式容器组件
 * 提供自适应的布局容器，支持不同屏幕尺寸的优化显示
 */

import React, { ReactNode } from 'react'
import { useBreakpoints } from '@hooks/useMediaQuery'

/**
 * 响应式容器属性接口
 */
export interface ResponsiveContainerProps {
  /** 子组件 */
  children: ReactNode
  /** 最大宽度 */
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  /** 内边距 */
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  /** 是否居中 */
  centered?: boolean
  /** 自定义类名 */
  className?: string
  /** 移动端特殊样式 */
  mobileClassName?: string
  /** 平板端特殊样式 */
  tabletClassName?: string
  /** 桌面端特殊样式 */
  desktopClassName?: string
}

/**
 * 最大宽度映射
 */
const MAX_WIDTH_MAP = {
  sm: 'max-w-sm',      // 384px
  md: 'max-w-md',      // 448px
  lg: 'max-w-lg',      // 512px
  xl: 'max-w-xl',      // 576px
  '2xl': 'max-w-2xl',  // 672px
  full: 'max-w-full'   // 100%
}

/**
 * 内边距映射
 */
const PADDING_MAP = {
  none: '',
  sm: 'p-2 md:p-4',
  md: 'p-4 md:p-6',
  lg: 'p-6 md:p-8',
  xl: 'p-8 md:p-12'
}

/**
 * 响应式容器组件
 */
const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  maxWidth = 'full',
  padding = 'md',
  centered = true,
  className = '',
  mobileClassName = '',
  tabletClassName = '',
  desktopClassName = ''
}) => {
  const { isMobile, isTablet, isDesktop } = useBreakpoints()

  // 构建基础类名
  const baseClasses = [
    MAX_WIDTH_MAP[maxWidth],
    PADDING_MAP[padding],
    centered ? 'mx-auto' : '',
    'w-full'
  ].filter(Boolean).join(' ')

  // 构建响应式类名
  const responsiveClasses = [
    isMobile ? mobileClassName : '',
    isTablet ? tabletClassName : '',
    isDesktop ? desktopClassName : ''
  ].filter(Boolean).join(' ')

  // 最终类名
  const finalClassName = [baseClasses, responsiveClasses, className]
    .filter(Boolean)
    .join(' ')

  return (
    <div className={finalClassName}>
      {children}
    </div>
  )
}

export default ResponsiveContainer

/**
 * 响应式网格组件
 */
export interface ResponsiveGridProps {
  /** 子组件 */
  children: ReactNode
  /** 列数配置 */
  cols?: {
    mobile?: number
    tablet?: number
    desktop?: number
  }
  /** 间距 */
  gap?: 'sm' | 'md' | 'lg' | 'xl'
  /** 自定义类名 */
  className?: string
}

/**
 * 间距映射
 */
const GAP_MAP = {
  sm: 'gap-2',
  md: 'gap-4',
  lg: 'gap-6',
  xl: 'gap-8'
}

/**
 * 响应式网格组件
 */
export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md',
  className = ''
}) => {
  const { mobile = 1, tablet = 2, desktop = 3 } = cols

  const gridClasses = [
    'grid',
    `grid-cols-${mobile}`,
    `md:grid-cols-${tablet}`,
    `lg:grid-cols-${desktop}`,
    GAP_MAP[gap],
    className
  ].filter(Boolean).join(' ')

  return (
    <div className={gridClasses}>
      {children}
    </div>
  )
}

/**
 * 响应式卡片组件
 */
export interface ResponsiveCardProps {
  /** 子组件 */
  children: ReactNode
  /** 标题 */
  title?: string
  /** 是否显示边框 */
  bordered?: boolean
  /** 是否显示阴影 */
  shadow?: boolean
  /** 内边距 */
  padding?: 'sm' | 'md' | 'lg'
  /** 自定义类名 */
  className?: string
}

/**
 * 卡片内边距映射
 */
const CARD_PADDING_MAP = {
  sm: 'p-3 md:p-4',
  md: 'p-4 md:p-6',
  lg: 'p-6 md:p-8'
}

/**
 * 响应式卡片组件
 */
export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  children,
  title,
  bordered = true,
  shadow = true,
  padding = 'md',
  className = ''
}) => {
  const cardClasses = [
    'bg-white',
    'rounded-lg',
    bordered ? 'border border-gray-200' : '',
    shadow ? 'shadow-sm hover:shadow-md transition-shadow' : '',
    CARD_PADDING_MAP[padding],
    className
  ].filter(Boolean).join(' ')

  return (
    <div className={cardClasses}>
      {title && (
        <div className="mb-4 pb-2 border-b border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        </div>
      )}
      {children}
    </div>
  )
}

/**
 * 响应式堆栈组件
 */
export interface ResponsiveStackProps {
  /** 子组件 */
  children: ReactNode
  /** 方向 */
  direction?: 'vertical' | 'horizontal' | 'responsive'
  /** 间距 */
  spacing?: 'sm' | 'md' | 'lg' | 'xl'
  /** 对齐方式 */
  align?: 'start' | 'center' | 'end' | 'stretch'
  /** 自定义类名 */
  className?: string
}

/**
 * 堆栈间距映射
 */
const STACK_SPACING_MAP = {
  sm: 'space-y-2 space-x-2',
  md: 'space-y-4 space-x-4',
  lg: 'space-y-6 space-x-6',
  xl: 'space-y-8 space-x-8'
}

/**
 * 对齐方式映射
 */
const ALIGN_MAP = {
  start: 'items-start',
  center: 'items-center',
  end: 'items-end',
  stretch: 'items-stretch'
}

/**
 * 响应式堆栈组件
 */
export const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  children,
  direction = 'vertical',
  spacing = 'md',
  align = 'start',
  className = ''
}) => {
  const getDirectionClasses = () => {
    switch (direction) {
      case 'vertical':
        return 'flex flex-col space-y-4'
      case 'horizontal':
        return 'flex flex-row space-x-4'
      case 'responsive':
        return 'flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4'
      default:
        return 'flex flex-col space-y-4'
    }
  }

  const stackClasses = [
    getDirectionClasses(),
    ALIGN_MAP[align],
    className
  ].filter(Boolean).join(' ')

  return (
    <div className={stackClasses}>
      {children}
    </div>
  )
}

/**
 * 响应式分隔符组件
 */
export interface ResponsiveDividerProps {
  /** 方向 */
  orientation?: 'horizontal' | 'vertical'
  /** 是否虚线 */
  dashed?: boolean
  /** 自定义类名 */
  className?: string
}

/**
 * 响应式分隔符组件
 */
export const ResponsiveDivider: React.FC<ResponsiveDividerProps> = ({
  orientation = 'horizontal',
  dashed = false,
  className = ''
}) => {
  const dividerClasses = [
    orientation === 'horizontal' ? 'w-full h-px' : 'h-full w-px',
    'bg-gray-200',
    dashed ? 'border-dashed' : '',
    className
  ].filter(Boolean).join(' ')

  return <div className={dividerClasses} />
}
