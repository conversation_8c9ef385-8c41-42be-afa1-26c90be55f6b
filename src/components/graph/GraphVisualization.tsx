import React, { useRef, useEffect, useState, useCallback } from 'react'
import { Button, Space, Select, Slider, Switch, Card, Tooltip, message } from 'antd'
import { 
  ZoomInOutlined, 
  ZoomOutOutlined,
  FullscreenOutlined,
  SettingOutlined,
  ReloadOutlined,
  FilterOutlined
} from '@ant-design/icons'
import { BaseDocument, DocumentLink, DocumentType, LinkType } from '@/types'

/**
 * 图谱节点接口
 */
interface GraphNode {
  id: string
  label: string
  type: DocumentType
  x: number
  y: number
  radius: number
  color: string
  linkCount: number
  document: BaseDocument
}

/**
 * 图谱边接口
 */
interface GraphEdge {
  id: string
  source: string
  target: string
  type: LinkType
  color: string
  width: number
  link: DocumentLink
}

/**
 * 图谱数据接口
 */
interface GraphData {
  nodes: GraphNode[]
  edges: GraphEdge[]
}

/**
 * 图谱可视化组件属性
 */
interface GraphVisualizationProps {
  documents: BaseDocument[]
  links: DocumentLink[]
  onNodeClick?: (document: BaseDocument) => void
  onNodeDoubleClick?: (document: BaseDocument) => void
  className?: string
}

/**
 * 文档类型颜色映射
 */
const DOCUMENT_TYPE_COLORS = {
  [DocumentType.TEXT]: '#1890ff',
  [DocumentType.WHITEBOARD]: '#52c41a',
  [DocumentType.MINDMAP]: '#722ed1',
  [DocumentType.KANBAN]: '#fa8c16'
}

/**
 * 链接类型颜色映射
 */
const LINK_TYPE_COLORS = {
  [LinkType.REFERENCE]: '#666666',
  [LinkType.RELATED]: '#1890ff',
  [LinkType.PARENT_CHILD]: '#52c41a',
  [LinkType.DEPENDENCY]: '#fa8c16'
}

/**
 * 关系图谱可视化组件
 * 使用Canvas绘制文档关系网络图
 */
const GraphVisualization: React.FC<GraphVisualizationProps> = ({
  documents,
  links,
  onNodeClick,
  onNodeDoubleClick,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  
  const [graphData, setGraphData] = useState<GraphData>({ nodes: [], edges: [] })
  const [viewport, setViewport] = useState({ x: 0, y: 0, zoom: 1 })
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null)
  const [hoveredNodeId, setHoveredNodeId] = useState<string | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  
  // 过滤和显示选项
  const [showLabels, setShowLabels] = useState(true)
  const [filterByType, setFilterByType] = useState<DocumentType | 'all'>('all')
  const [minLinkCount, setMinLinkCount] = useState(0)

  /**
   * 构建图谱数据
   */
  const buildGraphData = useCallback(() => {
    const nodes: GraphNode[] = []
    const edges: GraphEdge[] = []
    
    // 计算每个文档的链接数量
    const linkCounts = new Map<string, number>()
    links.forEach(link => {
      linkCounts.set(link.sourceId, (linkCounts.get(link.sourceId) || 0) + 1)
      linkCounts.set(link.targetId, (linkCounts.get(link.targetId) || 0) + 1)
    })

    // 过滤文档
    const filteredDocuments = documents.filter(doc => {
      if (filterByType !== 'all' && doc.type !== filterByType) {
        return false
      }
      const linkCount = linkCounts.get(doc.id) || 0
      return linkCount >= minLinkCount
    })

    // 创建节点
    filteredDocuments.forEach((doc, index) => {
      const linkCount = linkCounts.get(doc.id) || 0
      const angle = (index / filteredDocuments.length) * 2 * Math.PI
      const radius = Math.min(300, 100 + linkCount * 20)
      
      nodes.push({
        id: doc.id,
        label: doc.title,
        type: doc.type,
        x: 400 + Math.cos(angle) * radius,
        y: 300 + Math.sin(angle) * radius,
        radius: Math.max(8, Math.min(30, 8 + linkCount * 2)),
        color: DOCUMENT_TYPE_COLORS[doc.type] || '#666666',
        linkCount,
        document: doc
      })
    })

    // 创建边
    const nodeIds = new Set(nodes.map(n => n.id))
    links.forEach(link => {
      if (nodeIds.has(link.sourceId) && nodeIds.has(link.targetId)) {
        edges.push({
          id: link.id,
          source: link.sourceId,
          target: link.targetId,
          type: link.type,
          color: LINK_TYPE_COLORS[link.type] || '#666666',
          width: 2,
          link
        })
      }
    })

    setGraphData({ nodes, edges })
    console.log(`图谱数据构建完成: ${nodes.length} 个节点, ${edges.length} 条边`)
  }, [documents, links, filterByType, minLinkCount])

  /**
   * 绘制图谱
   */
  const drawGraph = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 设置画布变换
    ctx.save()
    ctx.translate(viewport.x, viewport.y)
    ctx.scale(viewport.zoom, viewport.zoom)

    // 绘制边
    graphData.edges.forEach(edge => {
      const sourceNode = graphData.nodes.find(n => n.id === edge.source)
      const targetNode = graphData.nodes.find(n => n.id === edge.target)
      
      if (sourceNode && targetNode) {
        ctx.strokeStyle = edge.color
        ctx.lineWidth = edge.width
        ctx.globalAlpha = 0.6
        
        ctx.beginPath()
        ctx.moveTo(sourceNode.x, sourceNode.y)
        ctx.lineTo(targetNode.x, targetNode.y)
        ctx.stroke()
        
        ctx.globalAlpha = 1
      }
    })

    // 绘制节点
    graphData.nodes.forEach(node => {
      const isSelected = selectedNodeId === node.id
      const isHovered = hoveredNodeId === node.id
      
      // 绘制节点圆圈
      ctx.fillStyle = node.color
      ctx.strokeStyle = isSelected ? '#ff4d4f' : (isHovered ? '#40a9ff' : '#ffffff')
      ctx.lineWidth = isSelected ? 3 : (isHovered ? 2 : 1)
      
      ctx.beginPath()
      ctx.arc(node.x, node.y, node.radius, 0, 2 * Math.PI)
      ctx.fill()
      ctx.stroke()

      // 绘制节点标签
      if (showLabels && (isHovered || isSelected || viewport.zoom > 0.8)) {
        ctx.fillStyle = '#000000'
        ctx.font = `${Math.max(10, 12 * viewport.zoom)}px Arial`
        ctx.textAlign = 'center'
        ctx.textBaseline = 'top'
        
        const maxWidth = node.radius * 4
        const text = node.label.length > 20 ? node.label.substring(0, 20) + '...' : node.label
        
        ctx.fillText(text, node.x, node.y + node.radius + 5)
      }

      // 绘制链接数量
      if (node.linkCount > 0 && (isHovered || isSelected)) {
        ctx.fillStyle = '#ffffff'
        ctx.strokeStyle = node.color
        ctx.lineWidth = 1
        
        const badgeRadius = 8
        const badgeX = node.x + node.radius - badgeRadius
        const badgeY = node.y - node.radius + badgeRadius
        
        ctx.beginPath()
        ctx.arc(badgeX, badgeY, badgeRadius, 0, 2 * Math.PI)
        ctx.fill()
        ctx.stroke()
        
        ctx.fillStyle = node.color
        ctx.font = '10px Arial'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillText(node.linkCount.toString(), badgeX, badgeY)
      }
    })

    ctx.restore()
  }, [graphData, viewport, selectedNodeId, hoveredNodeId, showLabels])

  /**
   * 获取鼠标在画布上的坐标
   */
  const getCanvasCoordinates = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return { x: 0, y: 0 }

    const rect = canvas.getBoundingClientRect()
    const x = (event.clientX - rect.left - viewport.x) / viewport.zoom
    const y = (event.clientY - rect.top - viewport.y) / viewport.zoom
    
    return { x, y }
  }, [viewport])

  /**
   * 查找指定坐标的节点
   */
  const findNodeAtPosition = useCallback((x: number, y: number): GraphNode | null => {
    for (const node of graphData.nodes) {
      const distance = Math.sqrt(Math.pow(x - node.x, 2) + Math.pow(y - node.y, 2))
      if (distance <= node.radius) {
        return node
      }
    }
    return null
  }, [graphData.nodes])

  /**
   * 处理鼠标移动
   */
  const handleMouseMove = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const { x, y } = getCanvasCoordinates(event)
    const hoveredNode = findNodeAtPosition(x, y)
    
    setHoveredNodeId(hoveredNode?.id || null)
    
    // 更新鼠标样式
    const canvas = canvasRef.current
    if (canvas) {
      canvas.style.cursor = hoveredNode ? 'pointer' : 'default'
    }
  }, [getCanvasCoordinates, findNodeAtPosition])

  /**
   * 处理鼠标点击
   */
  const handleMouseClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const { x, y } = getCanvasCoordinates(event)
    const clickedNode = findNodeAtPosition(x, y)

    if (clickedNode) {
      setSelectedNodeId(clickedNode.id)
      if (onNodeClick) {
        onNodeClick(clickedNode.document)
      }
    } else {
      setSelectedNodeId(null)
    }
  }, [getCanvasCoordinates, findNodeAtPosition, onNodeClick])

  /**
   * 处理鼠标双击
   */
  const handleMouseDoubleClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const { x, y } = getCanvasCoordinates(event)
    const clickedNode = findNodeAtPosition(x, y)

    if (clickedNode && onNodeDoubleClick) {
      onNodeDoubleClick(clickedNode.document)
    }
  }, [getCanvasCoordinates, findNodeAtPosition, onNodeDoubleClick])

  /**
   * 缩放控制
   */
  const handleZoom = useCallback((delta: number) => {
    setViewport(prev => ({
      ...prev,
      zoom: Math.max(0.1, Math.min(3, prev.zoom + delta))
    }))
  }, [])

  /**
   * 重置视图
   */
  const resetView = useCallback(() => {
    setViewport({ x: 0, y: 0, zoom: 1 })
    setSelectedNodeId(null)
  }, [])

  /**
   * 初始化和数据更新
   */
  useEffect(() => {
    buildGraphData()
  }, [buildGraphData])

  /**
   * 重绘画布
   */
  useEffect(() => {
    drawGraph()
  }, [drawGraph])

  /**
   * 设置画布尺寸
   */
  useEffect(() => {
    const canvas = canvasRef.current
    const container = containerRef.current
    if (!canvas || !container) return

    const resizeCanvas = () => {
      canvas.width = container.clientWidth
      canvas.height = container.clientHeight
      drawGraph()
    }

    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)
    return () => window.removeEventListener('resize', resizeCanvas)
  }, [drawGraph])

  return (
    <div className={`graph-visualization ${className}`}>
      {/* 控制面板 */}
      <Card className="mb-4" size="small">
        <div className="flex flex-wrap items-center gap-4">
          <Space>
            <Button
              icon={<ZoomInOutlined />}
              onClick={() => handleZoom(0.2)}
              size="small"
            >
              放大
            </Button>
            <Button
              icon={<ZoomOutOutlined />}
              onClick={() => handleZoom(-0.2)}
              size="small"
            >
              缩小
            </Button>
            <Button
              icon={<FullscreenOutlined />}
              onClick={resetView}
              size="small"
            >
              重置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={buildGraphData}
              size="small"
            >
              刷新
            </Button>
          </Space>

          <div className="flex items-center gap-2">
            <span className="text-sm">显示标签:</span>
            <Switch
              checked={showLabels}
              onChange={setShowLabels}
              size="small"
            />
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm">文档类型:</span>
            <Select
              value={filterByType}
              onChange={setFilterByType}
              size="small"
              style={{ width: 120 }}
            >
              <Select.Option value="all">全部</Select.Option>
              <Select.Option value={DocumentType.TEXT}>文本</Select.Option>
              <Select.Option value={DocumentType.WHITEBOARD}>白板</Select.Option>
              <Select.Option value={DocumentType.MINDMAP}>思维导图</Select.Option>
              <Select.Option value={DocumentType.KANBAN}>看板</Select.Option>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm">最小链接数:</span>
            <Slider
              min={0}
              max={10}
              value={minLinkCount}
              onChange={setMinLinkCount}
              style={{ width: 100 }}
            />
            <span className="text-sm w-4">{minLinkCount}</span>
          </div>
        </div>
      </Card>

      {/* 图谱画布 */}
      <div 
        ref={containerRef}
        className="border border-gray-300 rounded-lg overflow-hidden bg-white"
        style={{ height: 600 }}
      >
        <canvas
          ref={canvasRef}
          onMouseMove={handleMouseMove}
          onClick={handleMouseClick}
          onDoubleClick={handleMouseDoubleClick}
        />
      </div>

      {/* 图例 */}
      <Card className="mt-4" size="small" title="图例">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="text-sm font-semibold mb-2">文档类型</h4>
            <div className="space-y-1">
              {Object.entries(DOCUMENT_TYPE_COLORS).map(([type, color]) => (
                <div key={type} className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: color }}
                  />
                  <span className="text-sm">
                    {type === DocumentType.TEXT && '文本'}
                    {type === DocumentType.WHITEBOARD && '白板'}
                    {type === DocumentType.MINDMAP && '思维导图'}
                    {type === DocumentType.KANBAN && '看板'}
                  </span>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-semibold mb-2">链接类型</h4>
            <div className="space-y-1">
              {Object.entries(LINK_TYPE_COLORS).map(([type, color]) => (
                <div key={type} className="flex items-center gap-2">
                  <div 
                    className="w-8 h-0.5" 
                    style={{ backgroundColor: color }}
                  />
                  <span className="text-sm">
                    {type === LinkType.REFERENCE && '引用'}
                    {type === LinkType.RELATED && '相关'}
                    {type === LinkType.PARENT_CHILD && '父子'}
                    {type === LinkType.DEPENDENCY && '依赖'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Card>

      {/* 统计信息 */}
      <div className="mt-4 text-sm text-gray-600">
        <p>节点数: {graphData.nodes.length} | 连接数: {graphData.edges.length} | 缩放: {(viewport.zoom * 100).toFixed(0)}%</p>
        {selectedNodeId && (
          <p>已选择: {graphData.nodes.find(n => n.id === selectedNodeId)?.label}</p>
        )}
      </div>
    </div>
  )
}

export default GraphVisualization
