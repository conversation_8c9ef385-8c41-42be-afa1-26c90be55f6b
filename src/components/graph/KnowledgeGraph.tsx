/**
 * 知识图谱可视化组件
 * 实现知识图谱的可视化展示和交互功能
 */

import React, { useEffect, useRef, useCallback, useState } from 'react'
import { Card, Button, Space, Tooltip, Select, Slider, Switch } from 'antd'
import {
  ExpandOutlined,
  CompressOutlined,
  ReloadOutlined,
  SettingOutlined,
  SearchOutlined,
  FilterOutlined
} from '@ant-design/icons'
import { BaseDocument, DocumentType } from '@/types'
import { DocumentLink } from '@components/link/LinkCreator'
import { CrossFormatReference } from '@services/crossFormat/CrossFormatLinkManager'

const { Option } = Select

/**
 * 图谱节点接口
 */
export interface GraphNode {
  id: string
  label: string
  type: DocumentType
  size: number
  color: string
  x?: number
  y?: number
  document: BaseDocument
}

/**
 * 图谱边接口
 */
export interface GraphEdge {
  id: string
  source: string
  target: string
  label?: string
  type: 'link' | 'reference'
  weight: number
  color: string
}

/**
 * 知识图谱组件属性接口
 */
export interface KnowledgeGraphProps {
  /** 文档列表 */
  documents: BaseDocument[]
  /** 文档链接 */
  links: DocumentLink[]
  /** 跨格式引用 */
  references: CrossFormatReference[]
  /** 点击节点回调 */
  onNodeClick?: (document: BaseDocument) => void
  /** 点击边回调 */
  onEdgeClick?: (link: DocumentLink | CrossFormatReference) => void
  /** 样式类名 */
  className?: string
}

/**
 * 文档类型颜色映射
 */
const DOCUMENT_TYPE_COLORS = {
  [DocumentType.TEXT]: '#1890ff',
  [DocumentType.WHITEBOARD]: '#52c41a',
  [DocumentType.MINDMAP]: '#fa8c16',
  [DocumentType.KANBAN]: '#722ed1'
}

/**
 * 知识图谱组件
 */
const KnowledgeGraph: React.FC<KnowledgeGraphProps> = ({
  documents,
  links,
  references,
  onNodeClick,
  onEdgeClick,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  
  const [nodes, setNodes] = useState<GraphNode[]>([])
  const [edges, setEdges] = useState<GraphEdge[]>([])
  const [selectedNode, setSelectedNode] = useState<string | null>(null)
  const [zoom, setZoom] = useState(1)
  const [pan, setPan] = useState({ x: 0, y: 0 })
  const [showLabels, setShowLabels] = useState(true)
  const [filterType, setFilterType] = useState<DocumentType | 'all'>('all')
  const [nodeSize, setNodeSize] = useState(20)

  /**
   * 初始化图谱数据
   */
  useEffect(() => {
    const graphNodes: GraphNode[] = documents.map(doc => ({
      id: doc.id,
      label: doc.title,
      type: doc.type,
      size: nodeSize,
      color: DOCUMENT_TYPE_COLORS[doc.type],
      document: doc
    }))

    const graphEdges: GraphEdge[] = []

    // 添加文档链接边
    links.forEach(link => {
      graphEdges.push({
        id: link.id,
        source: link.sourceId,
        target: link.targetId,
        label: link.label,
        type: 'link',
        weight: 1,
        color: '#999'
      })
    })

    // 添加跨格式引用边
    references.forEach(ref => {
      graphEdges.push({
        id: ref.id,
        source: ref.sourceDocumentId,
        target: ref.targetDocumentId,
        label: ref.referenceType,
        type: 'reference',
        weight: 2,
        color: '#ff7875'
      })
    })

    setNodes(graphNodes)
    setEdges(graphEdges)
  }, [documents, links, references, nodeSize])

  /**
   * 力导向布局算法
   */
  const applyForceLayout = useCallback(() => {
    const width = canvasRef.current?.width || 800
    const height = canvasRef.current?.height || 600
    
    // 初始化节点位置
    const layoutNodes = nodes.map(node => ({
      ...node,
      x: node.x || Math.random() * width,
      y: node.y || Math.random() * height,
      vx: 0,
      vy: 0
    }))

    // 力导向布局参数
    const iterations = 100
    const repulsionStrength = 1000
    const attractionStrength = 0.01
    const damping = 0.9

    for (let i = 0; i < iterations; i++) {
      // 计算排斥力
      for (let j = 0; j < layoutNodes.length; j++) {
        for (let k = j + 1; k < layoutNodes.length; k++) {
          const nodeA = layoutNodes[j]
          const nodeB = layoutNodes[k]
          
          const dx = nodeB.x! - nodeA.x!
          const dy = nodeB.y! - nodeA.y!
          const distance = Math.sqrt(dx * dx + dy * dy) || 1
          
          const force = repulsionStrength / (distance * distance)
          const fx = (dx / distance) * force
          const fy = (dy / distance) * force
          
          nodeA.vx -= fx
          nodeA.vy -= fy
          nodeB.vx += fx
          nodeB.vy += fy
        }
      }

      // 计算吸引力
      edges.forEach(edge => {
        const sourceNode = layoutNodes.find(n => n.id === edge.source)
        const targetNode = layoutNodes.find(n => n.id === edge.target)
        
        if (sourceNode && targetNode) {
          const dx = targetNode.x! - sourceNode.x!
          const dy = targetNode.y! - sourceNode.y!
          const distance = Math.sqrt(dx * dx + dy * dy) || 1
          
          const force = attractionStrength * distance * edge.weight
          const fx = (dx / distance) * force
          const fy = (dy / distance) * force
          
          sourceNode.vx += fx
          sourceNode.vy += fy
          targetNode.vx -= fx
          targetNode.vy -= fy
        }
      })

      // 更新位置
      layoutNodes.forEach(node => {
        node.vx *= damping
        node.vy *= damping
        node.x! += node.vx
        node.y! += node.vy
        
        // 边界约束
        node.x! = Math.max(node.size, Math.min(width - node.size, node.x!))
        node.y! = Math.max(node.size, Math.min(height - node.size, node.y!))
      })
    }

    setNodes(layoutNodes)
  }, [nodes, edges])

  /**
   * 绘制图谱
   */
  const drawGraph = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 应用变换
    ctx.save()
    ctx.translate(pan.x, pan.y)
    ctx.scale(zoom, zoom)

    // 绘制边
    edges.forEach(edge => {
      const sourceNode = nodes.find(n => n.id === edge.source)
      const targetNode = nodes.find(n => n.id === edge.target)
      
      if (sourceNode && targetNode && sourceNode.x !== undefined && targetNode.x !== undefined) {
        ctx.strokeStyle = edge.color
        ctx.lineWidth = edge.weight
        ctx.setLineDash(edge.type === 'reference' ? [5, 5] : [])
        
        ctx.beginPath()
        ctx.moveTo(sourceNode.x, sourceNode.y!)
        ctx.lineTo(targetNode.x, targetNode.y!)
        ctx.stroke()
        
        // 绘制箭头
        const angle = Math.atan2(targetNode.y! - sourceNode.y!, targetNode.x! - sourceNode.x!)
        const arrowLength = 10
        const arrowAngle = Math.PI / 6
        
        ctx.beginPath()
        ctx.moveTo(
          targetNode.x! - arrowLength * Math.cos(angle - arrowAngle),
          targetNode.y! - arrowLength * Math.sin(angle - arrowAngle)
        )
        ctx.lineTo(targetNode.x!, targetNode.y!)
        ctx.lineTo(
          targetNode.x! - arrowLength * Math.cos(angle + arrowAngle),
          targetNode.y! - arrowLength * Math.sin(angle + arrowAngle)
        )
        ctx.stroke()
      }
    })

    // 绘制节点
    nodes.forEach(node => {
      if (filterType !== 'all' && node.type !== filterType) return
      if (node.x === undefined || node.y === undefined) return

      // 节点圆圈
      ctx.fillStyle = node.color
      ctx.strokeStyle = selectedNode === node.id ? '#000' : '#fff'
      ctx.lineWidth = selectedNode === node.id ? 3 : 2
      
      ctx.beginPath()
      ctx.arc(node.x, node.y, node.size, 0, 2 * Math.PI)
      ctx.fill()
      ctx.stroke()

      // 节点标签
      if (showLabels) {
        ctx.fillStyle = '#333'
        ctx.font = '12px Arial'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        
        const maxWidth = node.size * 4
        const text = node.label.length > 10 ? node.label.substring(0, 10) + '...' : node.label
        
        ctx.fillText(text, node.x, node.y + node.size + 15, maxWidth)
      }
    })

    ctx.restore()
  }, [nodes, edges, zoom, pan, selectedNode, showLabels, filterType])

  /**
   * 处理画布点击
   */
  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = (event.clientX - rect.left - pan.x) / zoom
    const y = (event.clientY - rect.top - pan.y) / zoom

    // 查找点击的节点
    const clickedNode = nodes.find(node => {
      if (node.x === undefined || node.y === undefined) return false
      const distance = Math.sqrt((x - node.x) ** 2 + (y - node.y) ** 2)
      return distance <= node.size
    })

    if (clickedNode) {
      setSelectedNode(clickedNode.id)
      onNodeClick?.(clickedNode.document)
    } else {
      setSelectedNode(null)
    }
  }, [nodes, pan, zoom, onNodeClick])

  /**
   * 重置视图
   */
  const resetView = useCallback(() => {
    setZoom(1)
    setPan({ x: 0, y: 0 })
  }, [])

  /**
   * 自动布局
   */
  const autoLayout = useCallback(() => {
    applyForceLayout()
  }, [applyForceLayout])

  // 初始化画布尺寸
  useEffect(() => {
    const canvas = canvasRef.current
    const container = containerRef.current
    
    if (canvas && container) {
      canvas.width = container.clientWidth
      canvas.height = container.clientHeight
    }
  }, [])

  // 绘制图谱
  useEffect(() => {
    drawGraph()
  }, [drawGraph])

  // 初始布局
  useEffect(() => {
    if (nodes.length > 0 && nodes.every(n => n.x === undefined)) {
      setTimeout(autoLayout, 100)
    }
  }, [nodes, autoLayout])

  return (
    <Card
      title="知识图谱"
      className={`knowledge-graph ${className}`}
      extra={
        <Space>
          <Tooltip title="显示标签">
            <Switch
              checked={showLabels}
              onChange={setShowLabels}
              size="small"
            />
          </Tooltip>
          
          <Select
            value={filterType}
            onChange={setFilterType}
            size="small"
            style={{ width: 100 }}
          >
            <Option value="all">全部</Option>
            <Option value={DocumentType.TEXT}>文本</Option>
            <Option value={DocumentType.WHITEBOARD}>白板</Option>
            <Option value={DocumentType.MINDMAP}>思维导图</Option>
            <Option value={DocumentType.KANBAN}>看板</Option>
          </Select>

          <Tooltip title="节点大小">
            <Slider
              value={nodeSize}
              onChange={setNodeSize}
              min={10}
              max={40}
              style={{ width: 80 }}
            />
          </Tooltip>

          <Tooltip title="自动布局">
            <Button size="small" icon={<ReloadOutlined />} onClick={autoLayout} />
          </Tooltip>

          <Tooltip title="重置视图">
            <Button size="small" icon={<CompressOutlined />} onClick={resetView} />
          </Tooltip>
        </Space>
      }
    >
      <div
        ref={containerRef}
        className="graph-container"
        style={{ width: '100%', height: '600px', position: 'relative' }}
      >
        <canvas
          ref={canvasRef}
          onClick={handleCanvasClick}
          style={{ cursor: 'pointer', border: '1px solid #d9d9d9' }}
        />
        
        {selectedNode && (
          <div className="graph-info" style={{
            position: 'absolute',
            top: 10,
            left: 10,
            background: 'rgba(255, 255, 255, 0.9)',
            padding: '8px 12px',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
          }}>
            <div>已选择: {nodes.find(n => n.id === selectedNode)?.label}</div>
            <div>类型: {nodes.find(n => n.id === selectedNode)?.type}</div>
          </div>
        )}
      </div>
    </Card>
  )
}

export default KnowledgeGraph
