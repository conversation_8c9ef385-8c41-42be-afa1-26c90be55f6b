/**
 * 文档分类和标签管理组件
 * 提供文档的分类管理和标签系统，支持文档的组织和分组
 */

import React, { useState, useEffect, useCallback } from 'react'
import {
  Tree,
  Input,
  Button,
  Modal,
  Form,
  Select,
  Tag,
  Space,
  Dropdown,
  message,
  Popconfirm,
  Tooltip,
  Badge
} from 'antd'
import {
  FolderOutlined,
  FolderOpenOutlined,
  TagOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  SearchOutlined,
  SettingOutlined
} from '@ant-design/icons'
import type { DataNode } from 'antd/es/tree'
import { BaseDocument, DocumentType } from '@/types'
import { documentManager } from '@services/document/documentManager'
import TagManager from './TagManager'

const { Search } = Input

/**
 * 文档分类接口
 */
export interface DocumentCategory {
  id: string
  name: string
  parentId?: string
  children?: DocumentCategory[]
  documentCount: number
  color?: string
  description?: string
}

/**
 * 标签接口
 */
export interface DocumentTag {
  name: string
  count: number
  color?: string
}

/**
 * 组件属性接口
 */
export interface DocumentCategoriesProps {
  /** 当前选中的分类ID */
  selectedCategoryId?: string
  /** 当前选中的标签 */
  selectedTags?: string[]
  /** 分类选择回调 */
  onCategorySelect?: (categoryId?: string) => void
  /** 标签选择回调 */
  onTagSelect?: (tags: string[]) => void
  /** 样式类名 */
  className?: string
}

/**
 * 文档分类和标签管理组件
 */
const DocumentCategories: React.FC<DocumentCategoriesProps> = ({
  selectedCategoryId,
  selectedTags = [],
  onCategorySelect,
  onTagSelect,
  className = ''
}) => {
  const [categories, setCategories] = useState<DocumentCategory[]>([])
  const [tags, setTags] = useState<DocumentTag[]>([])
  const [loading, setLoading] = useState(true)
  const [searchValue, setSearchValue] = useState('')
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([])
  const [categoryModalVisible, setCategoryModalVisible] = useState(false)
  const [editingCategory, setEditingCategory] = useState<DocumentCategory | null>(null)
  const [categoryForm] = Form.useForm()
  const [tagManagerVisible, setTagManagerVisible] = useState(false)

  /**
   * 加载分类和标签数据
   */
  const loadData = useCallback(async () => {
    try {
      setLoading(true)

      // 获取标签统计数据
      const tagsWithCount = await documentManager.getTagsWithCount()

      // 转换为DocumentTag格式
      const tagsData: DocumentTag[] = tagsWithCount.map((tag, index) => ({
        name: tag.name,
        count: tag.count,
        color: getTagColor(index) // 根据索引分配颜色
      }))

      // 暂时使用模拟的分类数据，因为还没有实现分类系统
      const mockCategories: DocumentCategory[] = [
        {
          id: 'all',
          name: '全部文档',
          documentCount: tagsWithCount.reduce((sum, tag) => sum + tag.count, 0),
          color: '#1890ff'
        }
      ]

      setCategories(mockCategories)
      setTags(tagsData)

      // 默认展开所有分类
      const allKeys = mockCategories.map(cat => cat.id)
      setExpandedKeys(allKeys)

    } catch (error) {
      console.error('加载分类和标签失败:', error)
      message.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * 根据索引获取标签颜色
   */
  const getTagColor = (index: number): string => {
    const colors = [
      '#1890ff', '#722ed1', '#13c2c2', '#fa8c16', '#52c41a',
      '#eb2f96', '#faad14', '#f5222d', '#2f54eb', '#fa541c'
    ]
    return colors[index % colors.length]
  }

  /**
   * 初始化加载
   */
  useEffect(() => {
    loadData()
  }, [loadData])

  /**
   * 转换分类数据为树形结构
   */
  const convertToTreeData = (categories: DocumentCategory[]): DataNode[] => {
    return categories.map(category => ({
      key: category.id,
      title: (
        <div className="flex items-center justify-between group">
          <div className="flex items-center space-x-2">
            <span style={{ color: category.color }}>{category.name}</span>
            <Badge count={category.documentCount} size="small" />
          </div>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'edit',
                  label: '编辑',
                  icon: <EditOutlined />,
                  onClick: () => handleEditCategory(category)
                },
                {
                  key: 'delete',
                  label: '删除',
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: () => handleDeleteCategory(category)
                }
              ]
            }}
            trigger={['click']}
          >
            <Button
              type="text"
              size="small"
              icon={<MoreOutlined />}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
            />
          </Dropdown>
        </div>
      ),
      icon: category.children?.length ? <FolderOpenOutlined /> : <FolderOutlined />,
      children: category.children ? convertToTreeData(category.children) : undefined
    }))
  }

  /**
   * 处理分类选择
   */
  const handleCategorySelect = (selectedKeys: React.Key[]) => {
    const categoryId = selectedKeys[0] as string
    onCategorySelect?.(categoryId === selectedCategoryId ? undefined : categoryId)
  }

  /**
   * 处理标签选择
   */
  const handleTagClick = (tagName: string) => {
    const newSelectedTags = selectedTags.includes(tagName)
      ? selectedTags.filter(tag => tag !== tagName)
      : [...selectedTags, tagName]
    onTagSelect?.(newSelectedTags)
  }

  /**
   * 创建新分类
   */
  const handleCreateCategory = () => {
    setEditingCategory(null)
    categoryForm.resetFields()
    setCategoryModalVisible(true)
  }

  /**
   * 编辑分类
   */
  const handleEditCategory = (category: DocumentCategory) => {
    setEditingCategory(category)
    categoryForm.setFieldsValue({
      name: category.name,
      parentId: category.parentId,
      color: category.color,
      description: category.description
    })
    setCategoryModalVisible(true)
  }

  /**
   * 删除分类
   */
  const handleDeleteCategory = (category: DocumentCategory) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除分类"${category.name}"吗？该分类下的文档将移动到未分类。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // await documentManager.deleteCategory(category.id)
          message.success('分类删除成功')
          loadData()
        } catch (error) {
          console.error('删除分类失败:', error)
          message.error('删除分类失败')
        }
      }
    })
  }

  /**
   * 保存分类
   */
  const handleSaveCategory = async () => {
    try {
      const values = await categoryForm.validateFields()
      
      if (editingCategory) {
        // await documentManager.updateCategory(editingCategory.id, values)
        message.success('分类更新成功')
      } else {
        // await documentManager.createCategory(values)
        message.success('分类创建成功')
      }
      
      setCategoryModalVisible(false)
      loadData()
    } catch (error) {
      console.error('保存分类失败:', error)
      message.error('保存分类失败')
    }
  }

  /**
   * 获取父分类选项
   */
  const getParentCategoryOptions = (categories: DocumentCategory[], excludeId?: string): any[] => {
    return categories.reduce((options: any[], category) => {
      if (category.id !== excludeId) {
        options.push({
          label: category.name,
          value: category.id
        })
        if (category.children) {
          options.push(...getParentCategoryOptions(category.children, excludeId))
        }
      }
      return options
    }, [])
  }

  const treeData = convertToTreeData(categories)
  const parentOptions = getParentCategoryOptions(categories, editingCategory?.id)

  return (
    <div className={`document-categories ${className}`}>
      {/* 分类部分 */}
      <div className="categories-section mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-700">文档分类</h3>
          <Button
            type="text"
            size="small"
            icon={<PlusOutlined />}
            onClick={handleCreateCategory}
          >
            新建
          </Button>
        </div>
        
        <Search
          placeholder="搜索分类..."
          size="small"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          className="mb-3"
        />
        
        <Tree
          treeData={treeData}
          selectedKeys={selectedCategoryId ? [selectedCategoryId] : []}
          expandedKeys={expandedKeys}
          onSelect={handleCategorySelect}
          onExpand={setExpandedKeys}
          showIcon
          className="category-tree"
        />
      </div>

      {/* 标签部分 */}
      <div className="tags-section">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-700">标签</h3>
          <Tooltip title="管理标签">
            <Button
              type="text"
              size="small"
              icon={<SettingOutlined />}
              onClick={() => setTagManagerVisible(true)}
            />
          </Tooltip>
        </div>
        
        <div className="flex flex-wrap gap-2">
          {tags.map(tag => (
            <Tag
              key={tag.name}
              color={selectedTags.includes(tag.name) ? tag.color : undefined}
              className={`cursor-pointer transition-all ${
                selectedTags.includes(tag.name) ? 'ring-2 ring-blue-200' : 'hover:scale-105'
              }`}
              onClick={() => handleTagClick(tag.name)}
            >
              {tag.name} ({tag.count})
            </Tag>
          ))}
        </div>
      </div>

      {/* 分类编辑对话框 */}
      <Modal
        title={editingCategory ? '编辑分类' : '新建分类'}
        open={categoryModalVisible}
        onOk={handleSaveCategory}
        onCancel={() => setCategoryModalVisible(false)}
        okText="保存"
        cancelText="取消"
      >
        <Form form={categoryForm} layout="vertical">
          <Form.Item
            name="name"
            label="分类名称"
            rules={[
              { required: true, message: '请输入分类名称' },
              { max: 50, message: '名称长度不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>
          
          <Form.Item name="parentId" label="父分类">
            <Select
              placeholder="选择父分类（可选）"
              allowClear
              options={parentOptions}
            />
          </Form.Item>
          
          <Form.Item name="color" label="分类颜色">
            <Select placeholder="选择颜色">
              <Select.Option value="#1890ff">蓝色</Select.Option>
              <Select.Option value="#52c41a">绿色</Select.Option>
              <Select.Option value="#faad14">橙色</Select.Option>
              <Select.Option value="#f5222d">红色</Select.Option>
              <Select.Option value="#722ed1">紫色</Select.Option>
              <Select.Option value="#13c2c2">青色</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item name="description" label="描述">
            <Input.TextArea
              placeholder="分类描述（可选）"
              rows={3}
              maxLength={200}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 标签管理对话框 */}
      <TagManager
        visible={tagManagerVisible}
        onClose={() => setTagManagerVisible(false)}
        onTagsUpdated={loadData}
      />
    </div>
  )
}

export default DocumentCategories
