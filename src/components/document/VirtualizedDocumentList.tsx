/**
 * 虚拟化文档列表组件
 * 使用虚拟滚动技术优化大量文档的显示性能
 */

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { FixedSizeList as List } from 'react-window'
import { 
  Card, 
  Tag, 
  Button, 
  Dropdown, 
  Skeleton,
  Empty,
  Input,
  Select,
  Space
} from 'antd'
import {
  MoreOutlined,
  FileTextOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined,
  ClockCircleOutlined,
  SearchOutlined
} from '@ant-design/icons'
import { BaseDocument, DocumentType, SearchOptions } from '@/types'
import { documentManager } from '@services/document/documentManager'
import { formatRelativeTime } from '@utils/index'
import DocumentContextMenu from './DocumentContextMenu'

const { Search } = Input
const { Option } = Select

/**
 * 虚拟化文档列表组件属性接口
 */
export interface VirtualizedDocumentListProps {
  /** 列表高度 */
  height?: number
  /** 每项高度 */
  itemHeight?: number
  /** 每页加载数量 */
  pageSize?: number
  /** 文档选择回调 */
  onDocumentSelect?: (document: BaseDocument) => void
  /** 样式类名 */
  className?: string
  /** 是否显示搜索 */
  showSearch?: boolean
  /** 是否显示筛选 */
  showFilters?: boolean
}

/**
 * 文档项组件属性接口
 */
interface DocumentItemProps {
  index: number
  style: React.CSSProperties
  data: {
    documents: BaseDocument[]
    onDocumentSelect?: (document: BaseDocument) => void
    onRefresh: () => void
  }
}

/**
 * 文档项组件
 */
const DocumentItem: React.FC<DocumentItemProps> = ({ index, style, data }) => {
  const { documents, onDocumentSelect, onRefresh } = data
  const document = documents[index]

  if (!document) {
    return (
      <div style={style} className="p-2">
        <Skeleton active paragraph={{ rows: 2 }} />
      </div>
    )
  }

  /**
   * 获取文档类型图标
   */
  const getDocumentIcon = (type: DocumentType) => {
    const icons = {
      [DocumentType.TEXT]: <FileTextOutlined className="text-blue-500" />,
      [DocumentType.WHITEBOARD]: <BgColorsOutlined className="text-green-500" />,
      [DocumentType.MINDMAP]: <NodeIndexOutlined className="text-purple-500" />,
      [DocumentType.KANBAN]: <ProjectOutlined className="text-orange-500" />
    }
    return icons[type]
  }

  /**
   * 获取文档类型名称
   */
  const getDocumentTypeName = (type: DocumentType) => {
    const names = {
      [DocumentType.TEXT]: '文本笔记',
      [DocumentType.WHITEBOARD]: '白板',
      [DocumentType.MINDMAP]: '思维导图',
      [DocumentType.KANBAN]: '看板'
    }
    return names[type]
  }

  /**
   * 处理文档点击
   */
  const handleDocumentClick = () => {
    onDocumentSelect?.(document)
  }

  return (
    <div style={style} className="p-2">
      <Card
        hoverable
        size="small"
        className="document-item-card"
        onClick={handleDocumentClick}
        actions={[
          <Dropdown
            key="more"
            dropdownRender={() => (
              <DocumentContextMenu
                document={document}
                onRefresh={onRefresh}
              />
            )}
            trigger={['click']}
          >
            <Button type="text" icon={<MoreOutlined />} size="small" />
          </Dropdown>
        ]}
      >
        <div className="flex items-start space-x-3">
          {/* 文档图标 */}
          <div className="flex-shrink-0 mt-1">
            {getDocumentIcon(document.type)}
          </div>
          
          {/* 文档信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <h4 className="text-sm font-medium text-gray-900 truncate">
                {document.title}
              </h4>
              <span className="text-xs text-gray-400 ml-2">
                {getDocumentTypeName(document.type)}
              </span>
            </div>
            
            {/* 标签 */}
            {document.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-2">
                {document.tags.slice(0, 3).map(tag => (
                  <Tag key={tag} size="small" color="blue">
                    {tag}
                  </Tag>
                ))}
                {document.tags.length > 3 && (
                  <Tag size="small">+{document.tags.length - 3}</Tag>
                )}
              </div>
            )}
            
            {/* 时间信息 */}
            <div className="flex items-center text-xs text-gray-400">
              <ClockCircleOutlined className="mr-1" />
              {formatRelativeTime(document.updatedAt)}
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}

/**
 * 虚拟化文档列表组件
 */
const VirtualizedDocumentList: React.FC<VirtualizedDocumentListProps> = ({
  height = 600,
  itemHeight = 120,
  pageSize = 50,
  onDocumentSelect,
  className = '',
  showSearch = true,
  showFilters = true
}) => {
  const [documents, setDocuments] = useState<BaseDocument[]>([])
  const [loading, setLoading] = useState(true)
  const [hasMore, setHasMore] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedType, setSelectedType] = useState<DocumentType | 'all'>('all')
  const [sortBy, setSortBy] = useState<'updatedAt' | 'createdAt' | 'title'>('updatedAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const listRef = useRef<List>(null)

  /**
   * 加载文档数据
   */
  const loadDocuments = useCallback(async (reset = false) => {
    try {
      if (reset) {
        setLoading(true)
        setDocuments([])
      }

      const searchOptions: SearchOptions = {
        types: selectedType === 'all' ? undefined : [selectedType],
        sortBy,
        sortOrder,
        limit: pageSize,
        offset: reset ? 0 : documents.length
      }

      let results: BaseDocument[]
      if (searchQuery.trim()) {
        results = await documentManager.searchDocuments(searchQuery, searchOptions)
      } else {
        results = await documentManager.searchDocuments('', searchOptions)
      }

      if (reset) {
        setDocuments(results)
      } else {
        setDocuments(prev => [...prev, ...results])
      }

      setHasMore(results.length === pageSize)
    } catch (error) {
      console.error('加载文档列表失败:', error)
    } finally {
      setLoading(false)
    }
  }, [searchQuery, selectedType, sortBy, sortOrder, pageSize, documents.length])

  /**
   * 重置并重新加载
   */
  const refreshDocuments = useCallback(() => {
    loadDocuments(true)
  }, [loadDocuments])

  /**
   * 初始化加载
   */
  useEffect(() => {
    refreshDocuments()
  }, [searchQuery, selectedType, sortBy, sortOrder])

  /**
   * 无限滚动处理
   */
  const handleScroll = useCallback(({ scrollOffset, scrollUpdateWasRequested }: any) => {
    if (scrollUpdateWasRequested || loading || !hasMore) return

    const scrollHeight = documents.length * itemHeight
    const clientHeight = height
    const scrollTop = scrollOffset

    // 当滚动到底部附近时加载更多
    if (scrollTop + clientHeight >= scrollHeight - itemHeight * 5) {
      loadDocuments(false)
    }
  }, [documents.length, itemHeight, height, loading, hasMore, loadDocuments])

  /**
   * 搜索处理
   */
  const handleSearch = useCallback((value: string) => {
    setSearchQuery(value)
  }, [])

  /**
   * 列表数据
   */
  const listData = useMemo(() => ({
    documents,
    onDocumentSelect,
    onRefresh: refreshDocuments
  }), [documents, onDocumentSelect, refreshDocuments])

  /**
   * 渲染空状态
   */
  if (!loading && documents.length === 0) {
    return (
      <div className={`virtualized-document-list ${className}`}>
        {/* 工具栏 */}
        {(showSearch || showFilters) && (
          <div className="document-list-toolbar mb-4 space-y-4">
            {showSearch && (
              <Search
                placeholder="搜索文档..."
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                onSearch={handleSearch}
              />
            )}
            
            {showFilters && (
              <Space>
                <Select
                  value={selectedType}
                  onChange={setSelectedType}
                  className="w-32"
                  placeholder="文档类型"
                >
                  <Option value="all">全部类型</Option>
                  <Option value={DocumentType.TEXT}>文本笔记</Option>
                  <Option value={DocumentType.WHITEBOARD}>白板</Option>
                  <Option value={DocumentType.MINDMAP}>思维导图</Option>
                  <Option value={DocumentType.KANBAN}>看板</Option>
                </Select>

                <Select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(value) => {
                    const [field, order] = value.split('-')
                    setSortBy(field as any)
                    setSortOrder(order as any)
                  }}
                  className="w-40"
                  placeholder="排序方式"
                >
                  <Option value="updatedAt-desc">最近修改</Option>
                  <Option value="createdAt-desc">最近创建</Option>
                  <Option value="title-asc">标题 A-Z</Option>
                  <Option value="title-desc">标题 Z-A</Option>
                </Select>
              </Space>
            )}
          </div>
        )}
        
        <Empty
          description={searchQuery ? '没有找到匹配的文档' : '暂无文档'}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    )
  }

  return (
    <div className={`virtualized-document-list ${className}`}>
      {/* 工具栏 */}
      {(showSearch || showFilters) && (
        <div className="document-list-toolbar mb-4 space-y-4">
          {showSearch && (
            <Search
              placeholder="搜索文档..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              onSearch={handleSearch}
            />
          )}
          
          {showFilters && (
            <Space>
              <Select
                value={selectedType}
                onChange={setSelectedType}
                className="w-32"
                placeholder="文档类型"
              >
                <Option value="all">全部类型</Option>
                <Option value={DocumentType.TEXT}>文本笔记</Option>
                <Option value={DocumentType.WHITEBOARD}>白板</Option>
                <Option value={DocumentType.MINDMAP}>思维导图</Option>
                <Option value={DocumentType.KANBAN}>看板</Option>
              </Select>

              <Select
                value={`${sortBy}-${sortOrder}`}
                onChange={(value) => {
                  const [field, order] = value.split('-')
                  setSortBy(field as any)
                  setSortOrder(order as any)
                }}
                className="w-40"
                placeholder="排序方式"
              >
                <Option value="updatedAt-desc">最近修改</Option>
                <Option value="createdAt-desc">最近创建</Option>
                <Option value="title-asc">标题 A-Z</Option>
                <Option value="title-desc">标题 Z-A</Option>
              </Select>
            </Space>
          )}
        </div>
      )}

      {/* 虚拟化列表 */}
      <List
        ref={listRef}
        height={height}
        itemCount={documents.length + (hasMore ? 1 : 0)} // 为加载更多预留一个位置
        itemSize={itemHeight}
        itemData={listData}
        onScroll={handleScroll}
        className="virtualized-list"
      >
        {DocumentItem}
      </List>
    </div>
  )
}

export default VirtualizedDocumentList
