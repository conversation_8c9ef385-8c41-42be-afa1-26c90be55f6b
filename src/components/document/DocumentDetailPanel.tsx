/**
 * 文档详情面板组件
 * 显示文档的详细信息、元数据和操作选项
 */

import React, { useState, useCallback } from 'react'
import {
  Drawer,
  Descriptions,
  Tag,
  Button,
  Space,
  Divider,
  Typography,
  Input,
  Form,
  Select,
  message,
  Popconfirm,
  Tooltip,
  Badge
} from 'antd'
import {
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  ShareAltOutlined,
  StarOutlined,
  StarFilled,
  TagOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  UserOutlined,
  EyeOutlined
} from '@ant-design/icons'
import { BaseDocument, DocumentType } from '@/types'
import { documentManager } from '@services/document/documentManager'
import { formatRelativeTime, formatFileSize } from '@utils/index'

const { Text, Paragraph } = Typography
const { TextArea } = Input

/**
 * 文档详情面板属性接口
 */
export interface DocumentDetailPanelProps {
  /** 是否显示 */
  visible: boolean
  /** 文档数据 */
  document: BaseDocument | null
  /** 关闭回调 */
  onClose: () => void
  /** 编辑回调 */
  onEdit?: (document: BaseDocument) => void
  /** 删除回调 */
  onDelete?: (document: BaseDocument) => void
  /** 复制回调 */
  onCopy?: (document: BaseDocument) => void
  /** 更新回调 */
  onUpdate?: () => void
}

/**
 * 文档详情面板组件
 */
const DocumentDetailPanel: React.FC<DocumentDetailPanelProps> = ({
  visible,
  document,
  onClose,
  onEdit,
  onDelete,
  onCopy,
  onUpdate
}) => {
  const [editing, setEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()

  /**
   * 获取文档类型名称
   */
  const getDocumentTypeName = useCallback((type: DocumentType) => {
    const names = {
      [DocumentType.TEXT]: '文本笔记',
      [DocumentType.WHITEBOARD]: '白板',
      [DocumentType.MINDMAP]: '思维导图',
      [DocumentType.KANBAN]: '看板'
    }
    return names[type]
  }, [])

  /**
   * 获取文档类型颜色
   */
  const getDocumentTypeColor = useCallback((type: DocumentType) => {
    const colors = {
      [DocumentType.TEXT]: 'blue',
      [DocumentType.WHITEBOARD]: 'green',
      [DocumentType.MINDMAP]: 'purple',
      [DocumentType.KANBAN]: 'orange'
    }
    return colors[type]
  }, [])

  /**
   * 处理编辑
   */
  const handleEdit = useCallback(() => {
    if (document) {
      onEdit?.(document)
    }
  }, [document, onEdit])

  /**
   * 处理删除
   */
  const handleDelete = useCallback(() => {
    if (document) {
      onDelete?.(document)
    }
  }, [document, onDelete])

  /**
   * 处理复制
   */
  const handleCopy = useCallback(() => {
    if (document) {
      onCopy?.(document)
    }
  }, [document, onCopy])

  /**
   * 开始编辑元数据
   */
  const startEditing = useCallback(() => {
    if (document) {
      form.setFieldsValue({
        title: document.title,
        tags: document.tags,
        description: document.metadata.description || ''
      })
      setEditing(true)
    }
  }, [document, form])

  /**
   * 保存编辑
   */
  const saveEditing = useCallback(async () => {
    if (!document) return

    try {
      setLoading(true)
      const values = await form.validateFields()
      
      // 更新文档
      await documentManager.updateDocument(document.id, {
        title: values.title,
        tags: values.tags || [],
        metadata: {
          ...document.metadata,
          description: values.description
        }
      })

      message.success('文档信息更新成功')
      setEditing(false)
      onUpdate?.()
    } catch (error) {
      console.error('更新文档失败:', error)
      message.error('更新文档失败')
    } finally {
      setLoading(false)
    }
  }, [document, form, onUpdate])

  /**
   * 取消编辑
   */
  const cancelEditing = useCallback(() => {
    setEditing(false)
    form.resetFields()
  }, [form])

  if (!document) {
    return null
  }

  return (
    <Drawer
      title={
        <div className="flex items-center space-x-2">
          <FileTextOutlined />
          <span>文档详情</span>
        </div>
      }
      placement="right"
      width={480}
      open={visible}
      onClose={onClose}
      extra={
        <Space>
          {!editing && (
            <>
              <Tooltip title="编辑信息">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={startEditing}
                />
              </Tooltip>
              <Tooltip title="编辑文档">
                <Button
                  type="text"
                  icon={<EyeOutlined />}
                  onClick={handleEdit}
                />
              </Tooltip>
              <Tooltip title="复制文档">
                <Button
                  type="text"
                  icon={<CopyOutlined />}
                  onClick={handleCopy}
                />
              </Tooltip>
              <Popconfirm
                title="确定要删除这个文档吗？"
                onConfirm={handleDelete}
                okText="删除"
                cancelText="取消"
                okType="danger"
              >
                <Tooltip title="删除文档">
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                  />
                </Tooltip>
              </Popconfirm>
            </>
          )}
          {editing && (
            <>
              <Button onClick={cancelEditing}>
                取消
              </Button>
              <Button
                type="primary"
                loading={loading}
                onClick={saveEditing}
              >
                保存
              </Button>
            </>
          )}
        </Space>
      }
    >
      {editing ? (
        <Form form={form} layout="vertical">
          <Form.Item
            name="title"
            label="文档标题"
            rules={[
              { required: true, message: '请输入文档标题' },
              { max: 100, message: '标题长度不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入文档标题" />
          </Form.Item>

          <Form.Item name="tags" label="标签">
            <Select
              mode="tags"
              placeholder="添加标签"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea
              rows={4}
              placeholder="添加文档描述"
              maxLength={500}
            />
          </Form.Item>
        </Form>
      ) : (
        <div className="space-y-6">
          {/* 基本信息 */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <Text strong className="text-lg">
                {document.title}
              </Text>
              <Tag color={getDocumentTypeColor(document.type)}>
                {getDocumentTypeName(document.type)}
              </Tag>
            </div>

            {document.metadata.description && (
              <Paragraph className="text-gray-600">
                {document.metadata.description}
              </Paragraph>
            )}
          </div>

          <Divider />

          {/* 详细信息 */}
          <Descriptions column={1} size="small">
            <Descriptions.Item label="创建时间">
              <div className="flex items-center space-x-2">
                <ClockCircleOutlined className="text-gray-400" />
                <span>{formatRelativeTime(document.createdAt)}</span>
              </div>
            </Descriptions.Item>

            <Descriptions.Item label="修改时间">
              <div className="flex items-center space-x-2">
                <ClockCircleOutlined className="text-gray-400" />
                <span>{formatRelativeTime(document.updatedAt)}</span>
              </div>
            </Descriptions.Item>

            <Descriptions.Item label="文档大小">
              <Badge
                count={formatFileSize(document.metadata.size)}
                style={{ backgroundColor: '#52c41a' }}
              />
            </Descriptions.Item>

            {document.metadata.wordCount && (
              <Descriptions.Item label="字数统计">
                <Badge
                  count={`${document.metadata.wordCount.toLocaleString()} 字`}
                  style={{ backgroundColor: '#1890ff' }}
                />
              </Descriptions.Item>
            )}

            {document.metadata.readingTime && (
              <Descriptions.Item label="阅读时间">
                <Badge
                  count={`${document.metadata.readingTime} 分钟`}
                  style={{ backgroundColor: '#722ed1' }}
                />
              </Descriptions.Item>
            )}

            {document.metadata.author && (
              <Descriptions.Item label="作者">
                <div className="flex items-center space-x-2">
                  <UserOutlined className="text-gray-400" />
                  <span>{document.metadata.author}</span>
                </div>
              </Descriptions.Item>
            )}

            <Descriptions.Item label="版本">
              v{document.metadata.version}
            </Descriptions.Item>
          </Descriptions>

          <Divider />

          {/* 标签 */}
          <div>
            <div className="flex items-center space-x-2 mb-3">
              <TagOutlined className="text-gray-400" />
              <Text strong>标签</Text>
            </div>
            <div className="flex flex-wrap gap-2">
              {document.tags.length > 0 ? (
                document.tags.map(tag => (
                  <Tag key={tag} color="blue">
                    {tag}
                  </Tag>
                ))
              ) : (
                <Text type="secondary">暂无标签</Text>
              )}
            </div>
          </div>
        </div>
      )}
    </Drawer>
  )
}

export default DocumentDetailPanel
