/**
 * 文档右键菜单组件
 * 提供文档的各种操作功能，包括编辑、复制、删除、重命名等
 */

import React, { useState } from 'react'
import { 
  Menu, 
  Modal, 
  Input, 
  message, 
  Form,
  Select,
  Tag,
  Space
} from 'antd'
import {
  EditOutlined,
  CopyOutlined,
  DeleteOutlined,
  FolderOutlined,
  TagOutlined,
  ShareAltOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
  StarOutlined,
  StarFilled,
  EyeOutlined,
  LockOutlined
} from '@ant-design/icons'
import { BaseDocument, DocumentType } from '@/types'
import { documentManager } from '@services/document/documentManager'

/**
 * 文档操作菜单组件属性接口
 */
export interface DocumentContextMenuProps {
  /** 文档对象 */
  document: BaseDocument
  /** 编辑回调 */
  onEdit?: (document: BaseDocument) => void
  /** 删除回调 */
  onDelete?: (document: BaseDocument) => void
  /** 复制回调 */
  onDuplicate?: (document: BaseDocument) => void
  /** 重命名回调 */
  onRename?: (document: BaseDocument, newTitle: string) => void
  /** 标签更新回调 */
  onTagsUpdate?: (document: BaseDocument, tags: string[]) => void
  /** 收藏状态切换回调 */
  onFavoriteToggle?: (document: BaseDocument, isFavorite: boolean) => void
  /** 刷新列表回调 */
  onRefresh?: () => void
}

/**
 * 文档操作菜单组件
 */
const DocumentContextMenu: React.FC<DocumentContextMenuProps> = ({
  document,
  onEdit,
  onDelete,
  onDuplicate,
  onRename,
  onTagsUpdate,
  onFavoriteToggle,
  onRefresh
}) => {
  const [renameModalVisible, setRenameModalVisible] = useState(false)
  const [tagsModalVisible, setTagsModalVisible] = useState(false)
  const [infoModalVisible, setInfoModalVisible] = useState(false)
  const [renameForm] = Form.useForm()
  const [tagsForm] = Form.useForm()

  // 是否收藏（这里假设有收藏功能，实际需要在文档模型中添加）
  const isFavorite = false // document.isFavorite || false

  /**
   * 处理编辑文档
   */
  const handleEdit = () => {
    onEdit?.(document)
  }

  /**
   * 处理复制文档
   */
  const handleDuplicate = async () => {
    try {
      await documentManager.duplicateDocument(document.id, {
        title: `${document.title} - 副本`
      })
      message.success('文档复制成功')
      onDuplicate?.(document)
      onRefresh?.()
    } catch (error) {
      console.error('复制文档失败:', error)
      message.error('复制文档失败')
    }
  }

  /**
   * 处理删除文档
   */
  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除文档"${document.title}"吗？删除后可以在回收站中恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await documentManager.moveToTrash(document.id)
          message.success('文档已移动到回收站')
          onDelete?.(document)
          onRefresh?.()
        } catch (error) {
          console.error('删除文档失败:', error)
          message.error('删除文档失败')
        }
      }
    })
  }

  /**
   * 处理重命名文档
   */
  const handleRename = () => {
    renameForm.setFieldsValue({ title: document.title })
    setRenameModalVisible(true)
  }

  /**
   * 确认重命名
   */
  const handleRenameConfirm = async () => {
    try {
      const values = await renameForm.validateFields()
      await documentManager.updateDocument(document.id, { title: values.title })
      message.success('文档重命名成功')
      onRename?.(document, values.title)
      onRefresh?.()
      setRenameModalVisible(false)
    } catch (error) {
      console.error('重命名文档失败:', error)
      message.error('重命名文档失败')
    }
  }

  /**
   * 处理标签编辑
   */
  const handleEditTags = () => {
    tagsForm.setFieldsValue({ tags: document.tags })
    setTagsModalVisible(true)
  }

  /**
   * 确认标签更新
   */
  const handleTagsConfirm = async () => {
    try {
      const values = await tagsForm.validateFields()
      await documentManager.updateDocument(document.id, { tags: values.tags || [] })
      message.success('标签更新成功')
      onTagsUpdate?.(document, values.tags || [])
      onRefresh?.()
      setTagsModalVisible(false)
    } catch (error) {
      console.error('更新标签失败:', error)
      message.error('更新标签失败')
    }
  }

  /**
   * 处理收藏切换
   */
  const handleFavoriteToggle = async () => {
    try {
      // 这里需要实现收藏功能的API
      // await documentManager.toggleFavorite(document.id, !isFavorite)
      message.success(isFavorite ? '已取消收藏' : '已添加到收藏')
      onFavoriteToggle?.(document, !isFavorite)
      onRefresh?.()
    } catch (error) {
      console.error('切换收藏状态失败:', error)
      message.error('操作失败')
    }
  }

  /**
   * 处理导出文档
   */
  const handleExport = async () => {
    try {
      await documentManager.exportDocument(document.id, 'json')
      message.success('文档导出成功')
    } catch (error) {
      console.error('导出文档失败:', error)
      message.error('导出文档失败')
    }
  }

  /**
   * 显示文档信息
   */
  const handleShowInfo = () => {
    setInfoModalVisible(true)
  }

  /**
   * 获取文档类型名称
   */
  const getDocumentTypeName = (type: DocumentType) => {
    const names = {
      [DocumentType.TEXT]: '文本笔记',
      [DocumentType.WHITEBOARD]: '白板',
      [DocumentType.MINDMAP]: '思维导图',
      [DocumentType.KANBAN]: '看板'
    }
    return names[type]
  }

  /**
   * 格式化文件大小
   */
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 菜单项配置
  const menuItems = [
    {
      key: 'edit',
      label: '编辑',
      icon: <EditOutlined />,
      onClick: handleEdit
    },
    {
      key: 'rename',
      label: '重命名',
      icon: <EditOutlined />,
      onClick: handleRename
    },
    {
      key: 'duplicate',
      label: '复制',
      icon: <CopyOutlined />,
      onClick: handleDuplicate
    },
    {
      type: 'divider' as const
    },
    {
      key: 'favorite',
      label: isFavorite ? '取消收藏' : '添加收藏',
      icon: isFavorite ? <StarFilled /> : <StarOutlined />,
      onClick: handleFavoriteToggle
    },
    {
      key: 'tags',
      label: '编辑标签',
      icon: <TagOutlined />,
      onClick: handleEditTags
    },
    {
      type: 'divider' as const
    },
    {
      key: 'export',
      label: '导出',
      icon: <DownloadOutlined />,
      onClick: handleExport
    },
    {
      key: 'info',
      label: '属性',
      icon: <InfoCircleOutlined />,
      onClick: handleShowInfo
    },
    {
      type: 'divider' as const
    },
    {
      key: 'delete',
      label: '删除',
      icon: <DeleteOutlined />,
      danger: true,
      onClick: handleDelete
    }
  ]

  return (
    <>
      <Menu items={menuItems} />

      {/* 重命名对话框 */}
      <Modal
        title="重命名文档"
        open={renameModalVisible}
        onOk={handleRenameConfirm}
        onCancel={() => setRenameModalVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        <Form form={renameForm} layout="vertical">
          <Form.Item
            name="title"
            label="文档标题"
            rules={[
              { required: true, message: '请输入文档标题' },
              { max: 100, message: '标题长度不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入文档标题" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 标签编辑对话框 */}
      <Modal
        title="编辑标签"
        open={tagsModalVisible}
        onOk={handleTagsConfirm}
        onCancel={() => setTagsModalVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        <Form form={tagsForm} layout="vertical">
          <Form.Item
            name="tags"
            label="文档标签"
            help="输入标签名称后按回车添加，最多10个标签"
          >
            <Select
              mode="tags"
              placeholder="添加标签..."
              maxCount={10}
              maxTagTextLength={20}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 文档信息对话框 */}
      <Modal
        title="文档属性"
        open={infoModalVisible}
        onCancel={() => setInfoModalVisible(false)}
        footer={null}
        width={500}
      >
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-500">标题</label>
            <div className="mt-1">{document.title}</div>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-500">类型</label>
            <div className="mt-1">{getDocumentTypeName(document.type)}</div>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-500">大小</label>
            <div className="mt-1">{formatFileSize(document.metadata.size)}</div>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-500">创建时间</label>
            <div className="mt-1">{new Date(document.createdAt).toLocaleString()}</div>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-500">修改时间</label>
            <div className="mt-1">{new Date(document.updatedAt).toLocaleString()}</div>
          </div>
          
          {document.tags.length > 0 && (
            <div>
              <label className="text-sm font-medium text-gray-500">标签</label>
              <div className="mt-1">
                <Space wrap>
                  {document.tags.map(tag => (
                    <Tag key={tag} color="blue">{tag}</Tag>
                  ))}
                </Space>
              </div>
            </div>
          )}
          
          {document.metadata.wordCount && (
            <div>
              <label className="text-sm font-medium text-gray-500">字数</label>
              <div className="mt-1">{document.metadata.wordCount.toLocaleString()} 字</div>
            </div>
          )}
          
          {document.metadata.readingTime && (
            <div>
              <label className="text-sm font-medium text-gray-500">预计阅读时间</label>
              <div className="mt-1">{document.metadata.readingTime} 分钟</div>
            </div>
          )}
        </div>
      </Modal>
    </>
  )
}

export default DocumentContextMenu
