/**
 * 标签管理组件
 * 提供标签的创建、编辑、删除和批量操作功能
 */

import React, { useState, useEffect, useCallback } from 'react'
import {
  Modal,
  Table,
  Button,
  Input,
  Space,
  Popconfirm,
  message,
  Form,
  Tag,
  Tooltip,
  Badge
} from 'antd'
import {
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  SearchOutlined,
  TagOutlined
} from '@ant-design/icons'
import { documentManager } from '@services/document/documentManager'

/**
 * 标签信息接口
 */
export interface TagInfo {
  name: string
  count: number
  color?: string
}

/**
 * 标签管理组件属性接口
 */
export interface TagManagerProps {
  /** 是否显示 */
  visible: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 标签更新回调 */
  onTagsUpdated?: () => void
}

/**
 * 标签管理组件
 */
const TagManager: React.FC<TagManagerProps> = ({
  visible,
  onClose,
  onTagsUpdated
}) => {
  const [tags, setTags] = useState<TagInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const [editingTag, setEditingTag] = useState<TagInfo | null>(null)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [form] = Form.useForm()

  /**
   * 加载标签数据
   */
  const loadTags = useCallback(async () => {
    try {
      setLoading(true)
      const tagsWithCount = await documentManager.getTagsWithCount()
      setTags(tagsWithCount)
    } catch (error) {
      console.error('加载标签失败:', error)
      message.error('加载标签失败')
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * 初始化加载
   */
  useEffect(() => {
    if (visible) {
      loadTags()
    }
  }, [visible, loadTags])

  /**
   * 筛选标签
   */
  const filteredTags = tags.filter(tag =>
    tag.name.toLowerCase().includes(searchValue.toLowerCase())
  )

  /**
   * 处理编辑标签
   */
  const handleEditTag = (tag: TagInfo) => {
    setEditingTag(tag)
    form.setFieldsValue({ name: tag.name })
    setEditModalVisible(true)
  }

  /**
   * 处理保存标签
   */
  const handleSaveTag = async () => {
    try {
      const values = await form.validateFields()
      const newName = values.name.trim()

      if (!editingTag) return

      if (newName === editingTag.name) {
        setEditModalVisible(false)
        return
      }

      // 检查新名称是否已存在
      if (tags.some(tag => tag.name === newName && tag.name !== editingTag.name)) {
        message.error('标签名称已存在')
        return
      }

      setLoading(true)
      await documentManager.renameTag(editingTag.name, newName)
      
      message.success('标签重命名成功')
      setEditModalVisible(false)
      setEditingTag(null)
      form.resetFields()
      
      await loadTags()
      onTagsUpdated?.()
      
    } catch (error) {
      console.error('保存标签失败:', error)
      message.error('保存标签失败')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 处理删除标签
   */
  const handleDeleteTag = async (tag: TagInfo) => {
    try {
      setLoading(true)
      const updatedCount = await documentManager.deleteTag(tag.name)
      
      message.success(`标签删除成功，已从 ${updatedCount} 个文档中移除`)
      
      await loadTags()
      onTagsUpdated?.()
      
    } catch (error) {
      console.error('删除标签失败:', error)
      message.error('删除标签失败')
    } finally {
      setLoading(false)
    }
  }

  /**
   * 表格列定义
   */
  const columns = [
    {
      title: '标签名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: TagInfo) => (
        <div className="flex items-center space-x-2">
          <Tag color={record.color || 'blue'}>{name}</Tag>
        </div>
      )
    },
    {
      title: '使用次数',
      dataIndex: 'count',
      key: 'count',
      width: 120,
      render: (count: number) => (
        <Badge count={count} showZero color="blue" />
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_: any, record: TagInfo) => (
        <Space size="small">
          <Tooltip title="编辑标签">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditTag(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确认删除"
            description={`确定要删除标签"${record.name}"吗？这将从 ${record.count} 个文档中移除该标签。`}
            onConfirm={() => handleDeleteTag(record)}
            okText="删除"
            cancelText="取消"
            okType="danger"
          >
            <Tooltip title="删除标签">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <>
      {/* 标签管理主对话框 */}
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <TagOutlined />
            <span>标签管理</span>
          </div>
        }
        open={visible}
        onCancel={onClose}
        footer={null}
        width={800}
        className="tag-manager-modal"
      >
        <div className="mb-4">
          <Input
            placeholder="搜索标签..."
            prefix={<SearchOutlined />}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            allowClear
          />
        </div>

        <Table
          columns={columns}
          dataSource={filteredTags}
          rowKey="name"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个标签`
          }}
          size="small"
        />
      </Modal>

      {/* 编辑标签对话框 */}
      <Modal
        title="编辑标签"
        open={editModalVisible}
        onOk={handleSaveTag}
        onCancel={() => {
          setEditModalVisible(false)
          setEditingTag(null)
          form.resetFields()
        }}
        okText="保存"
        cancelText="取消"
        confirmLoading={loading}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="标签名称"
            rules={[
              { required: true, message: '请输入标签名称' },
              { max: 20, message: '标签名称不能超过20个字符' },
              { pattern: /^[^\s]+$/, message: '标签名称不能包含空格' }
            ]}
          >
            <Input placeholder="请输入标签名称" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default TagManager
