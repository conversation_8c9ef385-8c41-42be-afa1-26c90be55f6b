/**
 * 链接工具栏组件
 * 为文本编辑器提供双向链接创建和管理功能
 */

import React, { useState, useCallback } from 'react'
import { Button, Dropdown, Space, Tooltip, message } from 'antd'
import {
  LinkOutlined,
  BranchesOutlined,
  PlusOutlined,
  SearchOutlined,
  HistoryOutlined
} from '@ant-design/icons'
import LinkCreator from '@components/link/LinkCreator'
import BacklinkPanel from '@components/link/BacklinkPanel'
import { BaseDocument } from '@/types'

/**
 * 链接工具栏属性接口
 */
export interface LinkToolbarProps {
  /** 当前文档 */
  document: BaseDocument
  /** 编辑器实例 */
  editor?: any
  /** 插入文本回调 */
  onInsertText?: (text: string) => void
  /** 样式类名 */
  className?: string
}

/**
 * 链接工具栏组件
 */
const LinkToolbar: React.FC<LinkToolbarProps> = ({
  document,
  editor,
  onInsertText,
  className = ''
}) => {
  const [linkCreatorVisible, setLinkCreatorVisible] = useState(false)
  const [backlinkPanelVisible, setBacklinkPanelVisible] = useState(false)

  /**
   * 插入普通链接
   */
  const insertLink = useCallback(() => {
    const selectedText = editor?.getSelection()?.toString() || '链接文本'
    const linkMarkdown = `[${selectedText}](URL)`
    onInsertText?.(linkMarkdown)
  }, [editor, onInsertText])

  /**
   * 处理文档链接创建
   */
  const handleDocumentLinkCreate = useCallback((targetDocument: BaseDocument, linkText?: string) => {
    const text = linkText || targetDocument.title
    const linkMarkdown = `[[${text}|${targetDocument.id}]]`
    onInsertText?.(linkMarkdown)
    setLinkCreatorVisible(false)
    message.success(`已创建到"${targetDocument.title}"的链接`)
  }, [onInsertText])

  /**
   * 处理反向链接点击
   */
  const handleBacklinkClick = useCallback((linkedDocument: BaseDocument) => {
    // 这里可以导航到链接的文档或在新窗口打开
    console.log('点击反向链接:', linkedDocument.title)
  }, [])

  /**
   * 链接菜单项
   */
  const linkMenuItems = [
    {
      key: 'url-link',
      label: '外部链接',
      icon: <LinkOutlined />,
      onClick: insertLink
    },
    {
      key: 'document-link',
      label: '文档链接',
      icon: <BranchesOutlined />,
      onClick: () => setLinkCreatorVisible(true)
    },
    {
      type: 'divider' as const
    },
    {
      key: 'backlinks',
      label: '反向链接',
      icon: <HistoryOutlined />,
      onClick: () => setBacklinkPanelVisible(true)
    }
  ]

  return (
    <div className={`link-toolbar ${className}`}>
      <Space size="small">
        {/* 快速插入链接 */}
        <Tooltip title="插入链接 (Ctrl+K)">
          <Button
            type="text"
            size="small"
            icon={<LinkOutlined />}
            onClick={insertLink}
          />
        </Tooltip>

        {/* 链接菜单 */}
        <Dropdown
          menu={{ items: linkMenuItems }}
          trigger={['click']}
          placement="bottomLeft"
        >
          <Tooltip title="链接选项">
            <Button
              type="text"
              size="small"
              icon={<PlusOutlined />}
            />
          </Tooltip>
        </Dropdown>

        {/* 反向链接面板切换 */}
        <Tooltip title="反向链接">
          <Button
            type={backlinkPanelVisible ? 'primary' : 'text'}
            size="small"
            icon={<BranchesOutlined />}
            onClick={() => setBacklinkPanelVisible(!backlinkPanelVisible)}
          />
        </Tooltip>
      </Space>

      {/* 链接创建对话框 */}
      <LinkCreator
        visible={linkCreatorVisible}
        currentDocument={document}
        onClose={() => setLinkCreatorVisible(false)}
        onLinkCreate={handleDocumentLinkCreate}
      />

      {/* 反向链接面板 */}
      {backlinkPanelVisible && (
        <div className="absolute top-full left-0 z-10 mt-2 w-80">
          <BacklinkPanel
            documentId={document.id}
            onDocumentClick={handleBacklinkClick}
            className="shadow-lg"
          />
        </div>
      )}
    </div>
  )
}

export default LinkToolbar
