/**
 * 主题切换组件
 * 提供明暗模式切换功能，支持状态持久化和平滑过渡动画
 */

import React, { useState, useEffect } from 'react'
import { Button, Tooltip } from 'antd'
import { SunOutlined, MoonOutlined } from '@ant-design/icons'

// 主题类型枚举
export enum ThemeMode {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto'
}

// 主题切换组件属性接口
export interface ThemeToggleProps {
  /** 当前主题模式 */
  theme?: ThemeMode
  /** 主题切换回调函数 */
  onChange?: (theme: ThemeMode) => void
  /** 组件大小 */
  size?: 'small' | 'middle' | 'large'
  /** 是否显示文本 */
  showText?: boolean
  /** 自定义样式类名 */
  className?: string
  /** 是否禁用 */
  disabled?: boolean
}

// 本地存储键名
const THEME_STORAGE_KEY = 'multidimensional-notes-theme'

/**
 * 获取系统主题偏好
 * @returns 系统是否偏好暗色主题
 */
const getSystemThemePreference = (): boolean => {
  if (typeof window === 'undefined') return false
  return window.matchMedia('(prefers-color-scheme: dark)').matches
}

/**
 * 从本地存储获取主题设置
 * @returns 保存的主题模式
 */
const getStoredTheme = (): ThemeMode => {
  if (typeof window === 'undefined') return ThemeMode.LIGHT
  
  try {
    const stored = localStorage.getItem(THEME_STORAGE_KEY)
    if (stored && Object.values(ThemeMode).includes(stored as ThemeMode)) {
      return stored as ThemeMode
    }
  } catch (error) {
    console.warn('无法读取主题设置:', error)
  }
  
  return ThemeMode.LIGHT
}

/**
 * 保存主题设置到本地存储
 * @param theme 要保存的主题模式
 */
const saveThemeToStorage = (theme: ThemeMode): void => {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.setItem(THEME_STORAGE_KEY, theme)
  } catch (error) {
    console.warn('无法保存主题设置:', error)
  }
}

/**
 * 应用主题到文档根元素
 * @param theme 要应用的主题模式
 */
const applyThemeToDocument = (theme: ThemeMode): void => {
  if (typeof document === 'undefined') return
  
  const root = document.documentElement
  const isDark = theme === ThemeMode.DARK || 
    (theme === ThemeMode.AUTO && getSystemThemePreference())
  
  // 添加过渡动画类
  root.classList.add('theme-transitioning')
  
  // 应用主题类
  if (isDark) {
    root.classList.add('dark')
    root.classList.remove('light')
  } else {
    root.classList.add('light')
    root.classList.remove('dark')
  }
  
  // 移除过渡动画类
  setTimeout(() => {
    root.classList.remove('theme-transitioning')
  }, 300)
}

/**
 * 主题切换组件
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({
  theme: controlledTheme,
  onChange,
  size = 'middle',
  showText = false,
  className = '',
  disabled = false
}) => {
  // 内部主题状态
  const [internalTheme, setInternalTheme] = useState<ThemeMode>(() => {
    return controlledTheme || getStoredTheme()
  })

  // 切换动画状态
  const [isSwitching, setIsSwitching] = useState(false)

  // 使用受控或非受控模式
  const currentTheme = controlledTheme !== undefined ? controlledTheme : internalTheme
  
  // 监听系统主题变化
  useEffect(() => {
    if (typeof window === 'undefined') return
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleSystemThemeChange = () => {
      if (currentTheme === ThemeMode.AUTO) {
        applyThemeToDocument(ThemeMode.AUTO)
      }
    }
    
    mediaQuery.addEventListener('change', handleSystemThemeChange)
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange)
  }, [currentTheme])
  
  // 初始化时应用主题
  useEffect(() => {
    applyThemeToDocument(currentTheme)
  }, [currentTheme])
  
  /**
   * 处理主题切换
   */
  const handleThemeChange = () => {
    if (disabled || isSwitching) return

    // 开始切换动画
    setIsSwitching(true)

    let nextTheme: ThemeMode

    // 循环切换：light -> dark -> auto -> light
    switch (currentTheme) {
      case ThemeMode.LIGHT:
        nextTheme = ThemeMode.DARK
        break
      case ThemeMode.DARK:
        nextTheme = ThemeMode.AUTO
        break
      case ThemeMode.AUTO:
        nextTheme = ThemeMode.LIGHT
        break
      default:
        nextTheme = ThemeMode.LIGHT
    }

    // 延迟更新状态以配合动画
    setTimeout(() => {
      // 更新状态
      if (controlledTheme === undefined) {
        setInternalTheme(nextTheme)
        saveThemeToStorage(nextTheme)
      }

      // 触发回调
      onChange?.(nextTheme)

      // 结束切换动画
      setTimeout(() => {
        setIsSwitching(false)
      }, 100)
    }, 200)
  }
  
  /**
   * 获取当前主题的显示信息
   */
  const getThemeInfo = () => {
    const isDarkMode = currentTheme === ThemeMode.DARK || 
      (currentTheme === ThemeMode.AUTO && getSystemThemePreference())
    
    switch (currentTheme) {
      case ThemeMode.LIGHT:
        return {
          icon: <SunOutlined />,
          text: '浅色模式',
          tooltip: '切换到深色模式'
        }
      case ThemeMode.DARK:
        return {
          icon: <MoonOutlined />,
          text: '深色模式',
          tooltip: '切换到自动模式'
        }
      case ThemeMode.AUTO:
        return {
          icon: isDarkMode ? <MoonOutlined /> : <SunOutlined />,
          text: '自动模式',
          tooltip: '切换到浅色模式'
        }
      default:
        return {
          icon: <SunOutlined />,
          text: '浅色模式',
          tooltip: '切换主题'
        }
    }
  }
  
  const themeInfo = getThemeInfo()
  
  return (
    <Tooltip title={themeInfo.tooltip} placement="bottom">
      <Button
        type="text"
        size={size}
        icon={themeInfo.icon}
        onClick={handleThemeChange}
        disabled={disabled || isSwitching}
        className={`theme-toggle ${isSwitching ? 'switching' : ''} ${className}`}
        aria-label={`当前主题: ${themeInfo.text}, 点击${themeInfo.tooltip}`}
        loading={isSwitching}
      >
        {showText && themeInfo.text}
      </Button>
    </Tooltip>
  )
}

export default ThemeToggle

// 导出工具函数供其他组件使用
export {
  getSystemThemePreference,
  getStoredTheme,
  saveThemeToStorage,
  applyThemeToDocument
}
