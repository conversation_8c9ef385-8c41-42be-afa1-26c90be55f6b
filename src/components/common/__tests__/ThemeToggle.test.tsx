/**
 * 主题切换组件单元测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ConfigProvider } from 'antd'
import ThemeToggle, { ThemeMode, getStoredTheme, saveThemeToStorage, applyThemeToDocument } from '../ThemeToggle'

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock document.documentElement
const mockDocumentElement = {
  classList: {
    add: jest.fn(),
    remove: jest.fn(),
    contains: jest.fn()
  }
}

Object.defineProperty(document, 'documentElement', {
  value: mockDocumentElement
})

describe('ThemeToggle', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  describe('基础渲染', () => {
    it('应该正确渲染主题切换按钮', () => {
      render(
        <ConfigProvider>
          <ThemeToggle />
        </ConfigProvider>
      )

      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
      expect(button).toHaveClass('theme-toggle')
    })

    it('应该显示正确的图标和提示文本', () => {
      render(
        <ConfigProvider>
          <ThemeToggle />
        </ConfigProvider>
      )

      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-label', expect.stringContaining('当前主题'))
    })

    it('应该支持显示文本模式', () => {
      render(
        <ConfigProvider>
          <ThemeToggle showText />
        </ConfigProvider>
      )

      expect(screen.getByText('浅色模式')).toBeInTheDocument()
    })
  })

  describe('主题切换功能', () => {
    it('应该在点击时切换主题', async () => {
      const mockOnChange = jest.fn()
      
      render(
        <ConfigProvider>
          <ThemeToggle onChange={mockOnChange} />
        </ConfigProvider>
      )

      const button = screen.getByRole('button')
      fireEvent.click(button)

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(ThemeMode.DARK)
      })
    })

    it('应该按照正确的顺序循环切换主题', async () => {
      const mockOnChange = jest.fn()
      
      render(
        <ConfigProvider>
          <ThemeToggle onChange={mockOnChange} />
        </ConfigProvider>
      )

      const button = screen.getByRole('button')
      
      // 第一次点击：light -> dark
      fireEvent.click(button)
      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(ThemeMode.DARK)
      })

      // 重新渲染为 dark 模式
      render(
        <ConfigProvider>
          <ThemeToggle theme={ThemeMode.DARK} onChange={mockOnChange} />
        </ConfigProvider>
      )

      const darkButton = screen.getByRole('button')
      
      // 第二次点击：dark -> auto
      fireEvent.click(darkButton)
      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(ThemeMode.AUTO)
      })
    })

    it('应该在禁用状态下不响应点击', () => {
      const mockOnChange = jest.fn()
      
      render(
        <ConfigProvider>
          <ThemeToggle disabled onChange={mockOnChange} />
        </ConfigProvider>
      )

      const button = screen.getByRole('button')
      fireEvent.click(button)

      expect(mockOnChange).not.toHaveBeenCalled()
    })
  })

  describe('受控模式', () => {
    it('应该在受控模式下显示正确的主题', () => {
      render(
        <ConfigProvider>
          <ThemeToggle theme={ThemeMode.DARK} showText />
        </ConfigProvider>
      )

      expect(screen.getByText('深色模式')).toBeInTheDocument()
    })

    it('应该在受控模式下不保存到本地存储', async () => {
      const mockOnChange = jest.fn()
      
      render(
        <ConfigProvider>
          <ThemeToggle theme={ThemeMode.LIGHT} onChange={mockOnChange} />
        </ConfigProvider>
      )

      const button = screen.getByRole('button')
      fireEvent.click(button)

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalled()
      })

      expect(mockLocalStorage.setItem).not.toHaveBeenCalled()
    })
  })

  describe('本地存储功能', () => {
    it('应该从本地存储加载主题设置', () => {
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(ThemeMode.DARK))
      
      render(
        <ConfigProvider>
          <ThemeToggle showText />
        </ConfigProvider>
      )

      expect(screen.getByText('深色模式')).toBeInTheDocument()
    })

    it('应该在非受控模式下保存主题到本地存储', async () => {
      const mockOnChange = jest.fn()
      
      render(
        <ConfigProvider>
          <ThemeToggle onChange={mockOnChange} />
        </ConfigProvider>
      )

      const button = screen.getByRole('button')
      fireEvent.click(button)

      await waitFor(() => {
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
          'multidimensional-notes-theme',
          ThemeMode.DARK
        )
      })
    })
  })

  describe('切换动画', () => {
    it('应该在切换时显示加载状态', async () => {
      render(
        <ConfigProvider>
          <ThemeToggle />
        </ConfigProvider>
      )

      const button = screen.getByRole('button')
      fireEvent.click(button)

      // 检查按钮是否显示加载状态
      expect(button).toHaveClass('switching')
    })

    it('应该在切换过程中禁用按钮', async () => {
      render(
        <ConfigProvider>
          <ThemeToggle />
        </ConfigProvider>
      )

      const button = screen.getByRole('button')
      fireEvent.click(button)

      expect(button).toBeDisabled()
    })
  })
})

describe('主题工具函数', () => {
  describe('getStoredTheme', () => {
    it('应该返回存储的主题', () => {
      mockLocalStorage.getItem.mockReturnValue(ThemeMode.DARK)
      expect(getStoredTheme()).toBe(ThemeMode.DARK)
    })

    it('应该在没有存储主题时返回默认值', () => {
      mockLocalStorage.getItem.mockReturnValue(null)
      expect(getStoredTheme()).toBe(ThemeMode.LIGHT)
    })

    it('应该在存储值无效时返回默认值', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid-theme')
      expect(getStoredTheme()).toBe(ThemeMode.LIGHT)
    })
  })

  describe('saveThemeToStorage', () => {
    it('应该保存主题到本地存储', () => {
      saveThemeToStorage(ThemeMode.DARK)
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'multidimensional-notes-theme',
        ThemeMode.DARK
      )
    })

    it('应该处理存储错误', () => {
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage error')
      })

      // 应该不抛出错误
      expect(() => saveThemeToStorage(ThemeMode.DARK)).not.toThrow()
    })
  })

  describe('applyThemeToDocument', () => {
    it('应该为浅色主题添加正确的类', () => {
      applyThemeToDocument(ThemeMode.LIGHT)
      
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('light')
      expect(mockDocumentElement.classList.remove).toHaveBeenCalledWith('dark')
    })

    it('应该为暗色主题添加正确的类', () => {
      applyThemeToDocument(ThemeMode.DARK)
      
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('dark')
      expect(mockDocumentElement.classList.remove).toHaveBeenCalledWith('light')
    })

    it('应该为自动模式根据系统偏好设置主题', () => {
      // Mock 系统偏好为暗色
      window.matchMedia = jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }))

      applyThemeToDocument(ThemeMode.AUTO)
      
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('dark')
      expect(mockDocumentElement.classList.remove).toHaveBeenCalledWith('light')
    })

    it('应该添加过渡动画类', () => {
      applyThemeToDocument(ThemeMode.LIGHT)
      
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('theme-transitioning')
    })
  })
})
