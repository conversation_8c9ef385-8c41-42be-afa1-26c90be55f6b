import React, { useRef, useEffect, useState, useCallback } from 'react'
import { fabric } from 'fabric'
import { Button, Space, Tooltip, Slider, ColorPicker, Select, Divider } from 'antd'
import {
  EditOutlined,
  BorderOutlined,
  RadiusSettingOutlined,
  LineOutlined,
  FontSizeOutlined,
  DragOutlined,
  ClearOutlined,
  UndoOutlined,
  RedoOutlined,
  SaveOutlined,
  DownloadOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  ExpandOutlined,
  CompressOutlined,
  VerticalAlignTopOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignMiddleOutlined,
  BringForwardOutlined,
  SendBackwardOutlined
} from '@ant-design/icons'
import { BaseDocument } from '@/types'

const { Option } = Select

/**
 * 防抖函数
 */
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * 绘图工具类型枚举
 */
export enum DrawingTool {
  SELECT = 'select',      // 选择工具
  PEN = 'pen',           // 画笔
  RECTANGLE = 'rectangle', // 矩形
  CIRCLE = 'circle',     // 圆形
  LINE = 'line',         // 直线
  TEXT = 'text',         // 文本
  ERASER = 'eraser'      // 橡皮擦
}

/**
 * 绘图配置接口
 */
export interface DrawingConfig {
  tool: DrawingTool
  strokeColor: string
  fillColor: string
  strokeWidth: number
  fontSize: number
  fontFamily: string
}

/**
 * 白板画布组件属性接口
 */
export interface WhiteboardCanvasProps {
  /** 文档数据 */
  document?: BaseDocument
  /** 画布宽度 */
  width?: number
  /** 画布高度 */
  height?: number
  /** 是否只读模式 */
  readonly?: boolean
  /** 内容变化回调 */
  onChange?: (data: string) => void
  /** 样式类名 */
  className?: string
}

/**
 * 白板画布组件
 * 基于 Fabric.js 实现的白板绘图功能，支持多种绘图工具和操作
 */
const WhiteboardCanvas: React.FC<WhiteboardCanvasProps> = ({
  document,
  width = 800,
  height = 600,
  readonly = false,
  onChange,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [drawingConfig, setDrawingConfig] = useState<DrawingConfig>({
    tool: DrawingTool.PEN,
    strokeColor: '#000000',
    fillColor: 'transparent',
    strokeWidth: 2,
    fontSize: 16,
    fontFamily: 'Arial'
  })

  // 历史记录管理
  const [history, setHistory] = useState<string[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)

  // 性能监控
  const [objectCount, setObjectCount] = useState(0)
  const [renderTime, setRenderTime] = useState(0)

  /**
   * 初始化 Fabric.js 画布
   */
  useEffect(() => {
    if (!canvasRef.current) return

    // 创建 Fabric.js 画布实例，启用性能优化
    const canvas = new fabric.Canvas(canvasRef.current, {
      width,
      height,
      backgroundColor: '#ffffff',
      selection: !readonly,
      isDrawingMode: false,
      // 性能优化配置
      renderOnAddRemove: false, // 禁用自动渲染，手动控制
      skipTargetFind: false,    // 启用目标查找优化
      imageSmoothingEnabled: false, // 禁用图像平滑以提升性能
      enableRetinaScaling: false,   // 禁用高DPI缩放以提升性能
    })

    fabricCanvasRef.current = canvas

    // 设置画布样式
    canvas.freeDrawingBrush.width = drawingConfig.strokeWidth
    canvas.freeDrawingBrush.color = drawingConfig.strokeColor

    // 监听画布变化事件
    canvas.on('path:created', handleCanvasChange)
    canvas.on('object:added', (e) => {
      handleCanvasChange()
      setObjectCount(canvas.getObjects().length)
    })
    canvas.on('object:removed', (e) => {
      handleCanvasChange()
      setObjectCount(canvas.getObjects().length)
    })
    canvas.on('object:modified', handleCanvasChange)

    // 监听渲染性能
    canvas.on('after:render', () => {
      const endTime = performance.now()
      setRenderTime(endTime)
    })

    // 加载文档内容
    if (document?.content) {
      try {
        canvas.loadFromJSON(document.content, () => {
          canvas.renderAll()
          saveToHistory()
        })
      } catch (error) {
        console.error('加载白板内容失败:', error)
      }
    } else {
      saveToHistory()
    }

    return () => {
      // 清理事件监听器
      canvas.off('path:created', handleCanvasChange)
      canvas.off('object:added', handleCanvasChange)
      canvas.off('object:removed', handleCanvasChange)
      canvas.off('object:modified', handleCanvasChange)

      // 清理所有对象
      canvas.clear()

      // 释放画布资源
      canvas.dispose()
    }
  }, [width, height, readonly])

  /**
   * 处理画布内容变化（防抖优化）
   */
  const handleCanvasChange = useCallback(
    debounce(() => {
      if (!fabricCanvasRef.current || readonly) return

      const jsonData = JSON.stringify(fabricCanvasRef.current.toJSON())
      onChange?.(jsonData)

      // 延迟保存历史记录，避免频繁操作
      setTimeout(() => {
        saveToHistory()
      }, 500)
    }, 300),
    [onChange, readonly]
  )

  /**
   * 保存到历史记录
   */
  const saveToHistory = useCallback(() => {
    if (!fabricCanvasRef.current) return

    const currentState = JSON.stringify(fabricCanvasRef.current.toJSON())

    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1)
      newHistory.push(currentState)

      // 限制历史记录数量
      if (newHistory.length > 50) {
        newHistory.shift()
      }

      return newHistory
    })

    setHistoryIndex(prev => Math.min(prev + 1, 49))
  }, [historyIndex])

  /**
   * 撤销操作
   */
  const handleUndo = useCallback(() => {
    if (!fabricCanvasRef.current || historyIndex <= 0) return

    const prevIndex = historyIndex - 1
    const prevState = history[prevIndex]

    fabricCanvasRef.current.loadFromJSON(prevState, () => {
      fabricCanvasRef.current?.renderAll()
      setHistoryIndex(prevIndex)
    })
  }, [history, historyIndex])

  /**
   * 重做操作
   */
  const handleRedo = useCallback(() => {
    if (!fabricCanvasRef.current || historyIndex >= history.length - 1) return

    const nextIndex = historyIndex + 1
    const nextState = history[nextIndex]

    fabricCanvasRef.current.loadFromJSON(nextState, () => {
      fabricCanvasRef.current?.renderAll()
      setHistoryIndex(nextIndex)
    })
  }, [history, historyIndex])

  /**
   * 切换绘图工具
   */
  const handleToolChange = useCallback((tool: DrawingTool) => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current

    // 重置画布状态
    canvas.isDrawingMode = false
    canvas.selection = true
    canvas.defaultCursor = 'default'

    switch (tool) {
      case DrawingTool.SELECT:
        canvas.selection = true
        break

      case DrawingTool.PEN:
        canvas.isDrawingMode = true
        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas)
        canvas.freeDrawingBrush.width = drawingConfig.strokeWidth
        canvas.freeDrawingBrush.color = drawingConfig.strokeColor
        break

      case DrawingTool.ERASER:
        canvas.isDrawingMode = true
        canvas.freeDrawingBrush = new fabric.EraserBrush(canvas)
        canvas.freeDrawingBrush.width = drawingConfig.strokeWidth * 2
        break

      default:
        canvas.selection = false
        canvas.defaultCursor = 'crosshair'
        break
    }

    setDrawingConfig(prev => ({ ...prev, tool }))
  }, [drawingConfig.strokeWidth, drawingConfig.strokeColor])

  /**
   * 处理鼠标按下事件（用于绘制形状）
   */
  const handleMouseDown = useCallback((event: fabric.IEvent) => {
    if (!fabricCanvasRef.current || drawingConfig.tool === DrawingTool.SELECT || drawingConfig.tool === DrawingTool.PEN) return

    const canvas = fabricCanvasRef.current
    const pointer = canvas.getPointer(event.e)

    setIsDrawing(true)

    let shape: fabric.Object | null = null

    switch (drawingConfig.tool) {
      case DrawingTool.RECTANGLE:
        shape = new fabric.Rect({
          left: pointer.x,
          top: pointer.y,
          width: 0,
          height: 0,
          fill: drawingConfig.fillColor === 'transparent' ? 'transparent' : drawingConfig.fillColor,
          stroke: drawingConfig.strokeColor,
          strokeWidth: drawingConfig.strokeWidth
        })
        break

      case DrawingTool.CIRCLE:
        shape = new fabric.Circle({
          left: pointer.x,
          top: pointer.y,
          radius: 0,
          fill: drawingConfig.fillColor === 'transparent' ? 'transparent' : drawingConfig.fillColor,
          stroke: drawingConfig.strokeColor,
          strokeWidth: drawingConfig.strokeWidth
        })
        break

      case DrawingTool.LINE:
        shape = new fabric.Line([pointer.x, pointer.y, pointer.x, pointer.y], {
          stroke: drawingConfig.strokeColor,
          strokeWidth: drawingConfig.strokeWidth
        })
        break

      case DrawingTool.TEXT:
        shape = new fabric.IText('双击编辑文本', {
          left: pointer.x,
          top: pointer.y,
          fontSize: drawingConfig.fontSize,
          fontFamily: drawingConfig.fontFamily,
          fill: drawingConfig.strokeColor
        })
        setIsDrawing(false) // 文本不需要拖拽
        break
    }

    if (shape) {
      // 启用对象缓存以提升性能
      shape.set({
        objectCaching: true,
        statefullCache: true,
        noScaleCache: false
      })

      canvas.add(shape)
      canvas.setActiveObject(shape)
      canvas.requestRenderAll() // 使用 requestRenderAll 而不是 renderAll
    }
  }, [drawingConfig, isDrawing])

  /**
   * 处理鼠标移动事件（用于绘制形状）
   */
  const handleMouseMove = useCallback((event: fabric.IEvent) => {
    if (!fabricCanvasRef.current || !isDrawing) return

    const canvas = fabricCanvasRef.current
    const pointer = canvas.getPointer(event.e)
    const activeObject = canvas.getActiveObject()

    if (!activeObject) return

    switch (drawingConfig.tool) {
      case DrawingTool.RECTANGLE:
        const rect = activeObject as fabric.Rect
        const rectWidth = Math.abs(pointer.x - rect.left!)
        const rectHeight = Math.abs(pointer.y - rect.top!)
        rect.set({ width: rectWidth, height: rectHeight })
        break

      case DrawingTool.CIRCLE:
        const circle = activeObject as fabric.Circle
        const radius = Math.sqrt(
          Math.pow(pointer.x - circle.left!, 2) + Math.pow(pointer.y - circle.top!, 2)
        ) / 2
        circle.set({ radius })
        break

      case DrawingTool.LINE:
        const line = activeObject as fabric.Line
        line.set({ x2: pointer.x, y2: pointer.y })
        break
    }

    canvas.renderAll()
  }, [drawingConfig.tool, isDrawing])

  /**
   * 处理鼠标抬起事件
   */
  const handleMouseUp = useCallback(() => {
    setIsDrawing(false)
  }, [])

  /**
   * 清空画布
   */
  const handleClear = useCallback(() => {
    if (!fabricCanvasRef.current) return

    fabricCanvasRef.current.clear()
    fabricCanvasRef.current.backgroundColor = '#ffffff'
    fabricCanvasRef.current.renderAll()
    saveToHistory()
  }, [saveToHistory])

  /**
   * 绑定鼠标事件
   */
  useEffect(() => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current

    canvas.on('mouse:down', handleMouseDown)
    canvas.on('mouse:move', handleMouseMove)
    canvas.on('mouse:up', handleMouseUp)

    return () => {
      canvas.off('mouse:down', handleMouseDown)
      canvas.off('mouse:move', handleMouseMove)
      canvas.off('mouse:up', handleMouseUp)
    }
  }, [handleMouseDown, handleMouseMove, handleMouseUp])

  /**
   * 绑定键盘和滚轮事件
   */
  useEffect(() => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current

    // 鼠标滚轮缩放
    const handleWheel = (opt: fabric.IEvent) => {
      const delta = (opt.e as WheelEvent).deltaY
      let zoom = canvas.getZoom()
      zoom *= 0.999 ** delta

      if (zoom > 20) zoom = 20
      if (zoom < 0.01) zoom = 0.01

      const point = new fabric.Point((opt.e as MouseEvent).offsetX, (opt.e as MouseEvent).offsetY)
      canvas.zoomToPoint(point, zoom)

      opt.e.preventDefault()
      opt.e.stopPropagation()
    }

    // 键盘快捷键
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'z':
            e.preventDefault()
            if (e.shiftKey) {
              handleRedo()
            } else {
              handleUndo()
            }
            break
          case 'a':
            e.preventDefault()
            canvas.discardActiveObject()
            const selection = new fabric.ActiveSelection(canvas.getObjects(), {
              canvas: canvas,
            })
            canvas.setActiveObject(selection)
            canvas.requestRenderAll()
            break
          case 'Delete':
          case 'Backspace':
            e.preventDefault()
            const activeObjects = canvas.getActiveObjects()
            activeObjects.forEach(obj => canvas.remove(obj))
            canvas.discardActiveObject()
            canvas.requestRenderAll()
            break
        }
      }
    }

    canvas.on('mouse:wheel', handleWheel)
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      canvas.off('mouse:wheel', handleWheel)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleUndo, handleRedo])

  /**
   * 导出画布为图片
   */
  const handleExport = useCallback(() => {
    if (!fabricCanvasRef.current) return

    const dataURL = fabricCanvasRef.current.toDataURL({
      format: 'png',
      quality: 1
    })

    const link = document.createElement('a')
    link.download = `whiteboard-${Date.now()}.png`
    link.href = dataURL
    link.click()
  }, [])

  /**
   * 缩放画布
   */
  const handleZoom = useCallback((delta: number) => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    let zoom = canvas.getZoom()
    zoom *= 0.999 ** delta

    if (zoom > 20) zoom = 20
    if (zoom < 0.01) zoom = 0.01

    canvas.zoomToPoint({ x: canvas.width! / 2, y: canvas.height! / 2 }, zoom)
  }, [])

  /**
   * 重置画布视图
   */
  const handleResetView = useCallback(() => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    canvas.setViewportTransform([1, 0, 0, 1, 0, 0])
    canvas.setZoom(1)
  }, [])

  /**
   * 适应画布内容
   */
  const handleFitToContent = useCallback(() => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    const objects = canvas.getObjects()

    if (objects.length === 0) return

    // 计算所有对象的边界框
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

    objects.forEach(obj => {
      const bounds = obj.getBoundingRect()
      minX = Math.min(minX, bounds.left)
      minY = Math.min(minY, bounds.top)
      maxX = Math.max(maxX, bounds.left + bounds.width)
      maxY = Math.max(maxY, bounds.top + bounds.height)
    })

    const contentWidth = maxX - minX
    const contentHeight = maxY - minY
    const canvasWidth = canvas.width!
    const canvasHeight = canvas.height!

    // 计算缩放比例
    const scaleX = canvasWidth / contentWidth
    const scaleY = canvasHeight / contentHeight
    const scale = Math.min(scaleX, scaleY) * 0.9 // 留一些边距

    // 计算中心点
    const centerX = (minX + maxX) / 2
    const centerY = (minY + maxY) / 2

    // 应用变换
    canvas.setZoom(scale)
    canvas.absolutePan({
      x: canvasWidth / 2 - centerX * scale,
      y: canvasHeight / 2 - centerY * scale
    })
  }, [])

  /**
   * 置于顶层
   */
  const handleBringToFront = useCallback(() => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    const activeObject = canvas.getActiveObject()

    if (activeObject) {
      canvas.bringToFront(activeObject)
      canvas.renderAll()
    }
  }, [])

  /**
   * 置于底层
   */
  const handleSendToBack = useCallback(() => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    const activeObject = canvas.getActiveObject()

    if (activeObject) {
      canvas.sendToBack(activeObject)
      canvas.renderAll()
    }
  }, [])

  /**
   * 向前一层
   */
  const handleBringForward = useCallback(() => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    const activeObject = canvas.getActiveObject()

    if (activeObject) {
      canvas.bringForward(activeObject)
      canvas.renderAll()
    }
  }, [])

  /**
   * 向后一层
   */
  const handleSendBackward = useCallback(() => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    const activeObject = canvas.getActiveObject()

    if (activeObject) {
      canvas.sendBackward(activeObject)
      canvas.renderAll()
    }
  }, [])

  /**
   * 复制选中对象
   */
  const handleCopy = useCallback(() => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    const activeObject = canvas.getActiveObject()

    if (activeObject) {
      activeObject.clone((cloned: fabric.Object) => {
        cloned.set({
          left: cloned.left! + 10,
          top: cloned.top! + 10,
        })
        canvas.add(cloned)
        canvas.setActiveObject(cloned)
        canvas.renderAll()
      })
    }
  }, [])

  /**
   * 删除选中对象
   */
  const handleDelete = useCallback(() => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    const activeObjects = canvas.getActiveObjects()

    if (activeObjects.length > 0) {
      activeObjects.forEach(obj => canvas.remove(obj))
      canvas.discardActiveObject()
      canvas.renderAll()
    }
  }, [])

  if (readonly) {
    return (
      <div className={`whiteboard-canvas readonly ${className}`}>
        <canvas ref={canvasRef} />
      </div>
    )
  }

  return (
    <div className={`whiteboard-canvas ${className}`}>
      {/* 工具栏 */}
      <div className="whiteboard-toolbar">
        <Space wrap>
          {/* 绘图工具 */}
          <Space>
            <Tooltip title="选择工具">
              <Button
                type={drawingConfig.tool === DrawingTool.SELECT ? 'primary' : 'default'}
                icon={<DragOutlined />}
                onClick={() => handleToolChange(DrawingTool.SELECT)}
              />
            </Tooltip>
            <Tooltip title="画笔">
              <Button
                type={drawingConfig.tool === DrawingTool.PEN ? 'primary' : 'default'}
                icon={<EditOutlined />}
                onClick={() => handleToolChange(DrawingTool.PEN)}
              />
            </Tooltip>
            <Tooltip title="矩形">
              <Button
                type={drawingConfig.tool === DrawingTool.RECTANGLE ? 'primary' : 'default'}
                icon={<BorderOutlined />}
                onClick={() => handleToolChange(DrawingTool.RECTANGLE)}
              />
            </Tooltip>
            <Tooltip title="圆形">
              <Button
                type={drawingConfig.tool === DrawingTool.CIRCLE ? 'primary' : 'default'}
                icon={<RadiusSettingOutlined />}
                onClick={() => handleToolChange(DrawingTool.CIRCLE)}
              />
            </Tooltip>
            <Tooltip title="直线">
              <Button
                type={drawingConfig.tool === DrawingTool.LINE ? 'primary' : 'default'}
                icon={<LineOutlined />}
                onClick={() => handleToolChange(DrawingTool.LINE)}
              />
            </Tooltip>
            <Tooltip title="文本">
              <Button
                type={drawingConfig.tool === DrawingTool.TEXT ? 'primary' : 'default'}
                icon={<FontSizeOutlined />}
                onClick={() => handleToolChange(DrawingTool.TEXT)}
              />
            </Tooltip>
          </Space>

          <Divider type="vertical" />

          {/* 样式配置 */}
          <Space>
            <ColorPicker
              value={drawingConfig.strokeColor}
              onChange={(color) => {
                const colorStr = color.toHexString()
                setDrawingConfig(prev => ({ ...prev, strokeColor: colorStr }))

                if (fabricCanvasRef.current?.freeDrawingBrush) {
                  fabricCanvasRef.current.freeDrawingBrush.color = colorStr
                }
              }}
            />

            <div style={{ width: 100 }}>
              <Slider
                min={1}
                max={20}
                value={drawingConfig.strokeWidth}
                onChange={(value) => {
                  setDrawingConfig(prev => ({ ...prev, strokeWidth: value }))

                  if (fabricCanvasRef.current?.freeDrawingBrush) {
                    fabricCanvasRef.current.freeDrawingBrush.width = value
                  }
                }}
              />
            </div>
          </Space>

          <Divider type="vertical" />

          {/* 操作按钮 */}
          <Space>
            <Tooltip title="撤销">
              <Button
                icon={<UndoOutlined />}
                onClick={handleUndo}
                disabled={historyIndex <= 0}
              />
            </Tooltip>
            <Tooltip title="重做">
              <Button
                icon={<RedoOutlined />}
                onClick={handleRedo}
                disabled={historyIndex >= history.length - 1}
              />
            </Tooltip>
            <Tooltip title="清空画布">
              <Button
                icon={<ClearOutlined />}
                onClick={handleClear}
                danger
              />
            </Tooltip>
            <Tooltip title="导出图片">
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              />
            </Tooltip>
          </Space>

          <Divider type="vertical" />

          {/* 画布操作 */}
          <Space>
            <Tooltip title="放大">
              <Button
                icon={<ZoomInOutlined />}
                onClick={() => handleZoom(-100)}
              />
            </Tooltip>
            <Tooltip title="缩小">
              <Button
                icon={<ZoomOutOutlined />}
                onClick={() => handleZoom(100)}
              />
            </Tooltip>
            <Tooltip title="适应内容">
              <Button
                icon={<CompressOutlined />}
                onClick={handleFitToContent}
              />
            </Tooltip>
            <Tooltip title="重置视图">
              <Button
                icon={<ExpandOutlined />}
                onClick={handleResetView}
              />
            </Tooltip>
          </Space>

          <Divider type="vertical" />

          {/* 图层管理 */}
          <Space>
            <Tooltip title="置于顶层">
              <Button
                icon={<VerticalAlignTopOutlined />}
                onClick={handleBringToFront}
              />
            </Tooltip>
            <Tooltip title="向前一层">
              <Button
                icon={<BringForwardOutlined />}
                onClick={handleBringForward}
              />
            </Tooltip>
            <Tooltip title="向后一层">
              <Button
                icon={<SendBackwardOutlined />}
                onClick={handleSendBackward}
              />
            </Tooltip>
            <Tooltip title="置于底层">
              <Button
                icon={<VerticalAlignBottomOutlined />}
                onClick={handleSendToBack}
              />
            </Tooltip>
          </Space>
        </Space>
      </div>

      {/* 画布 */}
      <div className="whiteboard-canvas-container">
        <canvas ref={canvasRef} />
      </div>
    </div>
  )
}

export default WhiteboardCanvas
