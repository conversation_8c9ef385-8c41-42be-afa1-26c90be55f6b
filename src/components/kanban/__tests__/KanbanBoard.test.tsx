/**
 * 看板组件单元测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import KanbanBoard, { CardPriority, CardStatus } from '../KanbanBoardNew'
import { BaseDocument, DocumentType } from '@/types'

// Mock @dnd-kit
jest.mock('@dnd-kit/core', () => ({
  DndContext: ({ children }: any) => <div data-testid="dnd-context">{children}</div>,
  useSensor: jest.fn(),
  useSensors: jest.fn(() => []),
  PointerSensor: jest.fn(),
  closestCorners: jest.fn()
}))

jest.mock('@dnd-kit/sortable', () => ({
  SortableContext: ({ children }: any) => <div data-testid="sortable-context">{children}</div>,
  useSortable: () => ({
    attributes: {},
    listeners: {},
    setNodeRef: jest.fn(),
    transform: null,
    transition: null,
    isDragging: false
  }),
  verticalListSortingStrategy: jest.fn()
}))

jest.mock('@dnd-kit/utilities', () => ({
  CSS: {
    Transform: {
      toString: jest.fn(() => '')
    }
  }
}))

// 测试用的文档数据
const mockDocument: BaseDocument = {
  id: 'test-doc-1',
  title: '测试看板',
  content: JSON.stringify({
    columns: [
      { id: 'todo', title: '待办', status: CardStatus.TODO, color: '#f0f0f0' },
      { id: 'in_progress', title: '进行中', status: CardStatus.IN_PROGRESS, color: '#e6f7ff' }
    ],
    cards: [
      {
        id: 'card-1',
        title: '测试卡片',
        description: '这是一个测试卡片',
        status: CardStatus.TODO,
        priority: CardPriority.MEDIUM,
        tags: ['测试'],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]
  }),
  type: DocumentType.KANBAN,
  tags: ['测试'],
  createdAt: new Date(),
  updatedAt: new Date()
}

describe('KanbanBoard', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('基础渲染', () => {
    it('应该正确渲染看板组件', () => {
      render(<KanbanBoard />)

      expect(screen.getByTestId('dnd-context')).toBeInTheDocument()
      expect(screen.getByText('待办')).toBeInTheDocument()
      expect(screen.getByText('进行中')).toBeInTheDocument()
      expect(screen.getByText('待审核')).toBeInTheDocument()
      expect(screen.getByText('已完成')).toBeInTheDocument()
    })

    it('应该在非只读模式下显示添加卡片按钮', () => {
      render(<KanbanBoard readonly={false} />)

      const addButtons = screen.getAllByText('添加卡片')
      expect(addButtons.length).toBeGreaterThan(0)
    })

    it('应该在只读模式下隐藏添加卡片按钮', () => {
      render(<KanbanBoard readonly={true} />)

      expect(screen.queryByText('添加卡片')).not.toBeInTheDocument()
    })
  })

  describe('卡片显示', () => {
    it('应该正确显示卡片内容', () => {
      render(<KanbanBoard document={mockDocument} />)

      expect(screen.getByText('测试卡片')).toBeInTheDocument()
      expect(screen.getByText('这是一个测试卡片')).toBeInTheDocument()
      expect(screen.getByText('中')).toBeInTheDocument() // 优先级标签
      expect(screen.getByText('测试')).toBeInTheDocument() // 标签
    })

    it('应该显示正确的卡片数量', () => {
      render(<KanbanBoard document={mockDocument} />)

      // 检查列标题中的卡片数量
      expect(screen.getByText('(1)')).toBeInTheDocument() // 待办列有1张卡片
    })
  })

  describe('卡片操作', () => {
    it('应该能够打开新建卡片模态框', async () => {
      render(<KanbanBoard />)

      const addButton = screen.getAllByText('添加卡片')[0]
      fireEvent.click(addButton)

      await waitFor(() => {
        expect(screen.getByText('新建卡片')).toBeInTheDocument()
      })
    })

    it('应该能够在模态框中输入卡片信息', async () => {
      render(<KanbanBoard />)

      const addButton = screen.getAllByText('添加卡片')[0]
      fireEvent.click(addButton)

      await waitFor(() => {
        const titleInput = screen.getByPlaceholderText('请输入卡片标题')
        fireEvent.change(titleInput, { target: { value: '新卡片' } })
        expect(titleInput).toHaveValue('新卡片')
      })
    })

    it('应该能够选择卡片优先级', async () => {
      render(<KanbanBoard />)

      const addButton = screen.getAllByText('添加卡片')[0]
      fireEvent.click(addButton)

      await waitFor(() => {
        expect(screen.getByText('优先级')).toBeInTheDocument()
      })
    })
  })

  describe('数据管理', () => {
    it('应该正确加载文档内容', () => {
      const onChange = jest.fn()
      
      render(<KanbanBoard document={mockDocument} onChange={onChange} />)

      expect(screen.getByText('测试卡片')).toBeInTheDocument()
    })

    it('应该在内容变化时调用onChange回调', () => {
      const onChange = jest.fn()
      
      render(<KanbanBoard onChange={onChange} />)

      // onChange应该被正确设置
      expect(onChange).toBeDefined()
    })
  })

  describe('拖拽功能', () => {
    it('应该正确设置拖拽上下文', () => {
      render(<KanbanBoard />)

      expect(screen.getByTestId('dnd-context')).toBeInTheDocument()
      expect(screen.getAllByTestId('sortable-context')).toHaveLength(4) // 4个列
    })
  })

  describe('样式和主题', () => {
    it('应该应用自定义类名', () => {
      render(<KanbanBoard className="custom-kanban" />)

      const container = screen.getByTestId('dnd-context').parentElement
      expect(container).toHaveClass('custom-kanban')
    })

    it('应该显示不同优先级的颜色标签', () => {
      render(<KanbanBoard document={mockDocument} />)

      // 检查优先级标签是否存在
      expect(screen.getByText('中')).toBeInTheDocument()
    })
  })

  describe('错误处理', () => {
    it('应该处理无效的文档内容', () => {
      const invalidDocument: BaseDocument = {
        ...mockDocument,
        content: 'invalid json'
      }

      expect(() => {
        render(<KanbanBoard document={invalidDocument} />)
      }).not.toThrow()
    })

    it('应该处理空文档内容', () => {
      const emptyDocument: BaseDocument = {
        ...mockDocument,
        content: ''
      }

      expect(() => {
        render(<KanbanBoard document={emptyDocument} />)
      }).not.toThrow()
    })
  })

  describe('模态框操作', () => {
    it('应该能够关闭模态框', async () => {
      render(<KanbanBoard />)

      const addButton = screen.getAllByText('添加卡片')[0]
      fireEvent.click(addButton)

      await waitFor(() => {
        expect(screen.getByText('新建卡片')).toBeInTheDocument()
      })

      const cancelButton = screen.getByText('取消')
      fireEvent.click(cancelButton)

      await waitFor(() => {
        expect(screen.queryByText('新建卡片')).not.toBeInTheDocument()
      })
    })

    it('应该验证必填字段', async () => {
      render(<KanbanBoard />)

      const addButton = screen.getAllByText('添加卡片')[0]
      fireEvent.click(addButton)

      await waitFor(() => {
        const saveButton = screen.getByText('保存')
        fireEvent.click(saveButton)
        
        // 应该显示验证错误（由于标题为空）
        expect(screen.getByText('新建卡片')).toBeInTheDocument()
      })
    })
  })

  describe('性能优化', () => {
    it('应该正确清理资源', () => {
      const { unmount } = render(<KanbanBoard />)

      expect(() => unmount()).not.toThrow()
    })
  })

  describe('卡片编辑', () => {
    it('应该能够编辑现有卡片', () => {
      render(<KanbanBoard document={mockDocument} readonly={false} />)

      // 由于我们mock了拖拽功能，这里主要测试卡片是否正确渲染
      expect(screen.getByText('测试卡片')).toBeInTheDocument()
    })
  })
})
