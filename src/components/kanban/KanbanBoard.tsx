/**
 * 看板组件
 * 基于 @dnd-kit 实现的看板功能，支持卡片拖拽、状态管理等
 */

import React, { useState, useCallback, useMemo } from 'react'
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners
} from '@dnd-kit/core'
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
  useSortable
} from '@dnd-kit/sortable'
import { useDroppable } from '@dnd-kit/core'
import { CSS } from '@dnd-kit/utilities'
import { Card, Button, Input, Tag, Space, Modal, Form, Select, DatePicker, message, Tooltip } from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CalendarOutlined,
  UserOutlined,
  FlagOutlined,
  MoreOutlined
} from '@ant-design/icons'
import { BaseDocument } from '@/types'

/**
 * 看板卡片优先级
 */
export enum CardPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

/**
 * 看板卡片状态
 */
export enum CardStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  REVIEW = 'review',
  DONE = 'done'
}

/**
 * 看板卡片接口
 */
export interface KanbanCard {
  id: string
  title: string
  description: string
  status: CardStatus
  priority: CardPriority
  tags: string[]
  assignee?: string
  dueDate?: Date
  createdAt: Date
  updatedAt: Date
}

/**
 * 看板列接口
 */
export interface KanbanColumn {
  id: string
  title: string
  status: CardStatus
  cards: KanbanCard[]
  color?: string
  limit?: number
}

/**
 * 看板数据接口
 */
export interface KanbanData {
  columns: KanbanColumn[]
  cards: KanbanCard[]
}

/**
 * 看板组件属性接口
 */
export interface KanbanBoardProps {
  /** 文档数据 */
  document?: BaseDocument
  /** 看板数据 */
  data?: KanbanData
  /** 是否只读模式 */
  readonly?: boolean
  /** 内容变化回调 */
  onChange?: (data: string) => void
  /** 样式类名 */
  className?: string
}

/**
 * 优先级颜色映射
 */
const PRIORITY_COLORS = {
  [CardPriority.LOW]: '#52c41a',
  [CardPriority.MEDIUM]: '#1890ff',
  [CardPriority.HIGH]: '#fa8c16',
  [CardPriority.URGENT]: '#f5222d'
}

/**
 * 优先级标签映射
 */
const PRIORITY_LABELS = {
  [CardPriority.LOW]: '低',
  [CardPriority.MEDIUM]: '中',
  [CardPriority.HIGH]: '高',
  [CardPriority.URGENT]: '紧急'
}

/**
 * 默认看板数据
 */
const DEFAULT_KANBAN_DATA: KanbanData = {
  columns: [
    {
      id: 'todo',
      title: '待办',
      status: CardStatus.TODO,
      cards: [],
      color: '#f0f0f0'
    },
    {
      id: 'in_progress',
      title: '进行中',
      status: CardStatus.IN_PROGRESS,
      cards: [],
      color: '#e6f7ff'
    },
    {
      id: 'review',
      title: '待审核',
      status: CardStatus.REVIEW,
      cards: [],
      color: '#fff7e6'
    },
    {
      id: 'done',
      title: '已完成',
      status: CardStatus.DONE,
      cards: [],
      color: '#f6ffed'
    }
  ],
  cards: []
}

/**
 * 看板组件
 * 基于 @dnd-kit 实现的看板功能，支持卡片拖拽、状态管理等
 */
const KanbanBoard: React.FC<KanbanBoardProps> = ({
  document,
  data = DEFAULT_KANBAN_DATA,
  readonly = false,
  onChange,
  className = ''
}) => {
  const [kanbanData, setKanbanData] = useState<KanbanData>(data)
  const [isCardModalVisible, setIsCardModalVisible] = useState(false)
  const [editingCard, setEditingCard] = useState<KanbanCard | null>(null)
  const [selectedColumn, setSelectedColumn] = useState<CardStatus | null>(null)
  const [activeCard, setActiveCard] = useState<KanbanCard | null>(null)
  const [form] = Form.useForm()

  // 配置拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  )

  /**
   * 处理拖拽开始
   */
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event
    const card = kanbanData.cards.find(c => c.id === active.id)
    setActiveCard(card || null)
  }, [kanbanData.cards])

  /**
   * 处理拖拽结束
   */
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event

    setActiveCard(null)

    if (!over) return

    const cardId = active.id as string
    const newStatus = over.id as CardStatus

    // 如果状态没有改变，则不执行任何操作
    const card = kanbanData.cards.find(c => c.id === cardId)
    if (!card || card.status === newStatus) return

    setKanbanData(prevData => {
      const newData = { ...prevData }

      // 更新卡片状态
      const updatedCards = newData.cards.map(c =>
        c.id === cardId
          ? { ...c, status: newStatus, updatedAt: new Date() }
          : c
      )

      // 重新组织列数据
      const updatedColumns = newData.columns.map(column => ({
        ...column,
        cards: updatedCards.filter(card => card.status === column.status)
      }))

      const result = {
        ...newData,
        cards: updatedCards,
        columns: updatedColumns
      }

      // 触发变化回调
      if (onChange) {
        onChange(JSON.stringify(result))
      }

      return result
    })

    console.log(`卡片 ${cardId} 移动到 ${newStatus}`)
  }, [kanbanData.cards, onChange])

  /**
   * 生成唯一ID
   */
  const generateId = useCallback(() => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }, [])

  /**
   * 打开卡片编辑模态框
   */
  const openCardModal = useCallback((columnStatus?: CardStatus, card?: KanbanCard) => {
    if (readonly) return

    setSelectedColumn(columnStatus || null)
    setEditingCard(card || null)

    if (card) {
      form.setFieldsValue({
        title: card.title,
        description: card.description,
        priority: card.priority,
        tags: card.tags,
        assignee: card.assignee,
        dueDate: card.dueDate ? new Date(card.dueDate) : null
      })
    } else {
      form.resetFields()
    }

    setIsCardModalVisible(true)
  }, [form, readonly])

  /**
   * 关闭卡片编辑模态框
   */
  const closeCardModal = useCallback(() => {
    setIsCardModalVisible(false)
    setEditingCard(null)
    setSelectedColumn(null)
    form.resetFields()
  }, [form])

  /**
   * 保存卡片
   */
  const saveCard = useCallback(async () => {
    try {
      const values = await form.validateFields()
      const now = new Date()

      setKanbanData(prevData => {
        let newData: KanbanData

        if (editingCard) {
          // 更新现有卡片
          const updatedCards = prevData.cards.map(card =>
            card.id === editingCard.id
              ? {
                  ...card,
                  ...values,
                  updatedAt: now
                }
              : card
          )

          newData = {
            ...prevData,
            cards: updatedCards,
            columns: prevData.columns.map(column => ({
              ...column,
              cards: updatedCards.filter(card => card.status === column.status)
            }))
          }

          message.success('卡片更新成功')
        } else {
          // 创建新卡片
          const newCard: KanbanCard = {
            id: generateId(),
            ...values,
            status: selectedColumn || CardStatus.TODO,
            createdAt: now,
            updatedAt: now
          }

          const updatedCards = [...prevData.cards, newCard]

          newData = {
            ...prevData,
            cards: updatedCards,
            columns: prevData.columns.map(column => ({
              ...column,
              cards: updatedCards.filter(card => card.status === column.status)
            }))
          }

          message.success('卡片创建成功')
        }

        // 触发变化回调
        if (onChange) {
          onChange(JSON.stringify(newData))
        }

        return newData
      })

      closeCardModal()
    } catch (error) {
      console.error('保存卡片失败:', error)
    }
  }, [editingCard, selectedColumn, form, closeCardModal, generateId, onChange])

  /**
   * 删除卡片
   */
  const deleteCard = useCallback((cardId: string) => {
    if (readonly) return

    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这张卡片吗？',
      onOk: () => {
        setKanbanData(prevData => {
          const updatedCards = prevData.cards.filter(card => card.id !== cardId)
          const newData = {
            ...prevData,
            cards: updatedCards,
            columns: prevData.columns.map(column => ({
              ...column,
              cards: updatedCards.filter(card => card.status === column.status)
            }))
          }

          // 触发变化回调
          if (onChange) {
            onChange(JSON.stringify(newData))
          }

          return newData
        })
        message.success('卡片删除成功')
      }
    })
  }, [readonly, onChange])

  /**
   * 加载文档内容
   */
  const loadDocumentContent = useCallback(() => {
    if (!document?.content) return

    try {
      const data = JSON.parse(document.content) as KanbanData
      if (data.columns && data.cards) {
        // 重新组织列数据，确保卡片正确分配到列中
        const updatedColumns = data.columns.map(column => ({
          ...column,
          cards: data.cards.filter(card => card.status === column.status)
        }))

        setKanbanData({
          ...data,
          columns: updatedColumns
        })
      }
    } catch (error) {
      console.error('加载看板内容失败:', error)
    }
  }, [document?.content])

  // 加载文档内容
  React.useEffect(() => {
    loadDocumentContent()
  }, [loadDocumentContent])

  /**
   * 可排序卡片组件
   */
  const SortableCard: React.FC<{ card: KanbanCard }> = ({ card }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({ id: card.id })

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging ? 0.5 : 1,
    }

    return (
      <div
        ref={setNodeRef}
        style={style}
        {...attributes}
        {...listeners}
        className="mb-3"
      >
        <Card
          size="small"
          className="shadow-sm hover:shadow-md transition-shadow cursor-pointer"
          actions={[
            <EditOutlined
              key="edit"
              onClick={(e) => {
                e.stopPropagation()
                openCardModal(undefined, card)
              }}
            />,
            <DeleteOutlined
              key="delete"
              onClick={(e) => {
                e.stopPropagation()
                deleteCard(card.id)
              }}
            />
          ]}
        >
          <div className="mb-2">
            <h4 className="text-sm font-medium mb-1 line-clamp-2">{card.title}</h4>
            {card.description && (
              <p className="text-xs text-gray-600 line-clamp-3">{card.description}</p>
            )}
          </div>

          <div className="flex flex-wrap gap-1 mb-2">
            <Tag
              color={PRIORITY_COLORS[card.priority]}
              size="small"
              icon={<FlagOutlined />}
            >
              {PRIORITY_LABELS[card.priority]}
            </Tag>
            {card.tags.map(tag => (
              <Tag key={tag} size="small">{tag}</Tag>
            ))}
          </div>

          <div className="flex justify-between items-center text-xs text-gray-500">
            {card.assignee && (
              <span>
                <UserOutlined className="mr-1" />
                {card.assignee}
              </span>
            )}
            {card.dueDate && (
              <span>
                <CalendarOutlined className="mr-1" />
                {new Date(card.dueDate).toLocaleDateString()}
              </span>
            )}
          </div>
        </Card>
      </div>
    )
  }

  /**
   * 可放置列组件
   */
  const DroppableColumn: React.FC<{ column: KanbanColumn }> = ({ column }) => {
    const columnCards = kanbanData.cards.filter(card => card.status === column.status)
    const cardIds = columnCards.map(card => card.id)

    const { setNodeRef, isOver } = useDroppable({
      id: column.status,
    })

    return (
      <div className="flex-1 min-w-80 mx-2">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold text-gray-800">
              {column.title} ({columnCards.length})
            </h3>
            <Button
              type="text"
              size="small"
              icon={<PlusOutlined />}
              onClick={() => openCardModal(column.status)}
            >
              添加卡片
            </Button>
          </div>

          <SortableContext items={cardIds} strategy={verticalListSortingStrategy}>
            <div
              ref={setNodeRef}
              className={`min-h-96 ${
                isOver ? 'bg-blue-50' : ''
              } transition-colors rounded-lg p-2`}
            >
              {columnCards.map((card) => (
                <SortableCard key={card.id} card={card} />
              ))}
            </div>
          </SortableContext>
        </div>
      </div>
    )
  }

  /**
   * 渲染列
   */
  const renderColumn = useCallback((column: KanbanColumn) => (
    <DroppableColumn key={column.id} column={column} />
  ), [kanbanData.cards, openCardModal])

  return (
    <div className={`kanban-board ${className}`}>
      {/* 工具栏 */}
      <div className="mb-4 flex justify-between items-center">
        <h2 className="text-xl font-semibold">看板管理</h2>
        <Button type="primary" onClick={saveKanban}>
          保存看板
        </Button>
      </div>

      {/* 看板内容 */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex overflow-x-auto pb-4">
          {kanbanData.columns.map(renderColumn)}
        </div>

        <DragOverlay>
          {activeCard ? (
            <Card
              size="small"
              className="shadow-lg opacity-90"
              style={{ width: 280 }}
            >
              <div className="mb-2">
                <div className="font-medium text-sm">{activeCard.title}</div>
                {activeCard.description && (
                  <div className="text-xs text-gray-500 mt-1">
                    {activeCard.description}
                  </div>
                )}
              </div>
              <div className="flex items-center justify-between">
                <Tag color={PRIORITY_COLORS[activeCard.priority]} size="small">
                  {PRIORITY_LABELS[activeCard.priority]}
                </Tag>
                {activeCard.assignee && (
                  <div className="text-xs text-gray-500">
                    <UserOutlined className="mr-1" />
                    {activeCard.assignee}
                  </div>
                )}
              </div>
            </Card>
          ) : null}
        </DragOverlay>
      </DndContext>

      {/* 卡片编辑模态框 */}
      <Modal
        title={editingCard ? '编辑卡片' : '新建卡片'}
        open={isCardModalVisible}
        onOk={saveCard}
        onCancel={closeCardModal}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入卡片标题' }]}
          >
            <Input placeholder="请输入卡片标题" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <Input.TextArea 
              rows={3} 
              placeholder="请输入卡片描述" 
            />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="priority"
              label="优先级"
              initialValue={CardPriority.MEDIUM}
            >
              <Select>
                <Select.Option value={CardPriority.LOW}>
                  <Tag color={PRIORITY_COLORS[CardPriority.LOW]}>
                    {PRIORITY_LABELS[CardPriority.LOW]}
                  </Tag>
                </Select.Option>
                <Select.Option value={CardPriority.MEDIUM}>
                  <Tag color={PRIORITY_COLORS[CardPriority.MEDIUM]}>
                    {PRIORITY_LABELS[CardPriority.MEDIUM]}
                  </Tag>
                </Select.Option>
                <Select.Option value={CardPriority.HIGH}>
                  <Tag color={PRIORITY_COLORS[CardPriority.HIGH]}>
                    {PRIORITY_LABELS[CardPriority.HIGH]}
                  </Tag>
                </Select.Option>
                <Select.Option value={CardPriority.URGENT}>
                  <Tag color={PRIORITY_COLORS[CardPriority.URGENT]}>
                    {PRIORITY_LABELS[CardPriority.URGENT]}
                  </Tag>
                </Select.Option>
              </Select>
            </Form.Item>

            <Form.Item name="assignee" label="负责人">
              <Input placeholder="请输入负责人" />
            </Form.Item>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item name="tags" label="标签">
              <Select
                mode="tags"
                placeholder="请输入标签"
                tokenSeparators={[',']}
              />
            </Form.Item>

            <Form.Item name="dueDate" label="截止日期">
              <DatePicker 
                className="w-full"
                placeholder="请选择截止日期" 
              />
            </Form.Item>
          </div>
        </Form>
      </Modal>
    </div>
  )
}

export default KanbanBoard
