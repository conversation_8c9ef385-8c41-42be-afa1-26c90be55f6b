/**
 * 跨格式导航组件
 * 提供不同格式文档间的快速导航和关联展示
 */

import React, { useState, useEffect, useCallback } from 'react'
import {
  Drawer,
  Tree,
  Card,
  Typography,
  Space,
  Tag,
  Button,
  Input,
  Tooltip,
  Badge,
  Empty,
  Spin
} from 'antd'
import {
  BranchesOutlined,
  SearchOutlined,
  FileTextOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined,
  LinkOutlined,
  SwapOutlined,
  EyeOutlined,
  FilterOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { BaseDocument, DocumentType } from '@/types'
import { CrossFormatReference } from '@services/crossFormat/CrossFormatLinkManager'
import { documentManager } from '@services/document/documentManager'
import { formatRelativeTime } from '@utils/index'

const { Text } = Typography
const { Search } = Input
const { TreeNode } = Tree

/**
 * 导航节点接口
 */
interface NavigationNode {
  key: string
  title: string
  type: DocumentType
  document: BaseDocument
  children?: NavigationNode[]
  references?: CrossFormatReference[]
}

/**
 * 跨格式导航组件属性接口
 */
export interface CrossFormatNavigatorProps {
  /** 当前文档 */
  currentDocument?: BaseDocument
  /** 是否显示 */
  visible: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 文档选择回调 */
  onDocumentSelect?: (document: BaseDocument) => void
  /** 样式类名 */
  className?: string
}

/**
 * 跨格式导航组件
 */
const CrossFormatNavigator: React.FC<CrossFormatNavigatorProps> = ({
  currentDocument,
  visible,
  onClose,
  onDocumentSelect,
  className = ''
}) => {
  const navigate = useNavigate()
  
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedTypes, setSelectedTypes] = useState<DocumentType[]>(Object.values(DocumentType))
  const [navigationTree, setNavigationTree] = useState<NavigationNode[]>([])
  const [expandedKeys, setExpandedKeys] = useState<string[]>([])
  const [selectedKeys, setSelectedKeys] = useState<string[]>([])

  /**
   * 获取文档类型图标
   */
  const getDocumentTypeIcon = useCallback((type: DocumentType) => {
    const icons = {
      [DocumentType.TEXT]: <FileTextOutlined className="text-blue-500" />,
      [DocumentType.WHITEBOARD]: <BgColorsOutlined className="text-green-500" />,
      [DocumentType.MINDMAP]: <NodeIndexOutlined className="text-purple-500" />,
      [DocumentType.KANBAN]: <ProjectOutlined className="text-orange-500" />
    }
    return icons[type]
  }, [])

  /**
   * 获取文档类型名称
   */
  const getDocumentTypeName = useCallback((type: DocumentType) => {
    const names = {
      [DocumentType.TEXT]: '文本笔记',
      [DocumentType.WHITEBOARD]: '白板',
      [DocumentType.MINDMAP]: '思维导图',
      [DocumentType.KANBAN]: '看板'
    }
    return names[type]
  }, [])

  /**
   * 构建导航树
   */
  const buildNavigationTree = useCallback(async () => {
    try {
      setLoading(true)
      
      // 获取所有文档
      const allDocuments = await documentManager.getAllDocuments()
      
      // 按类型分组
      const documentsByType = allDocuments.reduce((acc, doc) => {
        if (!acc[doc.type]) {
          acc[doc.type] = []
        }
        acc[doc.type].push(doc)
        return acc
      }, {} as Record<DocumentType, BaseDocument[]>)

      // 构建树结构
      const tree: NavigationNode[] = []
      
      for (const type of selectedTypes) {
        const documents = documentsByType[type] || []
        const filteredDocs = documents.filter(doc => 
          !searchQuery || doc.title.toLowerCase().includes(searchQuery.toLowerCase())
        )

        if (filteredDocs.length > 0) {
          const typeNode: NavigationNode = {
            key: `type-${type}`,
            title: `${getDocumentTypeName(type)} (${filteredDocs.length})`,
            type,
            document: filteredDocs[0], // 临时使用第一个文档
            children: filteredDocs.map(doc => ({
              key: doc.id,
              title: doc.title,
              type: doc.type,
              document: doc
            }))
          }
          tree.push(typeNode)
        }
      }

      setNavigationTree(tree)
      
      // 自动展开当前文档所在的类型节点
      if (currentDocument) {
        setExpandedKeys([`type-${currentDocument.type}`])
        setSelectedKeys([currentDocument.id])
      }
      
    } catch (error) {
      console.error('构建导航树失败:', error)
    } finally {
      setLoading(false)
    }
  }, [selectedTypes, searchQuery, currentDocument, getDocumentTypeName])

  /**
   * 处理节点选择
   */
  const handleNodeSelect = useCallback((selectedKeys: React.Key[], info: any) => {
    const key = selectedKeys[0] as string
    if (!key || key.startsWith('type-')) return

    const node = info.node as any
    const document = node.document as BaseDocument
    
    if (document) {
      onDocumentSelect?.(document)
      navigateToDocument(document)
    }
  }, [onDocumentSelect])

  /**
   * 导航到文档
   */
  const navigateToDocument = useCallback((document: BaseDocument) => {
    switch (document.type) {
      case DocumentType.TEXT:
        navigate(`/editor/${document.id}`)
        break
      case DocumentType.WHITEBOARD:
        navigate(`/whiteboard/${document.id}`)
        break
      case DocumentType.MINDMAP:
        navigate(`/mindmap/${document.id}`)
        break
      case DocumentType.KANBAN:
        navigate(`/kanban/${document.id}`)
        break
      default:
        navigate(`/editor/${document.id}`)
    }
  }, [navigate])

  /**
   * 渲染树节点
   */
  const renderTreeNode = useCallback((node: NavigationNode) => {
    const isTypeNode = node.key.startsWith('type-')
    const isCurrentDocument = currentDocument && node.key === currentDocument.id

    return (
      <TreeNode
        key={node.key}
        title={
          <div className={`flex items-center justify-between ${isCurrentDocument ? 'bg-blue-50 px-2 py-1 rounded' : ''}`}>
            <div className="flex items-center space-x-2">
              {!isTypeNode && getDocumentTypeIcon(node.type)}
              <span className={isCurrentDocument ? 'font-medium text-blue-600' : ''}>
                {node.title}
              </span>
              {isCurrentDocument && (
                <Badge size="small" color="blue" />
              )}
            </div>
            {!isTypeNode && (
              <div className="flex items-center space-x-1">
                <Text type="secondary" className="text-xs">
                  {formatRelativeTime(node.document.updatedAt)}
                </Text>
                <Tooltip title="查看文档">
                  <Button
                    type="text"
                    size="small"
                    icon={<EyeOutlined />}
                    onClick={(e) => {
                      e.stopPropagation()
                      navigateToDocument(node.document)
                    }}
                  />
                </Tooltip>
              </div>
            )}
          </div>
        }
      >
        {node.children?.map(child => renderTreeNode(child))}
      </TreeNode>
    )
  }, [currentDocument, getDocumentTypeIcon, navigateToDocument])

  /**
   * 处理类型筛选
   */
  const handleTypeFilter = useCallback((type: DocumentType) => {
    setSelectedTypes(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    )
  }, [])

  useEffect(() => {
    if (visible) {
      buildNavigationTree()
    }
  }, [visible, buildNavigationTree])

  return (
    <Drawer
      title={
        <div className="flex items-center space-x-2">
          <BranchesOutlined />
          <span>跨格式导航</span>
        </div>
      }
      placement="left"
      width={400}
      open={visible}
      onClose={onClose}
      className={className}
    >
      <div className="space-y-4">
        {/* 搜索栏 */}
        <Search
          placeholder="搜索文档..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onSearch={buildNavigationTree}
          allowClear
        />

        {/* 类型筛选 */}
        <Card size="small" title="文档类型筛选">
          <Space wrap>
            {Object.values(DocumentType).map(type => (
              <Tag.CheckableTag
                key={type}
                checked={selectedTypes.includes(type)}
                onChange={() => handleTypeFilter(type)}
              >
                <div className="flex items-center space-x-1">
                  {getDocumentTypeIcon(type)}
                  <span>{getDocumentTypeName(type)}</span>
                </div>
              </Tag.CheckableTag>
            ))}
          </Space>
        </Card>

        {/* 导航树 */}
        <Card size="small" title="文档导航">
          {loading ? (
            <div className="text-center py-8">
              <Spin />
            </div>
          ) : navigationTree.length > 0 ? (
            <Tree
              showLine
              expandedKeys={expandedKeys}
              selectedKeys={selectedKeys}
              onExpand={setExpandedKeys}
              onSelect={handleNodeSelect}
            >
              {navigationTree.map(node => renderTreeNode(node))}
            </Tree>
          ) : (
            <Empty
              description="没有找到匹配的文档"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </Card>

        {/* 快速操作 */}
        {currentDocument && (
          <Card size="small" title="快速操作">
            <Space direction="vertical" className="w-full">
              <Button
                block
                icon={<SwapOutlined />}
                onClick={() => {
                  // 打开格式转换器
                }}
              >
                格式转换
              </Button>
              <Button
                block
                icon={<LinkOutlined />}
                onClick={() => {
                  // 打开关联创建
                }}
              >
                创建关联
              </Button>
            </Space>
          </Card>
        )}
      </div>
    </Drawer>
  )
}

export default CrossFormatNavigator
