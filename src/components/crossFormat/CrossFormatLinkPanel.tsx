/**
 * 跨格式关联面板组件
 * 提供不同格式文档间的关联创建、展示和管理功能
 */

import React, { useState, useEffect, useCallback } from 'react'
import {
  Card,
  List,
  Button,
  Space,
  Tag,
  Typography,
  Modal,
  Select,
  Input,
  message,
  Tooltip,
  Divider,
  Badge,
  Empty
} from 'antd'
import {
  LinkOutlined,
  BranchesOutlined,
  SwapOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  FileTextOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { BaseDocument, DocumentType } from '@/types'
import { CrossFormatReference, CrossFormatReferenceType } from '@services/crossFormat/CrossFormatLinkManager'
import { documentManager } from '@services/document/documentManager'
import { formatRelativeTime } from '@utils/index'

const { Text, Paragraph } = Typography
const { Option } = Select
const { TextArea } = Input

/**
 * 跨格式关联面板属性接口
 */
export interface CrossFormatLinkPanelProps {
  /** 当前文档 */
  document: BaseDocument
  /** 是否显示 */
  visible?: boolean
  /** 关闭回调 */
  onClose?: () => void
  /** 样式类名 */
  className?: string
}

/**
 * 跨格式关联面板组件
 */
const CrossFormatLinkPanel: React.FC<CrossFormatLinkPanelProps> = ({
  document,
  visible = true,
  onClose,
  className = ''
}) => {
  const navigate = useNavigate()
  
  const [references, setReferences] = useState<CrossFormatReference[]>([])
  const [loading, setLoading] = useState(false)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [selectedTargetType, setSelectedTargetType] = useState<DocumentType>()
  const [selectedReferenceType, setSelectedReferenceType] = useState<CrossFormatReferenceType>(CrossFormatReferenceType.LINK)
  const [targetDocuments, setTargetDocuments] = useState<BaseDocument[]>([])
  const [selectedTargetDocument, setSelectedTargetDocument] = useState<string>()

  /**
   * 获取文档类型图标
   */
  const getDocumentTypeIcon = useCallback((type: DocumentType) => {
    const icons = {
      [DocumentType.TEXT]: <FileTextOutlined className="text-blue-500" />,
      [DocumentType.WHITEBOARD]: <BgColorsOutlined className="text-green-500" />,
      [DocumentType.MINDMAP]: <NodeIndexOutlined className="text-purple-500" />,
      [DocumentType.KANBAN]: <ProjectOutlined className="text-orange-500" />
    }
    return icons[type]
  }, [])

  /**
   * 获取文档类型名称
   */
  const getDocumentTypeName = useCallback((type: DocumentType) => {
    const names = {
      [DocumentType.TEXT]: '文本笔记',
      [DocumentType.WHITEBOARD]: '白板',
      [DocumentType.MINDMAP]: '思维导图',
      [DocumentType.KANBAN]: '看板'
    }
    return names[type]
  }, [])

  /**
   * 获取引用类型名称
   */
  const getReferenceTypeName = useCallback((type: CrossFormatReferenceType) => {
    const names = {
      [CrossFormatReferenceType.EMBED]: '嵌入',
      [CrossFormatReferenceType.LINK]: '链接',
      [CrossFormatReferenceType.EXTRACT]: '提取',
      [CrossFormatReferenceType.TRANSFORM]: '转换'
    }
    return names[type]
  }, [])

  /**
   * 获取引用类型颜色
   */
  const getReferenceTypeColor = useCallback((type: CrossFormatReferenceType) => {
    const colors = {
      [CrossFormatReferenceType.EMBED]: 'blue',
      [CrossFormatReferenceType.LINK]: 'green',
      [CrossFormatReferenceType.EXTRACT]: 'orange',
      [CrossFormatReferenceType.TRANSFORM]: 'purple'
    }
    return colors[type]
  }, [])

  /**
   * 加载跨格式引用
   */
  const loadReferences = useCallback(async () => {
    try {
      setLoading(true)
      // 这里应该调用跨格式关联管理器的API
      // const refs = await crossFormatLinkManager.getReferencesForDocument(document.id)
      // setReferences(refs)
      
      // 临时模拟数据
      setReferences([])
    } catch (error) {
      console.error('加载跨格式引用失败:', error)
      message.error('加载跨格式引用失败')
    } finally {
      setLoading(false)
    }
  }, [document.id])

  /**
   * 加载目标文档列表
   */
  const loadTargetDocuments = useCallback(async (targetType: DocumentType) => {
    try {
      const docs = await documentManager.getDocumentsByType(targetType)
      // 排除当前文档
      const filteredDocs = docs.filter(doc => doc.id !== document.id)
      setTargetDocuments(filteredDocs)
    } catch (error) {
      console.error('加载目标文档失败:', error)
      message.error('加载目标文档失败')
    }
  }, [document.id])

  /**
   * 创建跨格式引用
   */
  const createReference = useCallback(async () => {
    if (!selectedTargetDocument || !selectedTargetType) {
      message.warning('请选择目标文档和引用类型')
      return
    }

    try {
      // 这里应该调用跨格式关联管理器的API
      // await crossFormatLinkManager.createReference({
      //   sourceDocumentId: document.id,
      //   targetDocumentId: selectedTargetDocument,
      //   sourceType: document.type,
      //   targetType: selectedTargetType,
      //   referenceType: selectedReferenceType
      // })

      message.success('跨格式引用创建成功')
      setCreateModalVisible(false)
      loadReferences()
    } catch (error) {
      console.error('创建跨格式引用失败:', error)
      message.error('创建跨格式引用失败')
    }
  }, [document, selectedTargetDocument, selectedTargetType, selectedReferenceType, loadReferences])

  /**
   * 删除跨格式引用
   */
  const deleteReference = useCallback(async (referenceId: string) => {
    try {
      // await crossFormatLinkManager.deleteReference(referenceId)
      message.success('跨格式引用删除成功')
      loadReferences()
    } catch (error) {
      console.error('删除跨格式引用失败:', error)
      message.error('删除跨格式引用失败')
    }
  }, [loadReferences])

  /**
   * 导航到目标文档
   */
  const navigateToDocument = useCallback((targetDoc: BaseDocument) => {
    switch (targetDoc.type) {
      case DocumentType.TEXT:
        navigate(`/editor/${targetDoc.id}`)
        break
      case DocumentType.WHITEBOARD:
        navigate(`/whiteboard/${targetDoc.id}`)
        break
      case DocumentType.MINDMAP:
        navigate(`/mindmap/${targetDoc.id}`)
        break
      case DocumentType.KANBAN:
        navigate(`/kanban/${targetDoc.id}`)
        break
      default:
        navigate(`/editor/${targetDoc.id}`)
    }
  }, [navigate])

  /**
   * 处理目标类型变化
   */
  const handleTargetTypeChange = useCallback((targetType: DocumentType) => {
    setSelectedTargetType(targetType)
    setSelectedTargetDocument(undefined)
    loadTargetDocuments(targetType)
  }, [loadTargetDocuments])

  useEffect(() => {
    if (visible) {
      loadReferences()
    }
  }, [visible, loadReferences])

  if (!visible) {
    return null
  }

  return (
    <Card
      title={
        <div className="flex items-center space-x-2">
          <BranchesOutlined />
          <span>跨格式关联</span>
          <Badge count={references.length} showZero />
        </div>
      }
      extra={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setCreateModalVisible(true)}
        >
          创建关联
        </Button>
      }
      className={className}
    >
      {references.length > 0 ? (
        <List
          dataSource={references}
          renderItem={(reference) => (
            <List.Item
              actions={[
                <Tooltip title="查看">
                  <Button
                    type="text"
                    icon={<EyeOutlined />}
                    onClick={() => {
                      // 导航到目标文档
                      const targetDoc = targetDocuments.find(doc => doc.id === reference.targetDocumentId)
                      if (targetDoc) {
                        navigateToDocument(targetDoc)
                      }
                    }}
                  />
                </Tooltip>,
                <Tooltip title="编辑">
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={() => {
                      // 打开编辑对话框
                    }}
                  />
                </Tooltip>,
                <Tooltip title="删除">
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => deleteReference(reference.id)}
                  />
                </Tooltip>
              ]}
            >
              <List.Item.Meta
                avatar={getDocumentTypeIcon(reference.targetType)}
                title={
                  <div className="flex items-center space-x-2">
                    <span>{reference.targetSelection?.content || '未命名文档'}</span>
                    <Tag color={getReferenceTypeColor(reference.referenceType)}>
                      {getReferenceTypeName(reference.referenceType)}
                    </Tag>
                  </div>
                }
                description={
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <span>{getDocumentTypeName(reference.targetType)}</span>
                      <Divider type="vertical" />
                      <span>{formatRelativeTime(reference.createdAt)}</span>
                    </div>
                    {reference.sourceSelection && (
                      <Paragraph
                        ellipsis={{ rows: 2 }}
                        className="text-sm text-gray-600 mb-0"
                      >
                        {reference.sourceSelection.content}
                      </Paragraph>
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      ) : (
        <Empty
          description="暂无跨格式关联"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )}

      {/* 创建关联对话框 */}
      <Modal
        title="创建跨格式关联"
        open={createModalVisible}
        onOk={createReference}
        onCancel={() => setCreateModalVisible(false)}
        okText="创建"
        cancelText="取消"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">目标文档类型</label>
            <Select
              value={selectedTargetType}
              onChange={handleTargetTypeChange}
              placeholder="选择目标文档类型"
              className="w-full"
            >
              {Object.values(DocumentType).map(type => (
                <Option key={type} value={type}>
                  <div className="flex items-center space-x-2">
                    {getDocumentTypeIcon(type)}
                    <span>{getDocumentTypeName(type)}</span>
                  </div>
                </Option>
              ))}
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">目标文档</label>
            <Select
              value={selectedTargetDocument}
              onChange={setSelectedTargetDocument}
              placeholder="选择目标文档"
              className="w-full"
              disabled={!selectedTargetType}
            >
              {targetDocuments.map(doc => (
                <Option key={doc.id} value={doc.id}>
                  <div className="flex items-center space-x-2">
                    {getDocumentTypeIcon(doc.type)}
                    <span>{doc.title}</span>
                  </div>
                </Option>
              ))}
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">关联类型</label>
            <Select
              value={selectedReferenceType}
              onChange={setSelectedReferenceType}
              className="w-full"
            >
              {Object.values(CrossFormatReferenceType).map(type => (
                <Option key={type} value={type}>
                  <Tag color={getReferenceTypeColor(type)}>
                    {getReferenceTypeName(type)}
                  </Tag>
                </Option>
              ))}
            </Select>
          </div>
        </div>
      </Modal>
    </Card>
  )
}

export default CrossFormatLinkPanel
