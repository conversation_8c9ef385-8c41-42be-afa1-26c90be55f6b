/**
 * 格式转换器组件
 * 提供不同文档格式间的转换功能
 */

import React, { useState, useCallback } from 'react'
import {
  Modal,
  Select,
  Button,
  Space,
  Typography,
  Card,
  Steps,
  Alert,
  Progress,
  message,
  Divider
} from 'antd'
import {
  SwapOutlined,
  FileTextOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined,
  ArrowRightOutlined,
  CheckCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons'
import { BaseDocument, DocumentType } from '@/types'
import { CrossFormatLinkManager } from '@services/crossFormat/CrossFormatLinkManager'
import { documentManager } from '@services/document/documentManager'

const { Text, Title } = Typography
const { Option } = Select
const { Step } = Steps

/**
 * 转换步骤
 */
enum ConversionStep {
  SELECT_TARGET = 0,
  PREVIEW = 1,
  CONVERTING = 2,
  COMPLETE = 3
}

/**
 * 格式转换器属性接口
 */
export interface FormatConverterProps {
  /** 源文档 */
  sourceDocument: BaseDocument
  /** 是否显示 */
  visible: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 转换完成回调 */
  onComplete?: (newDocument: BaseDocument) => void
}

/**
 * 格式转换器组件
 */
const FormatConverter: React.FC<FormatConverterProps> = ({
  sourceDocument,
  visible,
  onClose,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState<ConversionStep>(ConversionStep.SELECT_TARGET)
  const [targetType, setTargetType] = useState<DocumentType>()
  const [previewContent, setPreviewContent] = useState<string>('')
  const [converting, setConverting] = useState(false)
  const [progress, setProgress] = useState(0)
  const [newDocument, setNewDocument] = useState<BaseDocument | null>(null)

  const crossFormatManager = new CrossFormatLinkManager()

  /**
   * 获取文档类型图标
   */
  const getDocumentTypeIcon = useCallback((type: DocumentType) => {
    const icons = {
      [DocumentType.TEXT]: <FileTextOutlined className="text-blue-500" />,
      [DocumentType.WHITEBOARD]: <BgColorsOutlined className="text-green-500" />,
      [DocumentType.MINDMAP]: <NodeIndexOutlined className="text-purple-500" />,
      [DocumentType.KANBAN]: <ProjectOutlined className="text-orange-500" />
    }
    return icons[type]
  }, [])

  /**
   * 获取文档类型名称
   */
  const getDocumentTypeName = useCallback((type: DocumentType) => {
    const names = {
      [DocumentType.TEXT]: '文本笔记',
      [DocumentType.WHITEBOARD]: '白板',
      [DocumentType.MINDMAP]: '思维导图',
      [DocumentType.KANBAN]: '看板'
    }
    return names[type]
  }, [])

  /**
   * 获取可转换的目标类型
   */
  const getAvailableTargetTypes = useCallback(() => {
    const allTypes = Object.values(DocumentType)
    return allTypes.filter(type => type !== sourceDocument.type)
  }, [sourceDocument.type])

  /**
   * 生成预览内容
   */
  const generatePreview = useCallback(async (target: DocumentType) => {
    try {
      const content = await crossFormatManager.convertContent(
        sourceDocument.content,
        sourceDocument.type,
        target
      )
      setPreviewContent(content)
      setCurrentStep(ConversionStep.PREVIEW)
    } catch (error) {
      console.error('生成预览失败:', error)
      message.error('生成预览失败')
    }
  }, [sourceDocument, crossFormatManager])

  /**
   * 执行转换
   */
  const performConversion = useCallback(async () => {
    if (!targetType) return

    try {
      setConverting(true)
      setCurrentStep(ConversionStep.CONVERTING)
      setProgress(0)

      // 模拟转换进度
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 200)

      // 执行实际转换
      const convertedContent = await crossFormatManager.convertContent(
        sourceDocument.content,
        sourceDocument.type,
        targetType
      )

      // 创建新文档
      const newDoc = await documentManager.createDocument({
        title: `${sourceDocument.title} (转换为${getDocumentTypeName(targetType)})`,
        type: targetType,
        content: convertedContent,
        tags: [...sourceDocument.tags, '格式转换']
      })

      clearInterval(progressInterval)
      setProgress(100)
      setNewDocument(newDoc)
      setCurrentStep(ConversionStep.COMPLETE)

      message.success('格式转换完成')
    } catch (error) {
      console.error('格式转换失败:', error)
      message.error('格式转换失败')
    } finally {
      setConverting(false)
    }
  }, [sourceDocument, targetType, crossFormatManager, getDocumentTypeName])

  /**
   * 处理目标类型选择
   */
  const handleTargetTypeSelect = useCallback((type: DocumentType) => {
    setTargetType(type)
    generatePreview(type)
  }, [generatePreview])

  /**
   * 重置状态
   */
  const resetState = useCallback(() => {
    setCurrentStep(ConversionStep.SELECT_TARGET)
    setTargetType(undefined)
    setPreviewContent('')
    setConverting(false)
    setProgress(0)
    setNewDocument(null)
  }, [])

  /**
   * 处理关闭
   */
  const handleClose = useCallback(() => {
    resetState()
    onClose()
  }, [resetState, onClose])

  /**
   * 处理完成
   */
  const handleComplete = useCallback(() => {
    if (newDocument) {
      onComplete?.(newDocument)
    }
    handleClose()
  }, [newDocument, onComplete, handleClose])

  /**
   * 渲染步骤内容
   */
  const renderStepContent = () => {
    switch (currentStep) {
      case ConversionStep.SELECT_TARGET:
        return (
          <div className="space-y-4">
            <Alert
              message="选择目标格式"
              description="请选择要转换到的目标文档格式。系统将自动分析源文档内容并生成相应格式的预览。"
              type="info"
              showIcon
            />
            
            <div>
              <Text strong>目标格式：</Text>
              <Select
                value={targetType}
                onChange={handleTargetTypeSelect}
                placeholder="选择目标格式"
                className="w-full mt-2"
                size="large"
              >
                {getAvailableTargetTypes().map(type => (
                  <Option key={type} value={type}>
                    <div className="flex items-center space-x-2">
                      {getDocumentTypeIcon(type)}
                      <span>{getDocumentTypeName(type)}</span>
                    </div>
                  </Option>
                ))}
              </Select>
            </div>
          </div>
        )

      case ConversionStep.PREVIEW:
        return (
          <div className="space-y-4">
            <Alert
              message="预览转换结果"
              description="以下是转换后的内容预览。如果满意，请点击开始转换按钮。"
              type="success"
              showIcon
            />
            
            <Card
              title={
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getDocumentTypeIcon(sourceDocument.type)}
                    <span>{getDocumentTypeName(sourceDocument.type)}</span>
                  </div>
                  <ArrowRightOutlined />
                  <div className="flex items-center space-x-2">
                    {targetType && getDocumentTypeIcon(targetType)}
                    <span>{targetType && getDocumentTypeName(targetType)}</span>
                  </div>
                </div>
              }
              size="small"
            >
              <div className="max-h-60 overflow-y-auto">
                <pre className="whitespace-pre-wrap text-sm">
                  {previewContent}
                </pre>
              </div>
            </Card>
          </div>
        )

      case ConversionStep.CONVERTING:
        return (
          <div className="space-y-4 text-center">
            <LoadingOutlined className="text-4xl text-blue-500" />
            <Title level={4}>正在转换文档格式...</Title>
            <Progress percent={progress} status="active" />
            <Text type="secondary">
              正在将{getDocumentTypeName(sourceDocument.type)}转换为{targetType && getDocumentTypeName(targetType)}
            </Text>
          </div>
        )

      case ConversionStep.COMPLETE:
        return (
          <div className="space-y-4 text-center">
            <CheckCircleOutlined className="text-4xl text-green-500" />
            <Title level={4}>转换完成！</Title>
            <Alert
              message="格式转换成功"
              description={`已成功将文档转换为${targetType && getDocumentTypeName(targetType)}格式。新文档已保存到您的文档库中。`}
              type="success"
              showIcon
            />
            {newDocument && (
              <Card size="small">
                <div className="flex items-center space-x-2">
                  {getDocumentTypeIcon(newDocument.type)}
                  <span>{newDocument.title}</span>
                </div>
              </Card>
            )}
          </div>
        )

      default:
        return null
    }
  }

  /**
   * 渲染底部按钮
   */
  const renderFooter = () => {
    switch (currentStep) {
      case ConversionStep.SELECT_TARGET:
        return [
          <Button key="cancel" onClick={handleClose}>
            取消
          </Button>
        ]

      case ConversionStep.PREVIEW:
        return [
          <Button key="back" onClick={() => setCurrentStep(ConversionStep.SELECT_TARGET)}>
            返回
          </Button>,
          <Button key="convert" type="primary" onClick={performConversion}>
            开始转换
          </Button>
        ]

      case ConversionStep.CONVERTING:
        return [
          <Button key="converting" disabled loading={converting}>
            转换中...
          </Button>
        ]

      case ConversionStep.COMPLETE:
        return [
          <Button key="close" onClick={handleClose}>
            关闭
          </Button>,
          <Button key="open" type="primary" onClick={handleComplete}>
            打开新文档
          </Button>
        ]

      default:
        return []
    }
  }

  return (
    <Modal
      title={
        <div className="flex items-center space-x-2">
          <SwapOutlined />
          <span>格式转换器</span>
        </div>
      }
      open={visible}
      onCancel={handleClose}
      footer={renderFooter()}
      width={600}
      destroyOnClose
    >
      <div className="space-y-6">
        {/* 步骤指示器 */}
        <Steps current={currentStep} size="small">
          <Step title="选择格式" />
          <Step title="预览内容" />
          <Step title="转换中" />
          <Step title="完成" />
        </Steps>

        <Divider />

        {/* 步骤内容 */}
        {renderStepContent()}
      </div>
    </Modal>
  )
}

export default FormatConverter
