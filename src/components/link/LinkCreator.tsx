/**
 * 链接创建组件
 * 支持文档间的双向链接创建和管理功能
 */

import React, { useState, useCallback, useEffect } from 'react'
import { Modal, Input, List, Avatar, Tag, Button, Space, message, Tooltip } from 'antd'
import { 
  LinkOutlined, 
  SearchOutlined, 
  FileTextOutlined,
  BranchesOutlined,
  TableOutlined,
  EditOutlined
} from '@ant-design/icons'
import { BaseDocument, DocumentType } from '@/types'

const { Search } = Input

/**
 * 链接类型枚举
 */
export enum LinkType {
  REFERENCE = 'reference',     // 引用链接
  RELATED = 'related',        // 相关链接
  DEPENDENCY = 'dependency',   // 依赖链接
  PARENT = 'parent',          // 父子链接
  SIBLING = 'sibling'         // 兄弟链接
}

/**
 * 文档链接接口
 */
export interface DocumentLink {
  id: string
  sourceId: string
  targetId: string
  type: LinkType
  label?: string
  description?: string
  createdAt: Date
  updatedAt: Date
}

/**
 * 链接创建组件属性接口
 */
export interface LinkCreatorProps {
  /** 当前文档 */
  currentDocument: BaseDocument
  /** 所有文档列表 */
  documents: BaseDocument[]
  /** 现有链接列表 */
  existingLinks: DocumentLink[]
  /** 是否显示模态框 */
  visible: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 创建链接回调 */
  onCreateLink: (link: Omit<DocumentLink, 'id' | 'createdAt' | 'updatedAt'>) => void
  /** 删除链接回调 */
  onDeleteLink: (linkId: string) => void
}

/**
 * 链接类型配置
 */
const LINK_TYPE_CONFIG = {
  [LinkType.REFERENCE]: {
    label: '引用',
    color: '#1890ff',
    description: '引用其他文档的内容'
  },
  [LinkType.RELATED]: {
    label: '相关',
    color: '#52c41a',
    description: '与其他文档相关联'
  },
  [LinkType.DEPENDENCY]: {
    label: '依赖',
    color: '#fa8c16',
    description: '依赖其他文档'
  },
  [LinkType.PARENT]: {
    label: '父子',
    color: '#722ed1',
    description: '父子关系链接'
  },
  [LinkType.SIBLING]: {
    label: '兄弟',
    color: '#eb2f96',
    description: '兄弟关系链接'
  }
}

/**
 * 文档类型图标映射
 */
const DOCUMENT_TYPE_ICONS = {
  [DocumentType.TEXT]: <FileTextOutlined />,
  [DocumentType.WHITEBOARD]: <EditOutlined />,
  [DocumentType.MINDMAP]: <BranchesOutlined />,
  [DocumentType.KANBAN]: <TableOutlined />
}

/**
 * 链接创建组件
 */
const LinkCreator: React.FC<LinkCreatorProps> = ({
  currentDocument,
  documents,
  existingLinks,
  visible,
  onClose,
  onCreateLink,
  onDeleteLink
}) => {
  const [searchText, setSearchText] = useState('')
  const [selectedLinkType, setSelectedLinkType] = useState<LinkType>(LinkType.REFERENCE)
  const [filteredDocuments, setFilteredDocuments] = useState<BaseDocument[]>([])

  /**
   * 过滤文档列表
   */
  useEffect(() => {
    const filtered = documents.filter(doc => {
      // 排除当前文档
      if (doc.id === currentDocument.id) return false
      
      // 排除已经链接的文档
      const isLinked = existingLinks.some(link => 
        (link.sourceId === currentDocument.id && link.targetId === doc.id) ||
        (link.targetId === currentDocument.id && link.sourceId === doc.id)
      )
      if (isLinked) return false
      
      // 搜索过滤
      if (searchText) {
        const searchLower = searchText.toLowerCase()
        return doc.title.toLowerCase().includes(searchLower) ||
               doc.content.toLowerCase().includes(searchLower) ||
               doc.tags.some(tag => tag.toLowerCase().includes(searchLower))
      }
      
      return true
    })
    
    setFilteredDocuments(filtered)
  }, [documents, currentDocument.id, existingLinks, searchText])

  /**
   * 创建链接
   */
  const handleCreateLink = useCallback((targetDocument: BaseDocument) => {
    const newLink: Omit<DocumentLink, 'id' | 'createdAt' | 'updatedAt'> = {
      sourceId: currentDocument.id,
      targetId: targetDocument.id,
      type: selectedLinkType,
      label: `${currentDocument.title} → ${targetDocument.title}`,
      description: `${LINK_TYPE_CONFIG[selectedLinkType].description}`
    }
    
    onCreateLink(newLink)
    message.success(`已创建${LINK_TYPE_CONFIG[selectedLinkType].label}链接`)
  }, [currentDocument, selectedLinkType, onCreateLink])

  /**
   * 删除链接
   */
  const handleDeleteLink = useCallback((linkId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个链接吗？',
      onOk: () => {
        onDeleteLink(linkId)
        message.success('链接已删除')
      }
    })
  }, [onDeleteLink])

  /**
   * 获取文档摘要
   */
  const getDocumentSummary = useCallback((doc: BaseDocument) => {
    const maxLength = 100
    const content = doc.content || ''
    return content.length > maxLength 
      ? content.substring(0, maxLength) + '...'
      : content
  }, [])

  /**
   * 获取当前文档的链接
   */
  const currentDocumentLinks = existingLinks.filter(link => 
    link.sourceId === currentDocument.id || link.targetId === currentDocument.id
  )

  return (
    <Modal
      title={
        <Space>
          <LinkOutlined />
          创建文档链接
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      className="link-creator-modal"
    >
      <div className="link-creator-content">
        {/* 当前文档信息 */}
        <div className="current-document-info">
          <h4>当前文档</h4>
          <div className="document-card">
            <Space>
              {DOCUMENT_TYPE_ICONS[currentDocument.type]}
              <span className="document-title">{currentDocument.title}</span>
              <Tag color="blue">{currentDocument.type}</Tag>
            </Space>
          </div>
        </div>

        {/* 链接类型选择 */}
        <div className="link-type-selector">
          <h4>链接类型</h4>
          <Space wrap>
            {Object.entries(LINK_TYPE_CONFIG).map(([type, config]) => (
              <Button
                key={type}
                type={selectedLinkType === type ? 'primary' : 'default'}
                onClick={() => setSelectedLinkType(type as LinkType)}
                style={{ borderColor: config.color }}
              >
                <Tag color={config.color}>{config.label}</Tag>
                {config.description}
              </Button>
            ))}
          </Space>
        </div>

        {/* 搜索框 */}
        <div className="document-search">
          <h4>选择目标文档</h4>
          <Search
            placeholder="搜索文档标题、内容或标签"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            prefix={<SearchOutlined />}
            allowClear
          />
        </div>

        {/* 文档列表 */}
        <div className="document-list">
          <List
            dataSource={filteredDocuments}
            renderItem={(doc) => (
              <List.Item
                actions={[
                  <Button
                    type="primary"
                    size="small"
                    icon={<LinkOutlined />}
                    onClick={() => handleCreateLink(doc)}
                  >
                    创建链接
                  </Button>
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar 
                      icon={DOCUMENT_TYPE_ICONS[doc.type]} 
                      style={{ backgroundColor: '#f0f0f0', color: '#666' }}
                    />
                  }
                  title={
                    <Space>
                      <span>{doc.title}</span>
                      <Tag color="default">{doc.type}</Tag>
                      {doc.tags.map(tag => (
                        <Tag key={tag} size="small">{tag}</Tag>
                      ))}
                    </Space>
                  }
                  description={getDocumentSummary(doc)}
                />
              </List.Item>
            )}
            locale={{ emptyText: '没有找到可链接的文档' }}
          />
        </div>

        {/* 现有链接 */}
        {currentDocumentLinks.length > 0 && (
          <div className="existing-links">
            <h4>现有链接 ({currentDocumentLinks.length})</h4>
            <List
              size="small"
              dataSource={currentDocumentLinks}
              renderItem={(link) => {
                const targetDoc = documents.find(doc => 
                  doc.id === (link.sourceId === currentDocument.id ? link.targetId : link.sourceId)
                )
                
                if (!targetDoc) return null
                
                return (
                  <List.Item
                    actions={[
                      <Tooltip title="删除链接">
                        <Button
                          type="text"
                          size="small"
                          danger
                          onClick={() => handleDeleteLink(link.id)}
                        >
                          删除
                        </Button>
                      </Tooltip>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar 
                          size="small"
                          icon={DOCUMENT_TYPE_ICONS[targetDoc.type]}
                          style={{ backgroundColor: LINK_TYPE_CONFIG[link.type].color }}
                        />
                      }
                      title={
                        <Space>
                          <Tag color={LINK_TYPE_CONFIG[link.type].color}>
                            {LINK_TYPE_CONFIG[link.type].label}
                          </Tag>
                          <span>{targetDoc.title}</span>
                        </Space>
                      }
                      description={link.description}
                    />
                  </List.Item>
                )
              }}
            />
          </div>
        )}
      </div>
    </Modal>
  )
}

export default LinkCreator
