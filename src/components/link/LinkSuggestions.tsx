/**
 * 链接建议组件
 * 显示可能相关的文档链接建议
 */

import React, { useState, useEffect, useCallback } from 'react'
import { 
  Card, 
  List, 
  Button, 
  Tag, 
  Space, 
  Tooltip, 
  Empty, 
  Spin,
  Typography,
  Progress,
  message
} from 'antd'
import {
  BulbOutlined,
  LinkOutlined,
  FileTextOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined,
  PlusOutlined,
  CloseOutlined
} from '@ant-design/icons'
import { BaseDocument, DocumentType, LinkType } from '@/types'
import { linkService, LinkSuggestion } from '@services/link/linkService'

const { Text } = Typography

/**
 * 链接建议组件属性接口
 */
export interface LinkSuggestionsProps {
  documentId: string
  onLinkCreate?: (targetDocument: BaseDocument, linkType: LinkType) => void
  onSuggestionDismiss?: (suggestion: LinkSuggestion) => void
  className?: string
  maxSuggestions?: number
}

/**
 * 链接建议组件
 */
const LinkSuggestions: React.FC<LinkSuggestionsProps> = ({
  documentId,
  onLinkCreate,
  onSuggestionDismiss,
  className = '',
  maxSuggestions = 5
}) => {
  const [suggestions, setSuggestions] = useState<LinkSuggestion[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState<string | null>(null)
  const [dismissedSuggestions, setDismissedSuggestions] = useState<Set<string>>(new Set())

  /**
   * 加载链接建议
   */
  const loadSuggestions = useCallback(async () => {
    try {
      setLoading(true)
      const allSuggestions = await linkService.getLinkSuggestions(documentId, maxSuggestions * 2)
      
      // 过滤掉已忽略的建议
      const filteredSuggestions = allSuggestions
        .filter(suggestion => !dismissedSuggestions.has(suggestion.document.id))
        .slice(0, maxSuggestions)
      
      setSuggestions(filteredSuggestions)
    } catch (error) {
      console.error('加载链接建议失败:', error)
    } finally {
      setLoading(false)
    }
  }, [documentId, maxSuggestions, dismissedSuggestions])

  /**
   * 初始化加载
   */
  useEffect(() => {
    if (documentId) {
      loadSuggestions()
    }
  }, [documentId, loadSuggestions])

  /**
   * 创建链接
   */
  const handleCreateLink = useCallback(async (suggestion: LinkSuggestion) => {
    try {
      setCreating(suggestion.document.id)
      
      if (onLinkCreate) {
        await onLinkCreate(suggestion.document, suggestion.type)
      } else {
        // 默认创建双向链接
        await linkService.createBidirectionalLink(
          documentId,
          suggestion.document.id,
          suggestion.type
        )
      }
      
      message.success(`已创建与"${suggestion.document.title}"的链接`)
      
      // 从建议列表中移除
      setSuggestions(prev => prev.filter(s => s.document.id !== suggestion.document.id))
    } catch (error) {
      console.error('创建链接失败:', error)
      message.error('创建链接失败')
    } finally {
      setCreating(null)
    }
  }, [documentId, onLinkCreate])

  /**
   * 忽略建议
   */
  const handleDismissSuggestion = useCallback((suggestion: LinkSuggestion) => {
    // 添加到已忽略列表
    setDismissedSuggestions(prev => new Set([...prev, suggestion.document.id]))
    
    // 从建议列表中移除
    setSuggestions(prev => prev.filter(s => s.document.id !== suggestion.document.id))
    
    if (onSuggestionDismiss) {
      onSuggestionDismiss(suggestion)
    }
  }, [onSuggestionDismiss])

  /**
   * 获取文档类型图标
   */
  const getDocumentIcon = (type: DocumentType) => {
    const icons = {
      [DocumentType.TEXT]: <FileTextOutlined className="text-blue-500" />,
      [DocumentType.WHITEBOARD]: <BgColorsOutlined className="text-green-500" />,
      [DocumentType.MINDMAP]: <NodeIndexOutlined className="text-purple-500" />,
      [DocumentType.KANBAN]: <ProjectOutlined className="text-orange-500" />
    }
    return icons[type]
  }

  /**
   * 获取链接类型标签
   */
  const getLinkTypeTag = (type: LinkType) => {
    const configs = {
      [LinkType.REFERENCE]: { color: 'blue', text: '引用' },
      [LinkType.EMBED]: { color: 'green', text: '嵌入' },
      [LinkType.RELATED]: { color: 'orange', text: '相关' },
      [LinkType.PARENT_CHILD]: { color: 'purple', text: '父子' }
    }
    
    const config = configs[type]
    return <Tag color={config.color} size="small">{config.text}</Tag>
  }

  /**
   * 获取相关性分数颜色
   */
  const getScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a' // 绿色
    if (score >= 60) return '#faad14' // 橙色
    if (score >= 40) return '#1890ff' // 蓝色
    return '#d9d9d9' // 灰色
  }

  if (loading) {
    return (
      <Card className={`link-suggestions ${className}`} title="链接建议">
        <div className="text-center py-8">
          <Spin tip="分析中..." />
        </div>
      </Card>
    )
  }

  return (
    <Card 
      className={`link-suggestions ${className}`}
      title={
        <Space>
          <BulbOutlined />
          <span>链接建议</span>
          <Text type="secondary">({suggestions.length})</Text>
        </Space>
      }
      size="small"
    >
      {suggestions.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无链接建议"
          className="py-8"
        >
          <Text type="secondary" className="text-sm">
            系统会根据内容相似性、标签等因素推荐相关文档
          </Text>
        </Empty>
      ) : (
        <List
          dataSource={suggestions}
          renderItem={(suggestion) => (
            <List.Item
              className="suggestion-item hover:bg-gray-50 rounded p-2"
              actions={[
                <Tooltip key="create" title="创建链接">
                  <Button
                    type="primary"
                    size="small"
                    icon={<LinkOutlined />}
                    loading={creating === suggestion.document.id}
                    onClick={() => handleCreateLink(suggestion)}
                  />
                </Tooltip>,
                <Tooltip key="dismiss" title="忽略建议">
                  <Button
                    type="text"
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={() => handleDismissSuggestion(suggestion)}
                    className="text-gray-400 hover:text-red-500"
                  />
                </Tooltip>
              ]}
            >
              <List.Item.Meta
                avatar={getDocumentIcon(suggestion.document.type)}
                title={
                  <div className="flex items-center justify-between">
                    <Tooltip title={suggestion.document.title}>
                      <Text className="text-sm font-medium truncate flex-1">
                        {suggestion.document.title}
                      </Text>
                    </Tooltip>
                    {getLinkTypeTag(suggestion.type)}
                  </div>
                }
                description={
                  <div className="space-y-2">
                    {/* 相关性分数 */}
                    <div className="flex items-center space-x-2">
                      <Text className="text-xs text-gray-500">相关性:</Text>
                      <Progress
                        percent={suggestion.score}
                        size="small"
                        strokeColor={getScoreColor(suggestion.score)}
                        showInfo={false}
                        className="flex-1 max-w-20"
                      />
                      <Text className="text-xs text-gray-600">
                        {suggestion.score}%
                      </Text>
                    </div>
                    
                    {/* 建议原因 */}
                    <Text className="text-xs text-gray-600">
                      {suggestion.reason}
                    </Text>
                    
                    {/* 文档标签 */}
                    {suggestion.document.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {suggestion.document.tags.slice(0, 3).map(tag => (
                          <Tag key={tag} size="small" color="default">
                            {tag}
                          </Tag>
                        ))}
                        {suggestion.document.tags.length > 3 && (
                          <Tag size="small" color="default">
                            +{suggestion.document.tags.length - 3}
                          </Tag>
                        )}
                      </div>
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      )}
      
      {suggestions.length > 0 && (
        <div className="text-center mt-3 pt-3 border-t border-gray-100">
          <Text type="secondary" className="text-xs">
            点击 <LinkOutlined className="mx-1" /> 创建链接，点击 <CloseOutlined className="mx-1" /> 忽略建议
          </Text>
        </div>
      )}
    </Card>
  )
}

export default LinkSuggestions
