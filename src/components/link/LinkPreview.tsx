/**
 * 链接预览组件
 * 实现链接悬停预览功能，支持快速查看链接目标内容
 */

import React, { useState, useEffect, useCallback } from 'react'
import { Card, Spin, Tag, Space, Typography, Avatar, Tooltip } from 'antd'
import { 
  FileTextOutlined,
  EditOutlined,
  BranchesOutlined,
  TableOutlined,
  CalendarOutlined,
  TagOutlined,
  UserOutlined
} from '@ant-design/icons'
import { BaseDocument, DocumentType } from '@/types'
import { formatRelativeTime } from '@utils/index'

const { Text, Paragraph, Title } = Typography

/**
 * 链接预览组件属性接口
 */
export interface LinkPreviewProps {
  /** 目标文档ID */
  documentId: string
  /** 是否显示预览 */
  visible: boolean
  /** 预览位置 */
  position?: { x: number; y: number }
  /** 获取文档数据的回调 */
  onGetDocument?: (documentId: string) => Promise<BaseDocument | null>
  /** 样式类名 */
  className?: string
}

/**
 * 文档类型图标映射
 */
const DOCUMENT_TYPE_ICONS = {
  [DocumentType.TEXT]: <FileTextOutlined />,
  [DocumentType.WHITEBOARD]: <EditOutlined />,
  [DocumentType.MINDMAP]: <BranchesOutlined />,
  [DocumentType.KANBAN]: <TableOutlined />
}

/**
 * 文档类型颜色映射
 */
const DOCUMENT_TYPE_COLORS = {
  [DocumentType.TEXT]: '#1890ff',
  [DocumentType.WHITEBOARD]: '#52c41a',
  [DocumentType.MINDMAP]: '#fa8c16',
  [DocumentType.KANBAN]: '#722ed1'
}

/**
 * 链接预览组件
 */
const LinkPreview: React.FC<LinkPreviewProps> = ({
  documentId,
  visible,
  position = { x: 0, y: 0 },
  onGetDocument,
  className = ''
}) => {
  const [document, setDocument] = useState<BaseDocument | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  /**
   * 加载文档数据
   */
  const loadDocument = useCallback(async () => {
    if (!documentId || !visible || !onGetDocument) return

    setLoading(true)
    setError(null)

    try {
      const doc = await onGetDocument(documentId)
      setDocument(doc)
    } catch (err) {
      setError('加载文档失败')
      console.error('加载文档预览失败:', err)
    } finally {
      setLoading(false)
    }
  }, [documentId, visible, onGetDocument])

  /**
   * 当可见性或文档ID变化时加载数据
   */
  useEffect(() => {
    if (visible) {
      loadDocument()
    } else {
      setDocument(null)
      setError(null)
    }
  }, [visible, loadDocument])

  /**
   * 获取文档内容摘要
   */
  const getContentPreview = useCallback((content: string, type: DocumentType) => {
    if (!content) return '暂无内容'

    const maxLength = 200

    try {
      // 对于不同类型的文档，尝试解析内容
      switch (type) {
        case DocumentType.TEXT:
          // 文本文档直接显示内容
          return content.length > maxLength 
            ? content.substring(0, maxLength) + '...'
            : content

        case DocumentType.WHITEBOARD:
          // 白板文档显示对象数量
          const whiteboardData = JSON.parse(content)
          const objectCount = whiteboardData.objects?.length || 0
          return `包含 ${objectCount} 个绘图对象`

        case DocumentType.MINDMAP:
          // 思维导图显示节点数量
          const mindmapData = JSON.parse(content)
          const nodeCount = mindmapData.nodes?.length || 0
          return `包含 ${nodeCount} 个思维导图节点`

        case DocumentType.KANBAN:
          // 看板显示卡片数量
          const kanbanData = JSON.parse(content)
          const cardCount = kanbanData.cards?.length || 0
          return `包含 ${cardCount} 张看板卡片`

        default:
          return content.length > maxLength 
            ? content.substring(0, maxLength) + '...'
            : content
      }
    } catch {
      // 如果解析失败，直接显示文本内容
      return content.length > maxLength 
        ? content.substring(0, maxLength) + '...'
        : content
    }
  }, [])

  if (!visible) {
    return null
  }

  const style: React.CSSProperties = {
    position: 'fixed',
    left: position.x,
    top: position.y,
    zIndex: 1000,
    maxWidth: 400,
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    border: '1px solid #d9d9d9'
  }

  return (
    <Card
      className={`link-preview ${className}`}
      style={style}
      size="small"
      loading={loading}
    >
      {error ? (
        <div className="preview-error">
          <Text type="danger">{error}</Text>
        </div>
      ) : document ? (
        <div className="preview-content">
          {/* 文档标题和类型 */}
          <div className="preview-header">
            <Space>
              <Avatar 
                size="small"
                icon={DOCUMENT_TYPE_ICONS[document.type]}
                style={{ 
                  backgroundColor: DOCUMENT_TYPE_COLORS[document.type],
                  color: 'white'
                }}
              />
              <Title level={5} style={{ margin: 0 }}>
                {document.title}
              </Title>
              <Tag color={DOCUMENT_TYPE_COLORS[document.type]}>
                {document.type}
              </Tag>
            </Space>
          </div>

          {/* 文档内容预览 */}
          <div className="preview-body" style={{ marginTop: 12 }}>
            <Paragraph 
              ellipsis={{ rows: 4, expandable: false }}
              style={{ margin: 0, fontSize: '13px', color: '#666' }}
            >
              {getContentPreview(document.content, document.type)}
            </Paragraph>
          </div>

          {/* 文档标签 */}
          {document.tags.length > 0 && (
            <div className="preview-tags" style={{ marginTop: 8 }}>
              <Space size={[4, 4]} wrap>
                <TagOutlined style={{ fontSize: '12px', color: '#999' }} />
                {document.tags.map(tag => (
                  <Tag key={tag} size="small">{tag}</Tag>
                ))}
              </Space>
            </div>
          )}

          {/* 文档元信息 */}
          <div className="preview-meta" style={{ marginTop: 8, fontSize: '12px', color: '#999' }}>
            <Space split={<span>•</span>}>
              <Tooltip title="创建时间">
                <Space size={4}>
                  <CalendarOutlined />
                  <span>{formatRelativeTime(document.createdAt)}</span>
                </Space>
              </Tooltip>
              
              {document.updatedAt && document.updatedAt !== document.createdAt && (
                <Tooltip title="更新时间">
                  <span>更新于 {formatRelativeTime(document.updatedAt)}</span>
                </Tooltip>
              )}

              {document.author && (
                <Tooltip title="作者">
                  <Space size={4}>
                    <UserOutlined />
                    <span>{document.author}</span>
                  </Space>
                </Tooltip>
              )}
            </Space>
          </div>
        </div>
      ) : (
        <div className="preview-loading">
          <Spin size="small" />
          <Text style={{ marginLeft: 8 }}>加载中...</Text>
        </div>
      )}
    </Card>
  )
}

/**
 * 链接预览Hook
 * 提供链接预览的状态管理和事件处理
 */
export const useLinkPreview = () => {
  const [previewState, setPreviewState] = useState<{
    visible: boolean
    documentId: string | null
    position: { x: number; y: number }
  }>({
    visible: false,
    documentId: null,
    position: { x: 0, y: 0 }
  })

  /**
   * 显示预览
   */
  const showPreview = useCallback((documentId: string, event: React.MouseEvent) => {
    const rect = (event.target as HTMLElement).getBoundingClientRect()
    setPreviewState({
      visible: true,
      documentId,
      position: {
        x: rect.right + 10,
        y: rect.top
      }
    })
  }, [])

  /**
   * 隐藏预览
   */
  const hidePreview = useCallback(() => {
    setPreviewState(prev => ({
      ...prev,
      visible: false
    }))
  }, [])

  /**
   * 更新预览位置
   */
  const updatePosition = useCallback((event: React.MouseEvent) => {
    if (previewState.visible) {
      const rect = (event.target as HTMLElement).getBoundingClientRect()
      setPreviewState(prev => ({
        ...prev,
        position: {
          x: rect.right + 10,
          y: rect.top
        }
      }))
    }
  }, [previewState.visible])

  return {
    previewState,
    showPreview,
    hidePreview,
    updatePosition
  }
}

export default LinkPreview
