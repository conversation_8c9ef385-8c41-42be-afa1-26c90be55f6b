/**
 * 链接编辑对话框组件
 * 提供链接信息的编辑功能
 */

import React, { useState, useEffect, useCallback } from 'react'
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Space,
  message,
  Typography,
  Divider,
  Tag
} from 'antd'
import {
  LinkOutlined,
  FileTextOutlined,
  EditOutlined,
  BranchesOutlined,
  TableOutlined
} from '@ant-design/icons'
import { DocumentLink, BaseDocument, LinkType, DocumentType } from '@/types'

const { Text } = Typography
const { TextArea } = Input

/**
 * 链接编辑对话框属性接口
 */
export interface LinkEditModalProps {
  /** 是否显示 */
  visible: boolean
  /** 要编辑的链接 */
  link: DocumentLink | null
  /** 源文档 */
  sourceDocument?: BaseDocument
  /** 目标文档 */
  targetDocument?: BaseDocument
  /** 关闭回调 */
  onClose: () => void
  /** 保存回调 */
  onSave: (linkId: string, updates: Partial<DocumentLink>) => Promise<void>
}

/**
 * 链接类型配置
 */
const LINK_TYPE_CONFIG = {
  [LinkType.REFERENCE]: {
    label: '引用',
    color: 'blue',
    description: '引用其他文档的内容'
  },
  [LinkType.EMBED]: {
    label: '嵌入',
    color: 'green',
    description: '嵌入其他文档的内容'
  },
  [LinkType.RELATED]: {
    label: '相关',
    color: 'orange',
    description: '与其他文档相关联'
  },
  [LinkType.PARENT_CHILD]: {
    label: '父子',
    color: 'purple',
    description: '父子关系链接'
  }
}

/**
 * 文档类型图标映射
 */
const DOCUMENT_TYPE_ICONS = {
  [DocumentType.TEXT]: <FileTextOutlined />,
  [DocumentType.WHITEBOARD]: <EditOutlined />,
  [DocumentType.MINDMAP]: <BranchesOutlined />,
  [DocumentType.KANBAN]: <TableOutlined />
}

/**
 * 链接编辑对话框组件
 */
const LinkEditModal: React.FC<LinkEditModalProps> = ({
  visible,
  link,
  sourceDocument,
  targetDocument,
  onClose,
  onSave
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  /**
   * 初始化表单数据
   */
  useEffect(() => {
    if (visible && link) {
      form.setFieldsValue({
        type: link.type,
        label: link.label || '',
        description: link.description || ''
      })
    }
  }, [visible, link, form])

  /**
   * 处理保存
   */
  const handleSave = useCallback(async () => {
    if (!link) return

    try {
      const values = await form.validateFields()
      setLoading(true)

      await onSave(link.id, {
        type: values.type,
        label: values.label.trim() || undefined,
        description: values.description.trim() || undefined,
        updatedAt: new Date()
      })

      message.success('链接更新成功')
      onClose()
    } catch (error) {
      if (error.errorFields) {
        // 表单验证错误
        return
      }
      console.error('更新链接失败:', error)
      message.error('更新链接失败')
    } finally {
      setLoading(false)
    }
  }, [link, form, onSave, onClose])

  /**
   * 处理取消
   */
  const handleCancel = useCallback(() => {
    form.resetFields()
    onClose()
  }, [form, onClose])

  /**
   * 渲染文档信息
   */
  const renderDocumentInfo = (document: BaseDocument | undefined, title: string) => {
    if (!document) {
      return (
        <div className="text-center py-4">
          <Text type="secondary">文档信息不可用</Text>
        </div>
      )
    }

    return (
      <div className="p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-3 mb-2">
          <div className="text-lg">
            {DOCUMENT_TYPE_ICONS[document.type]}
          </div>
          <div className="flex-1">
            <Text strong className="text-base">{document.title}</Text>
            <div className="flex items-center space-x-2 mt-1">
              <Tag color="default" size="small">{document.type}</Tag>
              {document.tags?.map(tag => (
                <Tag key={tag} size="small">{tag}</Tag>
              ))}
            </div>
          </div>
        </div>
        {document.description && (
          <Text type="secondary" className="text-sm">
            {document.description}
          </Text>
        )}
      </div>
    )
  }

  return (
    <Modal
      title={
        <div className="flex items-center space-x-2">
          <LinkOutlined />
          <span>编辑链接</span>
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      footer={
        <Space>
          <Button onClick={handleCancel}>
            取消
          </Button>
          <Button
            type="primary"
            loading={loading}
            onClick={handleSave}
          >
            保存
          </Button>
        </Space>
      }
      width={600}
      className="link-edit-modal"
    >
      <div className="space-y-6">
        {/* 源文档信息 */}
        <div>
          <Text strong className="block mb-2">源文档</Text>
          {renderDocumentInfo(sourceDocument, '源文档')}
        </div>

        <Divider />

        {/* 目标文档信息 */}
        <div>
          <Text strong className="block mb-2">目标文档</Text>
          {renderDocumentInfo(targetDocument, '目标文档')}
        </div>

        <Divider />

        {/* 链接信息编辑 */}
        <Form
          form={form}
          layout="vertical"
          className="space-y-4"
        >
          <Form.Item
            name="type"
            label="链接类型"
            rules={[{ required: true, message: '请选择链接类型' }]}
          >
            <Select placeholder="选择链接类型">
              {Object.entries(LINK_TYPE_CONFIG).map(([type, config]) => (
                <Select.Option key={type} value={type}>
                  <div className="flex items-center justify-between">
                    <Space>
                      <Tag color={config.color}>{config.label}</Tag>
                      <span>{config.description}</span>
                    </Space>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="label"
            label="链接标签"
            rules={[
              { max: 100, message: '标签长度不能超过100个字符' }
            ]}
          >
            <Input
              placeholder="为链接添加一个简短的标签（可选）"
              allowClear
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="链接描述"
            rules={[
              { max: 500, message: '描述长度不能超过500个字符' }
            ]}
          >
            <TextArea
              placeholder="描述这个链接的用途或关系（可选）"
              rows={3}
              allowClear
            />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  )
}

export default LinkEditModal
