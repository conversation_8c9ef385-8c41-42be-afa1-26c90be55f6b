/**
 * 快速导航组件
 * 提供快速访问最近文档、收藏文档、常用功能等导航功能
 */

import React, { useState, useEffect, useCallback } from 'react'
import {
  Drawer,
  List,
  Avatar,
  Tag,
  Button,
  Space,
  Divider,
  Typography,
  Input,
  Tabs,
  Badge,
  Empty,
  Tooltip
} from 'antd'
import {
  SearchOutlined,
  FileTextOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined,
  StarOutlined,
  HistoryOutlined,
  ClockCircleOutlined,
  TagOutlined,
  FolderOutlined,
  RocketOutlined,
  CloseOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { BaseDocument, DocumentType } from '@/types'
import { documentManager } from '@services/document/documentManager'
import { formatRelativeTime } from '@utils/index'

const { Text, Title } = Typography
const { Search } = Input
const { TabPane } = Tabs

/**
 * 快速导航组件属性接口
 */
export interface QuickNavigationProps {
  /** 是否显示 */
  visible: boolean
  /** 关闭回调 */
  onClose: () => void
  /** 自定义类名 */
  className?: string
}

/**
 * 文档类型图标映射
 */
const DOCUMENT_TYPE_ICONS = {
  [DocumentType.TEXT]: <FileTextOutlined />,
  [DocumentType.WHITEBOARD]: <BgColorsOutlined />,
  [DocumentType.MINDMAP]: <NodeIndexOutlined />,
  [DocumentType.KANBAN]: <ProjectOutlined />
}

/**
 * 文档类型颜色映射
 */
const DOCUMENT_TYPE_COLORS = {
  [DocumentType.TEXT]: '#1890ff',
  [DocumentType.WHITEBOARD]: '#52c41a',
  [DocumentType.MINDMAP]: '#722ed1',
  [DocumentType.KANBAN]: '#fa8c16'
}

/**
 * 快速操作配置
 */
const QUICK_ACTIONS = [
  {
    key: 'new-text',
    title: '新建文本文档',
    icon: <FileTextOutlined />,
    color: '#1890ff',
    path: '/editor'
  },
  {
    key: 'new-whiteboard',
    title: '新建白板',
    icon: <BgColorsOutlined />,
    color: '#52c41a',
    path: '/whiteboard'
  },
  {
    key: 'new-mindmap',
    title: '新建思维导图',
    icon: <NodeIndexOutlined />,
    color: '#722ed1',
    path: '/mindmap'
  },
  {
    key: 'new-kanban',
    title: '新建看板',
    icon: <ProjectOutlined />,
    color: '#fa8c16',
    path: '/kanban'
  }
]

/**
 * 快速导航组件
 */
const QuickNavigation: React.FC<QuickNavigationProps> = ({
  visible,
  onClose,
  className = ''
}) => {
  const navigate = useNavigate()
  
  const [recentDocuments, setRecentDocuments] = useState<BaseDocument[]>([])
  const [favoriteDocuments, setFavoriteDocuments] = useState<BaseDocument[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<BaseDocument[]>([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('recent')

  /**
   * 加载数据
   */
  useEffect(() => {
    if (visible) {
      loadRecentDocuments()
      loadFavoriteDocuments()
    }
  }, [visible])

  /**
   * 加载最近文档
   */
  const loadRecentDocuments = useCallback(async () => {
    try {
      const stats = await documentManager.getDocumentStats()
      if (stats.recentActivity) {
        const recentIds = stats.recentActivity.slice(0, 10).map(item => item.documentId)
        const documents = await Promise.all(
          recentIds.map(id => documentManager.getDocumentById(id))
        )
        setRecentDocuments(documents.filter(Boolean) as BaseDocument[])
      }
    } catch (error) {
      console.error('加载最近文档失败:', error)
    }
  }, [])

  /**
   * 加载收藏文档
   */
  const loadFavoriteDocuments = useCallback(async () => {
    try {
      // 这里可以从用户设置或数据库加载收藏文档
      // 暂时使用模拟数据
      setFavoriteDocuments([])
    } catch (error) {
      console.error('加载收藏文档失败:', error)
    }
  }, [])

  /**
   * 搜索文档
   */
  const searchDocuments = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSearchResults([])
      return
    }

    try {
      setLoading(true)
      const results = await documentManager.searchDocuments({
        query,
        limit: 10
      })
      setSearchResults(results)
    } catch (error) {
      console.error('搜索文档失败:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * 处理搜索
   */
  const handleSearch = useCallback((value: string) => {
    setSearchQuery(value)
    searchDocuments(value)
  }, [searchDocuments])

  /**
   * 导航到文档
   */
  const navigateToDocument = useCallback((document: BaseDocument) => {
    const pathMap = {
      [DocumentType.TEXT]: '/editor',
      [DocumentType.WHITEBOARD]: '/whiteboard',
      [DocumentType.MINDMAP]: '/mindmap',
      [DocumentType.KANBAN]: '/kanban'
    }
    
    navigate(`${pathMap[document.type]}/${document.id}`)
    onClose()
  }, [navigate, onClose])

  /**
   * 快速创建文档
   */
  const handleQuickAction = useCallback((action: typeof QUICK_ACTIONS[0]) => {
    navigate(action.path)
    onClose()
  }, [navigate, onClose])

  /**
   * 渲染文档列表项
   */
  const renderDocumentItem = useCallback((document: BaseDocument) => (
    <List.Item
      key={document.id}
      onClick={() => navigateToDocument(document)}
      style={{ cursor: 'pointer' }}
      className="hover:bg-gray-50 transition-colors"
    >
      <List.Item.Meta
        avatar={
          <Avatar
            icon={DOCUMENT_TYPE_ICONS[document.type]}
            style={{ 
              backgroundColor: DOCUMENT_TYPE_COLORS[document.type],
              color: '#fff'
            }}
          />
        }
        title={
          <Space>
            <span>{document.title}</span>
            <Tag color={DOCUMENT_TYPE_COLORS[document.type]} size="small">
              {document.type}
            </Tag>
          </Space>
        }
        description={
          <div>
            <div className="text-gray-500 text-sm">
              {formatRelativeTime(document.updatedAt)}
            </div>
            {document.tags.length > 0 && (
              <div className="mt-1">
                {document.tags.slice(0, 3).map(tag => (
                  <Tag key={tag} size="small">{tag}</Tag>
                ))}
                {document.tags.length > 3 && (
                  <Tag size="small">+{document.tags.length - 3}</Tag>
                )}
              </div>
            )}
          </div>
        }
      />
    </List.Item>
  ), [navigateToDocument])

  return (
    <Drawer
      title={
        <Space>
          <RocketOutlined />
          快速导航
        </Space>
      }
      placement="right"
      width={400}
      open={visible}
      onClose={onClose}
      className={className}
      extra={
        <Button type="text" icon={<CloseOutlined />} onClick={onClose} />
      }
    >
      <div className="quick-navigation-content">
        {/* 搜索框 */}
        <div className="mb-4">
          <Search
            placeholder="快速搜索文档..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            loading={loading}
            allowClear
          />
        </div>

        {/* 快速操作 */}
        <div className="mb-6">
          <Title level={5}>
            <Space>
              <RocketOutlined />
              快速创建
            </Space>
          </Title>
          <div className="grid grid-cols-2 gap-2">
            {QUICK_ACTIONS.map(action => (
              <Button
                key={action.key}
                type="dashed"
                icon={action.icon}
                onClick={() => handleQuickAction(action)}
                className="h-12 text-left"
                style={{ borderColor: action.color }}
              >
                <div className="text-xs">{action.title}</div>
              </Button>
            ))}
          </div>
        </div>

        {/* 文档列表 */}
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Space>
                <HistoryOutlined />
                最近文档
                {recentDocuments.length > 0 && (
                  <Badge count={recentDocuments.length} size="small" />
                )}
              </Space>
            }
            key="recent"
          >
            <List
              dataSource={recentDocuments}
              renderItem={renderDocumentItem}
              locale={{ emptyText: <Empty description="暂无最近文档" /> }}
            />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <StarOutlined />
                收藏文档
                {favoriteDocuments.length > 0 && (
                  <Badge count={favoriteDocuments.length} size="small" />
                )}
              </Space>
            }
            key="favorites"
          >
            <List
              dataSource={favoriteDocuments}
              renderItem={renderDocumentItem}
              locale={{ emptyText: <Empty description="暂无收藏文档" /> }}
            />
          </TabPane>

          {searchQuery && (
            <TabPane
              tab={
                <Space>
                  <SearchOutlined />
                  搜索结果
                  {searchResults.length > 0 && (
                    <Badge count={searchResults.length} size="small" />
                  )}
                </Space>
              }
              key="search"
            >
              <List
                loading={loading}
                dataSource={searchResults}
                renderItem={renderDocumentItem}
                locale={{ emptyText: <Empty description="没有找到相关文档" /> }}
              />
            </TabPane>
          )}
        </Tabs>
      </div>
    </Drawer>
  )
}

export default QuickNavigation
