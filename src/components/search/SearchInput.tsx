/**
 * 搜索输入组件
 * 提供实时搜索、搜索建议、搜索历史等功能
 */

import React, { useState, useEffect, useCallback, useRef } from 'react'
import {
  Input,
  AutoComplete,
  Button,
  Dropdown,
  Tag,
  Space,
  Divider,
  Empty,
  Spin
} from 'antd'
import {
  SearchOutlined,
  CloseOutlined,
  HistoryOutlined,
  FilterOutlined,
  ClearOutlined
} from '@ant-design/icons'
import { BaseDocument, DocumentType, SearchOptions } from '@/types'
import { documentManager } from '@services/document/documentManager'
import { advancedSearchService, SearchSuggestion as AdvancedSearchSuggestion } from '@services/search/AdvancedSearchService'
import { debounce } from '@utils/index'

const { Search } = Input

/**
 * 搜索建议项接口
 */
export interface SearchSuggestion {
  value: string
  type: 'document' | 'tag' | 'history'
  document?: BaseDocument
  count?: number
}

/**
 * 搜索输入组件属性接口
 */
export interface SearchInputProps {
  /** 搜索值 */
  value?: string
  /** 搜索回调 */
  onSearch?: (query: string, options?: SearchOptions) => void
  /** 值变化回调 */
  onChange?: (value: string) => void
  /** 占位符 */
  placeholder?: string
  /** 是否显示高级搜索 */
  showAdvanced?: boolean
  /** 是否显示搜索历史 */
  showHistory?: boolean
  /** 是否显示搜索建议 */
  showSuggestions?: boolean
  /** 样式类名 */
  className?: string
  /** 尺寸 */
  size?: 'small' | 'middle' | 'large'
}

// 本地存储键名
const SEARCH_HISTORY_KEY = 'multidimensional-notes-search-history'
const MAX_HISTORY_ITEMS = 10

/**
 * 搜索输入组件
 */
const SearchInput: React.FC<SearchInputProps> = ({
  value = '',
  onSearch,
  onChange,
  placeholder = '搜索文档、标签...',
  showAdvanced = true,
  showHistory = true,
  showSuggestions = true,
  className = '',
  size = 'middle'
}) => {
  const [inputValue, setInputValue] = useState(value)
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)
  const [advancedFilters, setAdvancedFilters] = useState<SearchOptions>({})
  const inputRef = useRef<any>(null)

  /**
   * 加载搜索历史
   */
  useEffect(() => {
    try {
      // 从高级搜索服务加载搜索历史
      const history = advancedSearchService.getSearchHistory(MAX_HISTORY_ITEMS)
      setSearchHistory(history.map(item => item.query))
    } catch (error) {
      console.warn('加载搜索历史失败:', error)
      // 回退到本地存储
      try {
        const localHistory = localStorage.getItem(SEARCH_HISTORY_KEY)
        if (localHistory) {
          setSearchHistory(JSON.parse(localHistory))
        }
      } catch (localError) {
        console.warn('加载本地搜索历史失败:', localError)
      }
    }
  }, [])

  /**
   * 保存搜索历史
   */
  const saveSearchHistory = useCallback((query: string) => {
    if (!query.trim()) return

    try {
      const newHistory = [query, ...searchHistory.filter(item => item !== query)]
        .slice(0, MAX_HISTORY_ITEMS)
      
      setSearchHistory(newHistory)
      localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory))
    } catch (error) {
      console.warn('保存搜索历史失败:', error)
    }
  }, [searchHistory])

  /**
   * 获取搜索建议
   */
  const getSuggestions = useCallback(
    debounce(async (query: string) => {
      if (!query.trim() || !showSuggestions) {
        setSuggestions([])
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        
        // 获取文档建议
        const documentResults = await documentManager.searchDocuments(query, {
          limit: 5
        })
        
        // 获取标签建议
        const allTags = await documentManager.getAllTags()
        const tagSuggestions = allTags
          .filter(tag => tag.toLowerCase().includes(query.toLowerCase()))
          .slice(0, 3)

        const newSuggestions: SearchSuggestion[] = [
          // 文档建议
          ...documentResults.map(doc => ({
            value: doc.title,
            type: 'document' as const,
            document: doc
          })),
          // 标签建议
          ...tagSuggestions.map(tag => ({
            value: `标签:${tag}`,
            type: 'tag' as const,
            count: 0 // 这里应该是标签的文档数量
          }))
        ]

        setSuggestions(newSuggestions)
      } catch (error) {
        console.error('获取搜索建议失败:', error)
        setSuggestions([])
      } finally {
        setLoading(false)
      }
    }, 300),
    [showSuggestions]
  )

  /**
   * 处理输入值变化
   */
  const handleInputChange = useCallback((newValue: string) => {
    setInputValue(newValue)
    onChange?.(newValue)
    
    if (newValue.trim()) {
      getSuggestions(newValue)
    } else {
      setSuggestions([])
    }
  }, [onChange, getSuggestions])

  /**
   * 处理搜索
   */
  const handleSearch = useCallback((searchValue?: string) => {
    const query = searchValue || inputValue
    if (!query.trim()) return

    saveSearchHistory(query)
    onSearch?.(query, advancedFilters)
    setShowDropdown(false)
  }, [inputValue, advancedFilters, onSearch, saveSearchHistory])

  /**
   * 处理建议选择
   */
  const handleSuggestionSelect = useCallback((suggestion: SearchSuggestion) => {
    if (suggestion.type === 'document' && suggestion.document) {
      // 直接跳转到文档
      setInputValue(suggestion.value)
      handleSearch(suggestion.value)
    } else if (suggestion.type === 'tag') {
      // 搜索标签
      const tagName = suggestion.value.replace('标签:', '')
      setInputValue(tagName)
      handleSearch(tagName)
    } else {
      // 历史记录
      setInputValue(suggestion.value)
      handleSearch(suggestion.value)
    }
  }, [handleSearch])

  /**
   * 清除搜索历史
   */
  const clearSearchHistory = useCallback(() => {
    setSearchHistory([])
    localStorage.removeItem(SEARCH_HISTORY_KEY)
  }, [])

  /**
   * 渲染下拉内容
   */
  const renderDropdownContent = () => {
    const hasHistory = showHistory && searchHistory.length > 0
    const hasSuggestions = suggestions.length > 0

    if (!hasHistory && !hasSuggestions && !loading) {
      return (
        <div className="p-4">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无搜索建议"
            className="text-center"
          />
        </div>
      )
    }

    return (
      <div className="search-dropdown-content max-h-80 overflow-y-auto">
        {/* 搜索建议 */}
        {hasSuggestions && (
          <div className="p-2">
            <div className="text-xs text-gray-500 mb-2 px-2">搜索建议</div>
            {suggestions.map((suggestion, index) => (
              <div
                key={index}
                className="flex items-center p-2 hover:bg-gray-50 cursor-pointer rounded"
                onClick={() => handleSuggestionSelect(suggestion)}
              >
                <SearchOutlined className="text-gray-400 mr-2" />
                <div className="flex-1">
                  <div className="text-sm">{suggestion.value}</div>
                  {suggestion.document && (
                    <div className="text-xs text-gray-500">
                      {suggestion.document.type} • {new Date(suggestion.document.updatedAt).toLocaleDateString()}
                    </div>
                  )}
                </div>
                {suggestion.type === 'tag' && suggestion.count !== undefined && (
                  <Tag size="small">{suggestion.count}</Tag>
                )}
              </div>
            ))}
          </div>
        )}

        {/* 分隔线 */}
        {hasSuggestions && hasHistory && <Divider className="my-1" />}

        {/* 搜索历史 */}
        {hasHistory && (
          <div className="p-2">
            <div className="flex items-center justify-between mb-2 px-2">
              <span className="text-xs text-gray-500">搜索历史</span>
              <Button
                type="text"
                size="small"
                icon={<ClearOutlined />}
                onClick={clearSearchHistory}
                className="text-xs"
              >
                清除
              </Button>
            </div>
            {searchHistory.map((historyItem, index) => (
              <div
                key={index}
                className="flex items-center p-2 hover:bg-gray-50 cursor-pointer rounded"
                onClick={() => handleSuggestionSelect({ value: historyItem, type: 'history' })}
              >
                <HistoryOutlined className="text-gray-400 mr-2" />
                <span className="text-sm flex-1">{historyItem}</span>
                <Button
                  type="text"
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={(e) => {
                    e.stopPropagation()
                    const newHistory = searchHistory.filter((_, i) => i !== index)
                    setSearchHistory(newHistory)
                    localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory))
                  }}
                  className="opacity-0 group-hover:opacity-100"
                />
              </div>
            ))}
          </div>
        )}

        {/* 加载状态 */}
        {loading && (
          <div className="p-4 text-center">
            <Spin size="small" />
            <span className="ml-2 text-sm text-gray-500">搜索中...</span>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`search-input-wrapper ${className}`}>
      <Dropdown
        open={showDropdown}
        onOpenChange={setShowDropdown}
        dropdownRender={renderDropdownContent}
        trigger={['click']}
        placement="bottomLeft"
        overlayClassName="search-dropdown"
      >
        <div className="relative">
          <Search
            ref={inputRef}
            value={inputValue}
            onChange={(e) => handleInputChange(e.target.value)}
            onSearch={handleSearch}
            placeholder={placeholder}
            size={size}
            enterButton={
              <Button type="primary" icon={<SearchOutlined />}>
                搜索
              </Button>
            }
            suffix={
              <Space>
                {showAdvanced && (
                  <Button
                    type="text"
                    size="small"
                    icon={<FilterOutlined />}
                    onClick={() => {
                      // 这里可以打开高级搜索面板
                      console.log('打开高级搜索')
                    }}
                  />
                )}
              </Space>
            }
            onFocus={() => setShowDropdown(true)}
            className="search-input"
          />
        </div>
      </Dropdown>
    </div>
  )
}

export default SearchInput
