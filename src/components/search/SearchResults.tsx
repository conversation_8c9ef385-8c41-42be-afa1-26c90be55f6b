/**
 * 搜索结果展示组件
 * 提供搜索结果的展示、高亮、分页等功能
 */

import React, { useState, useCallback, useMemo } from 'react'
import {
  List,
  Card,
  Tag,
  Button,
  Pagination,
  Empty,
  Spin,
  Typography,
  Space,
  Divider,
  Tooltip,
  Badge
} from 'antd'
import {
  FileTextOutlined,
  BgColorsOutlined,
  NodeIndexOutlined,
  ProjectOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  StarOutlined,
  TagOutlined
} from '@ant-design/icons'
import { BaseDocument, DocumentType, SearchResult } from '@/types'
import { formatRelativeTime, highlightText } from '@utils/index'

const { Text, Paragraph } = Typography

/**
 * 搜索结果项接口
 */
export interface SearchResultItem extends BaseDocument {
  /** 搜索匹配的片段 */
  highlights?: string[]
  /** 匹配分数 */
  score?: number
  /** 匹配的字段 */
  matchedFields?: string[]
}

/**
 * 搜索结果组件属性接口
 */
export interface SearchResultsProps {
  /** 搜索结果 */
  results: SearchResultItem[]
  /** 搜索关键词 */
  query: string
  /** 总数量 */
  total: number
  /** 当前页码 */
  current: number
  /** 每页数量 */
  pageSize: number
  /** 加载状态 */
  loading?: boolean
  /** 页码变化回调 */
  onPageChange?: (page: number, size: number) => void
  /** 文档选择回调 */
  onDocumentSelect?: (document: BaseDocument) => void
  /** 样式类名 */
  className?: string
}

/**
 * 搜索结果展示组件
 */
const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  query,
  total,
  current,
  pageSize,
  loading = false,
  onPageChange,
  onDocumentSelect,
  className = ''
}) => {
  const [viewMode, setViewMode] = useState<'list' | 'card'>('list')

  /**
   * 获取文档类型图标
   */
  const getDocumentIcon = useCallback((type: DocumentType) => {
    const icons = {
      [DocumentType.TEXT]: <FileTextOutlined className="text-blue-500" />,
      [DocumentType.WHITEBOARD]: <BgColorsOutlined className="text-green-500" />,
      [DocumentType.MINDMAP]: <NodeIndexOutlined className="text-purple-500" />,
      [DocumentType.KANBAN]: <ProjectOutlined className="text-orange-500" />
    }
    return icons[type]
  }, [])

  /**
   * 获取文档类型名称
   */
  const getDocumentTypeName = useCallback((type: DocumentType) => {
    const names = {
      [DocumentType.TEXT]: '文本笔记',
      [DocumentType.WHITEBOARD]: '白板',
      [DocumentType.MINDMAP]: '思维导图',
      [DocumentType.KANBAN]: '看板'
    }
    return names[type]
  }, [])

  /**
   * 处理文档点击
   */
  const handleDocumentClick = useCallback((document: BaseDocument) => {
    onDocumentSelect?.(document)
  }, [onDocumentSelect])

  /**
   * 渲染搜索结果项
   */
  const renderResultItem = useCallback((item: SearchResultItem) => {
    const { highlights = [], matchedFields = [] } = item

    return (
      <List.Item
        key={item.id}
        className="search-result-item hover:bg-gray-50 cursor-pointer transition-colors"
        onClick={() => handleDocumentClick(item)}
      >
        <div className="w-full">
          <div className="flex items-start space-x-3">
            {/* 文档图标 */}
            <div className="flex-shrink-0 mt-1">
              {getDocumentIcon(item.type)}
            </div>

            {/* 文档信息 */}
            <div className="flex-1 min-w-0">
              {/* 标题行 */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Text
                    strong
                    className="text-base"
                    style={{ color: '#1890ff' }}
                  >
                    {highlightText(item.title, query)}
                  </Text>
                  <Tag size="small" color="blue">
                    {getDocumentTypeName(item.type)}
                  </Tag>
                  {item.score && (
                    <Badge
                      count={`${Math.round(item.score * 100)}%`}
                      style={{ backgroundColor: '#52c41a' }}
                    />
                  )}
                </div>
                <Text type="secondary" className="text-sm">
                  {formatRelativeTime(item.updatedAt)}
                </Text>
              </div>

              {/* 高亮片段 */}
              {highlights.length > 0 && (
                <div className="mb-2">
                  {highlights.slice(0, 2).map((highlight, index) => (
                    <Paragraph
                      key={index}
                      className="text-sm text-gray-600 mb-1"
                      ellipsis={{ rows: 2 }}
                    >
                      <span
                        dangerouslySetInnerHTML={{
                          __html: highlightText(highlight, query)
                        }}
                      />
                    </Paragraph>
                  ))}
                  {highlights.length > 2 && (
                    <Text type="secondary" className="text-xs">
                      还有 {highlights.length - 2} 个匹配片段...
                    </Text>
                  )}
                </div>
              )}

              {/* 标签 */}
              {item.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-2">
                  <TagOutlined className="text-gray-400 text-xs mt-1" />
                  {item.tags.slice(0, 5).map(tag => (
                    <Tag
                      key={tag}
                      size="small"
                      className="text-xs"
                      style={{
                        backgroundColor: query.toLowerCase().includes(tag.toLowerCase())
                          ? '#fff2e8'
                          : undefined,
                        borderColor: query.toLowerCase().includes(tag.toLowerCase())
                          ? '#ffbb96'
                          : undefined
                      }}
                    >
                      {highlightText(tag, query)}
                    </Tag>
                  ))}
                  {item.tags.length > 5 && (
                    <Tag size="small" className="text-xs">
                      +{item.tags.length - 5}
                    </Tag>
                  )}
                </div>
              )}

              {/* 匹配字段信息 */}
              {matchedFields.length > 0 && (
                <div className="flex items-center space-x-2 text-xs text-gray-500">
                  <span>匹配字段:</span>
                  {matchedFields.map(field => (
                    <Tag key={field} size="small" color="processing">
                      {field === 'title' ? '标题' :
                       field === 'content' ? '内容' :
                       field === 'tags' ? '标签' : field}
                    </Tag>
                  ))}
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            <div className="flex-shrink-0">
              <Space>
                <Tooltip title="查看详情">
                  <Button
                    type="text"
                    size="small"
                    icon={<EyeOutlined />}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDocumentClick(item)
                    }}
                  />
                </Tooltip>
                <Tooltip title="收藏">
                  <Button
                    type="text"
                    size="small"
                    icon={<StarOutlined />}
                    onClick={(e) => {
                      e.stopPropagation()
                      // 处理收藏逻辑
                    }}
                  />
                </Tooltip>
              </Space>
            </div>
          </div>
        </div>
      </List.Item>
    )
  }, [query, getDocumentIcon, getDocumentTypeName, handleDocumentClick])

  /**
   * 渲染统计信息
   */
  const renderStats = useMemo(() => {
    if (loading || total === 0) return null

    const startIndex = (current - 1) * pageSize + 1
    const endIndex = Math.min(current * pageSize, total)

    return (
      <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded">
        <div className="flex items-center space-x-4">
          <Text>
            找到 <Text strong>{total.toLocaleString()}</Text> 个结果
          </Text>
          <Text type="secondary">
            显示第 {startIndex}-{endIndex} 个结果
          </Text>
        </div>
        <div className="flex items-center space-x-2">
          <Text type="secondary" className="text-sm">
            搜索关键词:
          </Text>
          <Tag color="blue">{query}</Tag>
        </div>
      </div>
    )
  }, [loading, total, current, pageSize, query])

  /**
   * 渲染空状态
   */
  if (!loading && results.length === 0) {
    return (
      <div className={`search-results ${className}`}>
        <Empty
          description={
            query ? `没有找到包含"${query}"的文档` : '请输入搜索关键词'
          }
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    )
  }

  return (
    <div className={`search-results ${className}`}>
      {/* 统计信息 */}
      {renderStats}

      {/* 搜索结果列表 */}
      <Spin spinning={loading}>
        <List
          dataSource={results}
          renderItem={renderResultItem}
          className="search-results-list"
          split={true}
        />
      </Spin>

      {/* 分页 */}
      {total > pageSize && (
        <>
          <Divider />
          <div className="flex justify-center">
            <Pagination
              current={current}
              total={total}
              pageSize={pageSize}
              onChange={onPageChange}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
              pageSizeOptions={['10', '20', '50', '100']}
            />
          </div>
        </>
      )}
    </div>
  )
}

export default SearchResults
