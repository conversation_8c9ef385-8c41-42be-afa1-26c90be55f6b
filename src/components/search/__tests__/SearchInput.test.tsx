/**
 * SearchInput 组件单元测试
 * 测试搜索输入组件的功能
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import SearchInput from '../SearchInput'

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

describe('SearchInput', () => {
  const mockOnSearch = vi.fn()
  const mockOnChange = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(['历史搜索1', '历史搜索2']))
  })

  it('应该正确渲染搜索输入框', () => {
    render(
      <SearchInput
        value=""
        onSearch={mockOnSearch}
        placeholder="搜索文档..."
      />
    )

    const input = screen.getByPlaceholderText('搜索文档...')
    expect(input).toBeInTheDocument()
  })

  it('应该在输入时调用onChange回调', async () => {
    const user = userEvent.setup()
    
    render(
      <SearchInput
        value=""
        onSearch={mockOnSearch}
        onChange={mockOnChange}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, '测试搜索')

    expect(mockOnChange).toHaveBeenCalledWith('测试搜索')
  })

  it('应该在按下回车时触发搜索', async () => {
    const user = userEvent.setup()
    
    render(
      <SearchInput
        value="测试查询"
        onSearch={mockOnSearch}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, '{enter}')

    expect(mockOnSearch).toHaveBeenCalledWith('测试查询')
  })

  it('应该在点击搜索按钮时触发搜索', async () => {
    const user = userEvent.setup()
    
    render(
      <SearchInput
        value="测试查询"
        onSearch={mockOnSearch}
      />
    )

    const searchButton = screen.getByRole('button')
    await user.click(searchButton)

    expect(mockOnSearch).toHaveBeenCalledWith('测试查询')
  })

  it('应该显示搜索历史', async () => {
    const user = userEvent.setup()
    
    render(
      <SearchInput
        value=""
        onSearch={mockOnSearch}
        showHistory={true}
      />
    )

    const input = screen.getByRole('textbox')
    await user.click(input)

    await waitFor(() => {
      expect(screen.getByText('历史搜索1')).toBeInTheDocument()
      expect(screen.getByText('历史搜索2')).toBeInTheDocument()
    })
  })

  it('应该在选择历史记录时触发搜索', async () => {
    const user = userEvent.setup()
    
    render(
      <SearchInput
        value=""
        onSearch={mockOnSearch}
        showHistory={true}
      />
    )

    const input = screen.getByRole('textbox')
    await user.click(input)

    await waitFor(() => {
      const historyItem = screen.getByText('历史搜索1')
      expect(historyItem).toBeInTheDocument()
    })

    const historyItem = screen.getByText('历史搜索1')
    await user.click(historyItem)

    expect(mockOnSearch).toHaveBeenCalledWith('历史搜索1')
  })

  it('应该显示搜索建议', async () => {
    const user = userEvent.setup()
    
    const mockSuggestions = ['建议1', '建议2', '建议3']
    
    render(
      <SearchInput
        value="建议"
        onSearch={mockOnSearch}
        showSuggestions={true}
        suggestions={mockSuggestions}
      />
    )

    const input = screen.getByRole('textbox')
    await user.click(input)

    await waitFor(() => {
      expect(screen.getByText('建议1')).toBeInTheDocument()
      expect(screen.getByText('建议2')).toBeInTheDocument()
      expect(screen.getByText('建议3')).toBeInTheDocument()
    })
  })

  it('应该支持清空输入', async () => {
    const user = userEvent.setup()
    
    render(
      <SearchInput
        value="测试内容"
        onSearch={mockOnSearch}
        onChange={mockOnChange}
        allowClear={true}
      />
    )

    const clearButton = screen.getByRole('button', { name: /clear/i })
    await user.click(clearButton)

    expect(mockOnChange).toHaveBeenCalledWith('')
  })

  it('应该显示高级搜索按钮', () => {
    render(
      <SearchInput
        value=""
        onSearch={mockOnSearch}
        showAdvanced={true}
      />
    )

    const advancedButton = screen.getByText('高级搜索')
    expect(advancedButton).toBeInTheDocument()
  })

  it('应该保存搜索历史到localStorage', async () => {
    const user = userEvent.setup()
    
    render(
      <SearchInput
        value="新搜索"
        onSearch={mockOnSearch}
        showHistory={true}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, '{enter}')

    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'search-history',
      expect.stringContaining('新搜索')
    )
  })

  it('应该限制搜索历史的数量', async () => {
    // 模拟已有很多历史记录
    const longHistory = Array.from({ length: 15 }, (_, i) => `历史${i}`)
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(longHistory))

    const user = userEvent.setup()
    
    render(
      <SearchInput
        value="新搜索"
        onSearch={mockOnSearch}
        showHistory={true}
      />
    )

    const input = screen.getByRole('textbox')
    await user.type(input, '{enter}')

    // 验证保存的历史记录不超过10条
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'search-history',
      expect.stringMatching(/^\[.*\]$/)
    )

    const savedHistory = JSON.parse(mockLocalStorage.setItem.mock.calls[0][1])
    expect(savedHistory.length).toBeLessThanOrEqual(10)
  })

  it('应该防抖处理输入变化', async () => {
    const user = userEvent.setup()
    
    render(
      <SearchInput
        value=""
        onSearch={mockOnSearch}
        onChange={mockOnChange}
        debounceMs={300}
      />
    )

    const input = screen.getByRole('textbox')
    
    // 快速输入多个字符
    await user.type(input, 'abc')

    // 在防抖时间内，onChange应该只被调用一次（最后一次）
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenLastCalledWith('abc')
    }, { timeout: 500 })
  })

  it('应该支持不同的尺寸', () => {
    const { rerender } = render(
      <SearchInput
        value=""
        onSearch={mockOnSearch}
        size="small"
      />
    )

    let input = screen.getByRole('textbox')
    expect(input).toHaveClass('ant-input-sm')

    rerender(
      <SearchInput
        value=""
        onSearch={mockOnSearch}
        size="large"
      />
    )

    input = screen.getByRole('textbox')
    expect(input).toHaveClass('ant-input-lg')
  })

  it('应该处理空的搜索历史', () => {
    mockLocalStorage.getItem.mockReturnValue(null)

    render(
      <SearchInput
        value=""
        onSearch={mockOnSearch}
        showHistory={true}
      />
    )

    // 不应该抛出错误
    const input = screen.getByRole('textbox')
    expect(input).toBeInTheDocument()
  })

  it('应该处理无效的搜索历史JSON', () => {
    mockLocalStorage.getItem.mockReturnValue('invalid json')

    render(
      <SearchInput
        value=""
        onSearch={mockOnSearch}
        showHistory={true}
      />
    )

    // 不应该抛出错误
    const input = screen.getByRole('textbox')
    expect(input).toBeInTheDocument()
  })
})
