/**
 * 高级搜索组件
 * 提供详细的搜索条件设置，支持按类型、日期、标签等条件筛选
 */

import React, { useState, useCallback, useEffect } from 'react'
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Checkbox,
  Button,
  Space,
  Divider,
  Tag,
  Row,
  Col,
  Slider,
  InputNumber,
  Radio
} from 'antd'
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  SaveOutlined,
  HistoryOutlined
} from '@ant-design/icons'
import { DocumentType, SearchOptions } from '@/types'
import { documentManager } from '@services/document/documentManager'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker
const { TextArea } = Input
const { Option } = Select

/**
 * 高级搜索条件接口
 */
export interface AdvancedSearchFilters extends SearchOptions {
  /** 关键词 */
  keywords?: string
  /** 排除关键词 */
  excludeKeywords?: string
  /** 精确匹配 */
  exactMatch?: boolean
  /** 搜索范围 */
  searchIn?: ('title' | 'content' | 'tags')[]
  /** 文档大小范围 */
  sizeRange?: [number, number]
  /** 字数范围 */
  wordCountRange?: [number, number]
  /** 阅读时间范围 */
  readingTimeRange?: [number, number]
  /** 是否收藏 */
  isFavorite?: boolean
  /** 创建者 */
  author?: string
}

/**
 * 高级搜索组件属性接口
 */
export interface AdvancedSearchProps {
  /** 是否显示 */
  visible: boolean
  /** 初始搜索条件 */
  initialFilters?: AdvancedSearchFilters
  /** 关闭回调 */
  onClose: () => void
  /** 搜索回调 */
  onSearch: (filters: AdvancedSearchFilters) => void
  /** 重置回调 */
  onReset?: () => void
}

/**
 * 高级搜索组件
 */
const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  visible,
  initialFilters = {},
  onClose,
  onSearch,
  onReset
}) => {
  const [form] = Form.useForm()
  const [availableTags, setAvailableTags] = useState<string[]>([])
  const [savedFilters, setSavedFilters] = useState<AdvancedSearchFilters[]>([])
  const [loading, setLoading] = useState(false)

  /**
   * 加载可用标签
   */
  useEffect(() => {
    const loadTags = async () => {
      try {
        const tags = await documentManager.getAllTags()
        setAvailableTags(tags)
      } catch (error) {
        console.error('加载标签失败:', error)
      }
    }

    if (visible) {
      loadTags()
    }
  }, [visible])

  /**
   * 加载保存的搜索条件
   */
  useEffect(() => {
    try {
      const saved = localStorage.getItem('advanced-search-filters')
      if (saved) {
        setSavedFilters(JSON.parse(saved))
      }
    } catch (error) {
      console.warn('加载保存的搜索条件失败:', error)
    }
  }, [])

  /**
   * 初始化表单值
   */
  useEffect(() => {
    if (visible && initialFilters) {
      form.setFieldsValue({
        keywords: initialFilters.keywords || '',
        excludeKeywords: initialFilters.excludeKeywords || '',
        exactMatch: initialFilters.exactMatch || false,
        searchIn: initialFilters.searchIn || ['title', 'content', 'tags'],
        types: initialFilters.types || [],
        tags: initialFilters.tags || [],
        dateRange: initialFilters.createdAfter && initialFilters.createdBefore
          ? [dayjs(initialFilters.createdAfter), dayjs(initialFilters.createdBefore)]
          : undefined,
        sizeRange: initialFilters.sizeRange || [0, 100],
        wordCountRange: initialFilters.wordCountRange || [0, 10000],
        readingTimeRange: initialFilters.readingTimeRange || [0, 60],
        isFavorite: initialFilters.isFavorite,
        author: initialFilters.author || '',
        sortBy: initialFilters.sortBy || 'updatedAt',
        sortOrder: initialFilters.sortOrder || 'desc'
      })
    }
  }, [visible, initialFilters, form])

  /**
   * 处理搜索
   */
  const handleSearch = useCallback(async () => {
    try {
      setLoading(true)
      const values = await form.validateFields()
      
      const filters: AdvancedSearchFilters = {
        keywords: values.keywords?.trim(),
        excludeKeywords: values.excludeKeywords?.trim(),
        exactMatch: values.exactMatch,
        searchIn: values.searchIn,
        types: values.types?.length > 0 ? values.types : undefined,
        tags: values.tags?.length > 0 ? values.tags : undefined,
        createdAfter: values.dateRange?.[0]?.toDate(),
        createdBefore: values.dateRange?.[1]?.toDate(),
        sizeRange: values.sizeRange,
        wordCountRange: values.wordCountRange,
        readingTimeRange: values.readingTimeRange,
        isFavorite: values.isFavorite,
        author: values.author?.trim(),
        sortBy: values.sortBy,
        sortOrder: values.sortOrder
      }

      // 移除空值
      Object.keys(filters).forEach(key => {
        const value = filters[key as keyof AdvancedSearchFilters]
        if (value === undefined || value === '' || (Array.isArray(value) && value.length === 0)) {
          delete filters[key as keyof AdvancedSearchFilters]
        }
      })

      onSearch(filters)
      onClose()
    } catch (error) {
      console.error('搜索失败:', error)
    } finally {
      setLoading(false)
    }
  }, [form, onSearch, onClose])

  /**
   * 处理重置
   */
  const handleReset = useCallback(() => {
    form.resetFields()
    onReset?.()
  }, [form, onReset])

  /**
   * 保存搜索条件
   */
  const handleSaveFilters = useCallback(async () => {
    try {
      const values = await form.validateFields()
      const filterName = `搜索条件 ${new Date().toLocaleString()}`
      
      const newFilter = {
        name: filterName,
        ...values,
        createdAt: new Date().toISOString()
      }

      const updatedFilters = [newFilter, ...savedFilters.slice(0, 9)] // 最多保存10个
      setSavedFilters(updatedFilters)
      localStorage.setItem('advanced-search-filters', JSON.stringify(updatedFilters))
    } catch (error) {
      console.error('保存搜索条件失败:', error)
    }
  }, [form, savedFilters])

  /**
   * 应用保存的搜索条件
   */
  const handleApplySavedFilter = useCallback((filter: AdvancedSearchFilters) => {
    form.setFieldsValue(filter)
  }, [form])

  return (
    <Modal
      title={
        <Space>
          <FilterOutlined />
          高级搜索
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={
        <Space>
          <Button onClick={handleReset} icon={<ClearOutlined />}>
            重置
          </Button>
          <Button onClick={handleSaveFilters} icon={<SaveOutlined />}>
            保存条件
          </Button>
          <Button
            type="primary"
            onClick={handleSearch}
            loading={loading}
            icon={<SearchOutlined />}
          >
            搜索
          </Button>
        </Space>
      }
      className="advanced-search-modal"
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          exactMatch: false,
          searchIn: ['title', 'content', 'tags'],
          sizeRange: [0, 100],
          wordCountRange: [0, 10000],
          readingTimeRange: [0, 60],
          sortBy: 'updatedAt',
          sortOrder: 'desc'
        }}
      >
        {/* 保存的搜索条件 */}
        {savedFilters.length > 0 && (
          <>
            <div className="mb-4">
              <div className="text-sm font-medium mb-2 flex items-center">
                <HistoryOutlined className="mr-1" />
                保存的搜索条件
              </div>
              <div className="flex flex-wrap gap-2">
                {savedFilters.slice(0, 5).map((filter, index) => (
                  <Tag
                    key={index}
                    className="cursor-pointer"
                    onClick={() => handleApplySavedFilter(filter)}
                  >
                    {filter.name || `条件 ${index + 1}`}
                  </Tag>
                ))}
              </div>
            </div>
            <Divider />
          </>
        )}

        <Row gutter={16}>
          {/* 关键词搜索 */}
          <Col span={12}>
            <Form.Item name="keywords" label="包含关键词">
              <Input placeholder="输入要搜索的关键词" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="excludeKeywords" label="排除关键词">
              <Input placeholder="输入要排除的关键词" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          {/* 搜索选项 */}
          <Col span={12}>
            <Form.Item name="exactMatch" valuePropName="checked">
              <Checkbox>精确匹配</Checkbox>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="searchIn" label="搜索范围">
              <Checkbox.Group>
                <Checkbox value="title">标题</Checkbox>
                <Checkbox value="content">内容</Checkbox>
                <Checkbox value="tags">标签</Checkbox>
              </Checkbox.Group>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          {/* 文档类型 */}
          <Col span={12}>
            <Form.Item name="types" label="文档类型">
              <Select mode="multiple" placeholder="选择文档类型" allowClear>
                <Option value={DocumentType.TEXT}>文本笔记</Option>
                <Option value={DocumentType.WHITEBOARD}>白板</Option>
                <Option value={DocumentType.MINDMAP}>思维导图</Option>
                <Option value={DocumentType.KANBAN}>看板</Option>
              </Select>
            </Form.Item>
          </Col>
          {/* 标签 */}
          <Col span={12}>
            <Form.Item name="tags" label="标签">
              <Select
                mode="multiple"
                placeholder="选择标签"
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {availableTags.map(tag => (
                  <Option key={tag} value={tag}>
                    {tag}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* 日期范围 */}
        <Form.Item name="dateRange" label="创建日期">
          <RangePicker
            style={{ width: '100%' }}
            placeholder={['开始日期', '结束日期']}
          />
        </Form.Item>

        {/* 文档大小范围 */}
        <Form.Item name="sizeRange" label="文档大小 (KB)">
          <Slider
            range
            min={0}
            max={1000}
            marks={{
              0: '0KB',
              100: '100KB',
              500: '500KB',
              1000: '1MB'
            }}
          />
        </Form.Item>

        {/* 字数范围 */}
        <Form.Item name="wordCountRange" label="字数范围">
          <Slider
            range
            min={0}
            max={50000}
            step={100}
            marks={{
              0: '0',
              10000: '1万',
              25000: '2.5万',
              50000: '5万'
            }}
          />
        </Form.Item>

        {/* 阅读时间范围 */}
        <Form.Item name="readingTimeRange" label="阅读时间 (分钟)">
          <Slider
            range
            min={0}
            max={120}
            marks={{
              0: '0分钟',
              30: '30分钟',
              60: '1小时',
              120: '2小时'
            }}
          />
        </Form.Item>

        <Row gutter={16}>
          {/* 排序方式 */}
          <Col span={12}>
            <Form.Item name="sortBy" label="排序字段">
              <Select>
                <Option value="updatedAt">修改时间</Option>
                <Option value="createdAt">创建时间</Option>
                <Option value="title">标题</Option>
                <Option value="size">文件大小</Option>
                <Option value="wordCount">字数</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="sortOrder" label="排序顺序">
              <Radio.Group>
                <Radio value="desc">降序</Radio>
                <Radio value="asc">升序</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>

        {/* 其他选项 */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="author" label="创建者">
              <Input placeholder="输入创建者名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="isFavorite" label="收藏状态">
              <Select placeholder="选择收藏状态" allowClear>
                <Option value={true}>已收藏</Option>
                <Option value={false}>未收藏</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  )
}

export default AdvancedSearch
