/**
 * 白板画布组件单元测试
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import WhiteboardCanvas, { DrawingTool } from '@components/whiteboard/WhiteboardCanvas'
import { BaseDocument } from '@/types'

// Mock Fabric.js
const mockCanvas = {
  dispose: vi.fn(),
  clear: vi.fn(),
  add: vi.fn(),
  remove: vi.fn(),
  setActiveObject: vi.fn(),
  getActiveObject: vi.fn(),
  getActiveObjects: vi.fn(() => []),
  getObjects: vi.fn(() => []),
  renderAll: vi.fn(),
  requestRenderAll: vi.fn(),
  loadFromJSON: vi.fn((data, callback) => callback && callback()),
  toJSON: vi.fn(() => ({})),
  toDataURL: vi.fn(() => 'data:image/png;base64,mock'),
  on: vi.fn(),
  off: vi.fn(),
  getPointer: vi.fn(() => ({ x: 100, y: 100 })),
  bringToFront: vi.fn(),
  sendToBack: vi.fn(),
  bringForward: vi.fn(),
  sendBackward: vi.fn(),
  discardActiveObject: vi.fn(),
  getZoom: vi.fn(() => 1),
  zoomToPoint: vi.fn(),
  setViewportTransform: vi.fn(),
  setZoom: vi.fn(),
  absolutePan: vi.fn(),
  freeDrawingBrush: {
    width: 2,
    color: '#000000'
  },
  isDrawingMode: false,
  selection: true,
  defaultCursor: 'default',
  width: 800,
  height: 600,
  backgroundColor: '#ffffff'
}



// Mock fabric module
vi.mock('fabric', () => {
  const mockFabric = {
    Canvas: vi.fn(() => mockCanvas),
    PencilBrush: vi.fn(),
    EraserBrush: vi.fn(),
    Rect: vi.fn(),
    Circle: vi.fn(),
    Line: vi.fn(),
    IText: vi.fn(),
    Point: vi.fn(),
    ActiveSelection: vi.fn()
  }

  return {
    fabric: mockFabric
  }
})

describe('WhiteboardCanvas', () => {
  const mockOnChange = vi.fn()
  const mockDocument: BaseDocument = {
    id: 'test-doc',
    title: '测试白板',
    content: '{"objects":[]}',
    type: 'whiteboard',
    createdAt: new Date(),
    updatedAt: new Date(),
    tags: [],
    category: 'default'
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染白板组件', () => {
    render(<WhiteboardCanvas />)

    // 检查工具栏是否存在
    expect(screen.getByRole('button', { name: /选择工具/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /画笔/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /矩形/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /圆形/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /直线/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /文本/i })).toBeInTheDocument()
  })

  it('应该正确初始化Fabric.js画布', () => {
    render(<WhiteboardCanvas width={800} height={600} />)

    expect(mockFabric.Canvas).toHaveBeenCalledWith(
      expect.any(HTMLCanvasElement),
      expect.objectContaining({
        width: 800,
        height: 600,
        backgroundColor: '#ffffff',
        selection: true,
        isDrawingMode: false
      })
    )
  })

  it('应该在只读模式下禁用编辑功能', () => {
    render(<WhiteboardCanvas readonly={true} />)

    // 只读模式下不应该显示工具栏
    expect(screen.queryByRole('button', { name: /画笔/i })).not.toBeInTheDocument()
  })

  it('应该正确处理工具切换', async () => {
    render(<WhiteboardCanvas />)

    const penButton = screen.getByRole('button', { name: /画笔/i })
    const rectButton = screen.getByRole('button', { name: /矩形/i })

    // 点击画笔工具
    fireEvent.click(penButton)
    await waitFor(() => {
      expect(mockCanvas.isDrawingMode).toBe(true)
    })

    // 点击矩形工具
    fireEvent.click(rectButton)
    await waitFor(() => {
      expect(mockCanvas.isDrawingMode).toBe(false)
      expect(mockCanvas.selection).toBe(false)
      expect(mockCanvas.defaultCursor).toBe('crosshair')
    })
  })

  it('应该正确处理撤销和重做操作', async () => {
    render(<WhiteboardCanvas />)

    const undoButton = screen.getByRole('button', { name: /撤销/i })
    const redoButton = screen.getByRole('button', { name: /重做/i })

    // 初始状态下撤销和重做按钮应该被禁用
    expect(undoButton).toBeDisabled()
    expect(redoButton).toBeDisabled()
  })

  it('应该正确处理清空画布操作', async () => {
    render(<WhiteboardCanvas />)

    const clearButton = screen.getByRole('button', { name: /清空画布/i })

    fireEvent.click(clearButton)

    await waitFor(() => {
      expect(mockCanvas.clear).toHaveBeenCalled()
      expect(mockCanvas.renderAll).toHaveBeenCalled()
    })
  })

  it('应该正确处理导出功能', async () => {
    // Mock createElement and click
    const mockLink = {
      click: vi.fn(),
      download: '',
      href: ''
    }
    const createElementSpy = vi.spyOn(document, 'createElement').mockReturnValue(mockLink as any)

    render(<WhiteboardCanvas />)

    const exportButton = screen.getByRole('button', { name: /导出图片/i })

    fireEvent.click(exportButton)

    await waitFor(() => {
      expect(mockCanvas.toDataURL).toHaveBeenCalledWith({
        format: 'png',
        quality: 1
      })
      expect(createElementSpy).toHaveBeenCalledWith('a')
      expect(mockLink.click).toHaveBeenCalled()
    })

    createElementSpy.mockRestore()
  })

  it('应该正确处理缩放操作', async () => {
    render(<WhiteboardCanvas />)

    const zoomInButton = screen.getByRole('button', { name: /放大/i })
    const zoomOutButton = screen.getByRole('button', { name: /缩小/i })

    fireEvent.click(zoomInButton)
    await waitFor(() => {
      expect(mockCanvas.zoomToPoint).toHaveBeenCalled()
    })

    fireEvent.click(zoomOutButton)
    await waitFor(() => {
      expect(mockCanvas.zoomToPoint).toHaveBeenCalled()
    })
  })

  it('应该正确处理图层管理操作', async () => {
    // 模拟有选中对象
    mockCanvas.getActiveObject.mockReturnValue({ id: 'test-object' })

    render(<WhiteboardCanvas />)

    const bringToFrontButton = screen.getByRole('button', { name: /置于顶层/i })
    const sendToBackButton = screen.getByRole('button', { name: /置于底层/i })

    fireEvent.click(bringToFrontButton)
    await waitFor(() => {
      expect(mockCanvas.bringToFront).toHaveBeenCalled()
      expect(mockCanvas.renderAll).toHaveBeenCalled()
    })

    fireEvent.click(sendToBackButton)
    await waitFor(() => {
      expect(mockCanvas.sendToBack).toHaveBeenCalled()
      expect(mockCanvas.renderAll).toHaveBeenCalled()
    })
  })

  it('应该正确加载文档内容', () => {
    render(<WhiteboardCanvas document={mockDocument} />)

    expect(mockCanvas.loadFromJSON).toHaveBeenCalledWith(
      mockDocument.content,
      expect.any(Function)
    )
  })

  it('应该正确处理内容变化回调', async () => {
    render(<WhiteboardCanvas onChange={mockOnChange} />)

    // 模拟画布变化事件
    const changeHandler = mockCanvas.on.mock.calls.find(call => call[0] === 'object:added')?.[1]

    if (changeHandler) {
      changeHandler()

      // 由于使用了防抖，需要等待
      await waitFor(() => {
        expect(mockCanvas.toJSON).toHaveBeenCalled()
      }, { timeout: 1000 })
    }
  })

  it('应该正确清理资源', () => {
    const { unmount } = render(<WhiteboardCanvas />)

    unmount()

    expect(mockCanvas.off).toHaveBeenCalledWith('path:created', expect.any(Function))
    expect(mockCanvas.off).toHaveBeenCalledWith('object:added', expect.any(Function))
    expect(mockCanvas.off).toHaveBeenCalledWith('object:removed', expect.any(Function))
    expect(mockCanvas.off).toHaveBeenCalledWith('object:modified', expect.any(Function))
    expect(mockCanvas.clear).toHaveBeenCalled()
    expect(mockCanvas.dispose).toHaveBeenCalled()
  })
  it('应该正确处理键盘快捷键', async () => {
    render(<WhiteboardCanvas />)

    // 模拟Ctrl+Z撤销
    fireEvent.keyDown(document, { key: 'z', ctrlKey: true })

    // 模拟Ctrl+A全选
    fireEvent.keyDown(document, { key: 'a', ctrlKey: true })

    await waitFor(() => {
      expect(mockCanvas.discardActiveObject).toHaveBeenCalled()
    })
  })
})