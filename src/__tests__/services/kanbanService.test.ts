/**
 * 看板服务测试
 */

import { kanbanService } from '@services/kanbanService'
import { documentDAO } from '@services/database/documentDAO'
import { DocumentType } from '@/types'
import { CardStatus, CardPriority } from '@components/kanban/KanbanBoard'

// Mock documentDAO
jest.mock('@services/database/documentDAO')
const mockDocumentDAO = documentDAO as jest.Mocked<typeof documentDAO>

describe('KanbanService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('createKanban', () => {
    it('应该创建新的看板文档', async () => {
      const title = '测试看板'
      
      mockDocumentDAO.create.mockResolvedValue(undefined)

      const result = await kanbanService.createKanban(title)

      expect(result).toBeDefined()
      expect(result.title).toBe(title)
      expect(result.type).toBe(DocumentType.KANBAN)
      expect(result.content).toBeDefined()
      expect(result.content.columns).toHaveLength(4) // 默认4列
      expect(result.content.cards).toEqual([])
      expect(result.metadata.cardCount).toBe(0)
      expect(result.metadata.columnCount).toBe(4)

      // 验证默认列
      const columns = result.content.columns
      expect(columns[0].title).toBe('待办')
      expect(columns[1].title).toBe('进行中')
      expect(columns[2].title).toBe('待审核')
      expect(columns[3].title).toBe('已完成')

      expect(mockDocumentDAO.create).toHaveBeenCalledTimes(1)
    })

    it('应该使用默认标题创建看板', async () => {
      mockDocumentDAO.create.mockResolvedValue(undefined)

      const result = await kanbanService.createKanban()

      expect(result.title).toBe('新建看板')
    })
  })

  describe('saveKanban', () => {
    it('应该保存看板文档并更新元数据', async () => {
      const kanban = await kanbanService.createKanban('测试看板')
      
      // 添加一些卡片
      kanban.content.cards = [
        {
          id: 'card1',
          title: '任务1',
          description: '描述1',
          status: CardStatus.TODO,
          priority: CardPriority.HIGH,
          tags: ['标签1'],
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'card2',
          title: '任务2',
          description: '描述2',
          status: CardStatus.IN_PROGRESS,
          priority: CardPriority.MEDIUM,
          tags: ['标签2'],
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]

      mockDocumentDAO.update.mockResolvedValue(undefined)

      await kanbanService.saveKanban(kanban)

      expect(mockDocumentDAO.update).toHaveBeenCalledTimes(1)
      expect(mockDocumentDAO.update).toHaveBeenCalledWith(
        expect.objectContaining({
          type: DocumentType.KANBAN,
          metadata: expect.objectContaining({
            cardCount: 2,
            columnCount: 4
          })
        })
      )
    })
  })

  describe('loadKanban', () => {
    it('应该加载存在的看板文档', async () => {
      const mockDoc = {
        id: 'test-id',
        type: DocumentType.KANBAN,
        title: '测试看板',
        content: {
          columns: [
            {
              id: 'col1',
              title: '待办',
              status: CardStatus.TODO,
              cards: [],
              color: '#f0f0f0',
              order: 0
            }
          ],
          cards: [
            {
              id: 'card1',
              title: '测试任务',
              description: '测试描述',
              status: CardStatus.TODO,
              priority: CardPriority.MEDIUM,
              tags: ['测试'],
              createdAt: new Date(),
              updatedAt: new Date()
            }
          ],
          settings: {
            allowAddColumns: true,
            allowDeleteColumns: false,
            cardLimit: 50,
            columnLimit: 10
          }
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: [],
        metadata: { cardCount: 1, columnCount: 1 }
      }

      mockDocumentDAO.getById.mockResolvedValue(mockDoc)

      const result = await kanbanService.loadKanban('test-id')

      expect(result).toBeDefined()
      expect(result!.id).toBe('test-id')
      expect(result!.type).toBe(DocumentType.KANBAN)
      expect(result!.title).toBe('测试看板')
      expect(result!.content.cards).toHaveLength(1)

      expect(mockDocumentDAO.getById).toHaveBeenCalledWith('test-id')
    })

    it('应该在文档不存在时返回null', async () => {
      mockDocumentDAO.getById.mockResolvedValue(null)

      const result = await kanbanService.loadKanban('non-existent')

      expect(result).toBeNull()
    })
  })

  describe('getKanbanStats', () => {
    it('应该返回看板统计信息', async () => {
      const kanban = await kanbanService.createKanban('测试看板')
      
      // 添加测试卡片
      const now = new Date()
      const pastDate = new Date(now.getTime() - 24 * 60 * 60 * 1000) // 昨天
      
      kanban.content.cards = [
        {
          id: 'card1',
          title: '任务1',
          description: '',
          status: CardStatus.TODO,
          priority: CardPriority.HIGH,
          tags: [],
          dueDate: pastDate, // 逾期任务
          createdAt: now,
          updatedAt: now
        },
        {
          id: 'card2',
          title: '任务2',
          description: '',
          status: CardStatus.IN_PROGRESS,
          priority: CardPriority.MEDIUM,
          tags: [],
          createdAt: now,
          updatedAt: now
        },
        {
          id: 'card3',
          title: '任务3',
          description: '',
          status: CardStatus.DONE,
          priority: CardPriority.LOW,
          tags: [],
          dueDate: pastDate, // 已完成，不算逾期
          createdAt: now,
          updatedAt: now
        }
      ]

      mockDocumentDAO.getById.mockResolvedValue({
        ...kanban,
        content: kanban.content
      })

      const stats = await kanbanService.getKanbanStats(kanban.id)

      expect(stats).toBeDefined()
      expect(stats!.totalCards).toBe(3)
      expect(stats!.cardsByStatus[CardStatus.TODO]).toBe(1)
      expect(stats!.cardsByStatus[CardStatus.IN_PROGRESS]).toBe(1)
      expect(stats!.cardsByStatus[CardStatus.DONE]).toBe(1)
      expect(stats!.cardsByPriority[CardPriority.HIGH]).toBe(1)
      expect(stats!.cardsByPriority[CardPriority.MEDIUM]).toBe(1)
      expect(stats!.cardsByPriority[CardPriority.LOW]).toBe(1)
      expect(stats!.overdueTasks).toBe(1) // 只有未完成的逾期任务
    })

    it('应该在看板不存在时返回null', async () => {
      mockDocumentDAO.getById.mockResolvedValue(null)

      const stats = await kanbanService.getKanbanStats('non-existent')

      expect(stats).toBeNull()
    })
  })

  describe('searchKanbans', () => {
    it('应该根据标题搜索看板', async () => {
      const mockKanbans = [
        {
          id: 'kb1',
          title: '项目管理看板',
          tags: ['项目'],
          type: DocumentType.KANBAN,
          content: {
            columns: [],
            cards: [],
            settings: {}
          },
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        },
        {
          id: 'kb2',
          title: '个人任务',
          tags: ['个人'],
          type: DocumentType.KANBAN,
          content: {
            columns: [],
            cards: [],
            settings: {}
          },
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        }
      ]

      mockDocumentDAO.getByType.mockResolvedValue(mockKanbans)

      const result = await kanbanService.searchKanbans('项目')

      expect(result).toHaveLength(1)
      expect(result[0].title).toBe('项目管理看板')
    })

    it('应该根据卡片内容搜索看板', async () => {
      const mockKanbans = [
        {
          id: 'kb1',
          title: '看板1',
          tags: [],
          type: DocumentType.KANBAN,
          content: {
            columns: [],
            cards: [
              {
                id: 'card1',
                title: '重要任务',
                description: '这是一个重要的任务',
                status: CardStatus.TODO,
                priority: CardPriority.HIGH,
                tags: ['重要'],
                createdAt: new Date(),
                updatedAt: new Date()
              }
            ],
            settings: {}
          },
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        }
      ]

      mockDocumentDAO.getByType.mockResolvedValue(mockKanbans)

      const result = await kanbanService.searchKanbans('重要任务')

      expect(result).toHaveLength(1)
      expect(result[0].id).toBe('kb1')
    })
  })

  describe('exportKanbanData', () => {
    it('应该导出看板数据为JSON', async () => {
      const kanban = await kanbanService.createKanban('测试看板')
      
      mockDocumentDAO.getById.mockResolvedValue({
        ...kanban,
        content: kanban.content
      })

      const result = await kanbanService.exportKanbanData(kanban.id)

      expect(result).toBeDefined()
      
      const exportData = JSON.parse(result!)
      expect(exportData.title).toBe('测试看板')
      expect(exportData.columns).toBeDefined()
      expect(exportData.cards).toBeDefined()
      expect(exportData.createdAt).toBeDefined()
      expect(exportData.updatedAt).toBeDefined()
    })

    it('应该在看板不存在时返回null', async () => {
      mockDocumentDAO.getById.mockResolvedValue(null)

      const result = await kanbanService.exportKanbanData('non-existent')

      expect(result).toBeNull()
    })
  })

  describe('importKanbanData', () => {
    it('应该导入看板数据', async () => {
      const importData = {
        title: '导入的看板',
        columns: [
          {
            id: 'col1',
            title: '待办',
            status: CardStatus.TODO,
            cards: [],
            color: '#f0f0f0',
            order: 0
          }
        ],
        cards: [
          {
            id: 'card1',
            title: '导入的任务',
            description: '导入的描述',
            status: CardStatus.TODO,
            priority: CardPriority.MEDIUM,
            tags: ['导入'],
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ]
      }

      mockDocumentDAO.create.mockResolvedValue(undefined)
      mockDocumentDAO.update.mockResolvedValue(undefined)

      const result = await kanbanService.importKanbanData(JSON.stringify(importData))

      expect(result).toBeDefined()
      expect(result!.title).toBe('导入的看板')
      expect(result!.content.cards).toHaveLength(1)
      expect(result!.content.cards[0].title).toBe('导入的任务')

      expect(mockDocumentDAO.create).toHaveBeenCalledTimes(1)
      expect(mockDocumentDAO.update).toHaveBeenCalledTimes(1)
    })

    it('应该在JSON格式错误时返回null', async () => {
      const result = await kanbanService.importKanbanData('invalid json')

      expect(result).toBeNull()
    })
  })
})
