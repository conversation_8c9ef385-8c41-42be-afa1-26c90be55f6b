/**
 * 思维导图服务测试
 */

import { mindmapService } from '@services/mindmapService'
import { documentDAO } from '@services/database/documentDAO'
import { DocumentType } from '@/types'

// Mock documentDAO
jest.mock('@services/database/documentDAO')
const mockDocumentDAO = documentDAO as jest.Mocked<typeof documentDAO>

describe('MindMapService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('createMindMap', () => {
    it('应该创建新的思维导图文档', async () => {
      const title = '测试思维导图'
      
      mockDocumentDAO.create.mockResolvedValue(undefined)

      const result = await mindmapService.createMindMap(title)

      expect(result).toBeDefined()
      expect(result.title).toBe(title)
      expect(result.type).toBe(DocumentType.MINDMAP)
      expect(result.content).toBeDefined()
      expect(result.content.nodes).toHaveLength(1) // 应该有一个根节点
      expect(result.content.connections).toEqual([])
      expect(result.metadata.nodeCount).toBe(1)
      expect(result.metadata.maxLevel).toBe(0)

      expect(mockDocumentDAO.create).toHaveBeenCalledTimes(1)
      expect(mockDocumentDAO.create).toHaveBeenCalledWith(
        expect.objectContaining({
          type: DocumentType.MINDMAP,
          title
        })
      )
    })

    it('应该使用默认标题创建思维导图', async () => {
      mockDocumentDAO.create.mockResolvedValue(undefined)

      const result = await mindmapService.createMindMap()

      expect(result.title).toBe('新建思维导图')
    })

    it('应该创建包含默认根节点的思维导图', async () => {
      mockDocumentDAO.create.mockResolvedValue(undefined)

      const result = await mindmapService.createMindMap()

      const rootNode = result.content.nodes[0]
      expect(rootNode.text).toBe('中心主题')
      expect(rootNode.level).toBe(0)
      expect(rootNode.children).toEqual([])
      expect(rootNode.collapsed).toBe(false)
    })
  })

  describe('saveMindMap', () => {
    it('应该保存思维导图并更新元数据', async () => {
      const mindmap = await mindmapService.createMindMap('测试思维导图')
      
      // 添加一些节点
      mindmap.content.nodes.push({
        id: 'node2',
        text: '子节点',
        x: 100,
        y: 100,
        width: 80,
        height: 30,
        color: '#000000',
        backgroundColor: '#ffffff',
        fontSize: 14,
        children: [],
        parent: mindmap.content.nodes[0].id,
        collapsed: false,
        level: 1
      })

      mockDocumentDAO.update.mockResolvedValue(undefined)

      await mindmapService.saveMindMap(mindmap)

      expect(mockDocumentDAO.update).toHaveBeenCalledTimes(1)
      expect(mockDocumentDAO.update).toHaveBeenCalledWith(
        expect.objectContaining({
          type: DocumentType.MINDMAP,
          metadata: expect.objectContaining({
            nodeCount: 2,
            maxLevel: 1
          })
        })
      )
    })
  })

  describe('loadMindMap', () => {
    it('应该加载存在的思维导图文档', async () => {
      const mockDoc = {
        id: 'test-id',
        type: DocumentType.MINDMAP,
        title: '测试思维导图',
        content: {
          nodes: [{
            id: 'root',
            text: '中心主题',
            x: 400,
            y: 300,
            width: 120,
            height: 40,
            color: '#000000',
            backgroundColor: '#ffffff',
            fontSize: 16,
            children: [],
            collapsed: false,
            level: 0
          }],
          connections: [],
          layout: { type: 'radial', direction: 'horizontal', spacing: 100 },
          viewport: { x: 0, y: 0, zoom: 1 },
          theme: { nodeColor: '#000000', nodeBackgroundColor: '#ffffff', connectionColor: '#666666', fontSize: 14 }
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: [],
        metadata: { nodeCount: 1, maxLevel: 0 }
      }

      mockDocumentDAO.getById.mockResolvedValue(mockDoc)

      const result = await mindmapService.loadMindMap('test-id')

      expect(result).toBeDefined()
      expect(result!.id).toBe('test-id')
      expect(result!.type).toBe(DocumentType.MINDMAP)
      expect(result!.title).toBe('测试思维导图')
      expect(result!.content.nodes).toHaveLength(1)

      expect(mockDocumentDAO.getById).toHaveBeenCalledWith('test-id')
    })

    it('应该在文档不存在时返回null', async () => {
      mockDocumentDAO.getById.mockResolvedValue(null)

      const result = await mindmapService.loadMindMap('non-existent')

      expect(result).toBeNull()
    })

    it('应该在文档类型不匹配时返回null', async () => {
      const mockDoc = {
        id: 'test-id',
        type: DocumentType.TEXT,
        title: '文本文档',
        content: 'test content',
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: [],
        metadata: {}
      }

      mockDocumentDAO.getById.mockResolvedValue(mockDoc)

      const result = await mindmapService.loadMindMap('test-id')

      expect(result).toBeNull()
    })
  })

  describe('deleteMindMap', () => {
    it('应该删除思维导图文档', async () => {
      mockDocumentDAO.delete.mockResolvedValue(undefined)

      const result = await mindmapService.deleteMindMap('test-id')

      expect(result).toBe(true)
      expect(mockDocumentDAO.delete).toHaveBeenCalledWith('test-id')
    })

    it('应该在删除失败时返回false', async () => {
      mockDocumentDAO.delete.mockRejectedValue(new Error('删除失败'))

      const result = await mindmapService.deleteMindMap('test-id')

      expect(result).toBe(false)
    })
  })

  describe('searchMindMaps', () => {
    it('应该根据标题搜索思维导图', async () => {
      const mockMindMaps = [
        {
          id: 'mm1',
          title: '项目规划思维导图',
          tags: ['规划'],
          type: DocumentType.MINDMAP,
          content: {
            nodes: [{ id: 'root', text: '项目规划', level: 0, children: [] }],
            connections: [],
            layout: {},
            viewport: {},
            theme: {}
          },
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        },
        {
          id: 'mm2',
          title: '学习笔记',
          tags: ['学习'],
          type: DocumentType.MINDMAP,
          content: {
            nodes: [{ id: 'root', text: '学习内容', level: 0, children: [] }],
            connections: [],
            layout: {},
            viewport: {},
            theme: {}
          },
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        }
      ]

      mockDocumentDAO.getByType.mockResolvedValue(mockMindMaps)

      const result = await mindmapService.searchMindMaps('规划')

      expect(result).toHaveLength(1)
      expect(result[0].title).toBe('项目规划思维导图')
    })

    it('应该根据节点内容搜索思维导图', async () => {
      const mockMindMaps = [
        {
          id: 'mm1',
          title: '思维导图1',
          tags: [],
          type: DocumentType.MINDMAP,
          content: {
            nodes: [
              { id: 'root', text: '中心主题', level: 0, children: [] },
              { id: 'node1', text: '重要概念', level: 1, children: [] }
            ],
            connections: [],
            layout: {},
            viewport: {},
            theme: {}
          },
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {}
        }
      ]

      mockDocumentDAO.getByType.mockResolvedValue(mockMindMaps)

      const result = await mindmapService.searchMindMaps('重要概念')

      expect(result).toHaveLength(1)
      expect(result[0].id).toBe('mm1')
    })
  })

  describe('exportMindMapAsImage', () => {
    it('应该导出思维导图为图片', async () => {
      // Mock canvas API
      const mockCanvas = {
        width: 0,
        height: 0,
        getContext: jest.fn(() => ({
          fillStyle: '',
          strokeStyle: '',
          lineWidth: 0,
          font: '',
          textAlign: '',
          textBaseline: '',
          fillRect: jest.fn(),
          strokeRect: jest.fn(),
          fillText: jest.fn(),
          beginPath: jest.fn(),
          moveTo: jest.fn(),
          lineTo: jest.fn(),
          stroke: jest.fn()
        })),
        toDataURL: jest.fn(() => 'data:image/png;base64,test')
      }

      // Mock document.createElement
      const originalCreateElement = document.createElement
      document.createElement = jest.fn((tagName) => {
        if (tagName === 'canvas') {
          return mockCanvas as any
        }
        return originalCreateElement.call(document, tagName)
      })

      const mindmap = await mindmapService.createMindMap('测试思维导图')

      const result = await mindmapService.exportMindMapAsImage(mindmap)

      expect(result).toBe('data:image/png;base64,test')
      expect(mockCanvas.toDataURL).toHaveBeenCalledWith('image/png', 0.9)

      // 恢复原始方法
      document.createElement = originalCreateElement
    })

    it('应该在没有节点时返回null', async () => {
      const mindmap = await mindmapService.createMindMap('空思维导图')
      mindmap.content.nodes = []

      const result = await mindmapService.exportMindMapAsImage(mindmap)

      expect(result).toBeNull()
    })
  })

  describe('updateMindMapData', () => {
    it('应该更新思维导图数据', async () => {
      const mindmap = await mindmapService.createMindMap('测试思维导图')
      
      mockDocumentDAO.getById.mockResolvedValue({
        ...mindmap,
        content: mindmap.content
      })
      mockDocumentDAO.update.mockResolvedValue(undefined)

      const newData = {
        nodes: [
          {
            id: 'new-root',
            text: '新的中心主题',
            x: 400,
            y: 300,
            width: 120,
            height: 40,
            color: '#000000',
            backgroundColor: '#ffffff',
            fontSize: 16,
            children: [],
            collapsed: false,
            level: 0
          }
        ],
        connections: []
      }

      const result = await mindmapService.updateMindMapData(mindmap.id, newData)

      expect(result).toBe(true)
      expect(mockDocumentDAO.update).toHaveBeenCalledTimes(1)
    })

    it('应该在思维导图不存在时返回false', async () => {
      mockDocumentDAO.getById.mockResolvedValue(null)

      const result = await mindmapService.updateMindMapData('non-existent', { nodes: [], connections: [] })

      expect(result).toBe(false)
    })
  })
})
