import React, { useState, useEffect, useCallback } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Card, Typography, Button, message, Spin, Space, Layout } from 'antd'
import {
  NodeIndexOutlined,
  SaveOutlined,
  ArrowLeftOutlined,
  FileImageOutlined,
  ShareAltOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons'
import MindMapCanvas from '@components/mindmap/MindMapCanvas'
import CrossFormatLinkPanel from '@components/crossFormat/CrossFormatLinkPanel'
import { mindmapService, MindMapData } from '@services/mindmapService'
import { MindMapDocument } from '@/types'

const { Title, Text } = Typography
const { Sider, Content } = Layout

/**
 * 思维导图页面组件
 * 提供完整的思维导图编辑功能
 */
const MindmapPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  const [mindmap, setMindmap] = useState<MindMapDocument | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [sidebarVisible, setSidebarVisible] = useState(true)

  /**
   * 加载思维导图文档
   */
  const loadMindMap = useCallback(async (mindmapId: string) => {
    try {
      setLoading(true)
      const doc = await mindmapService.loadMindMap(mindmapId)

      if (doc) {
        setMindmap(doc)
        console.log('思维导图加载成功:', doc.title)
      } else {
        message.error('思维导图不存在')
        navigate('/')
      }
    } catch (error) {
      console.error('加载思维导图失败:', error)
      message.error('加载思维导图失败')
      navigate('/')
    } finally {
      setLoading(false)
    }
  }, [navigate])

  /**
   * 创建新思维导图
   */
  const createNewMindMap = useCallback(async () => {
    try {
      setLoading(true)
      const newMindMap = await mindmapService.createMindMap('新建思维导图')
      setMindmap(newMindMap)

      // 更新URL
      window.history.replaceState(null, '', `/mindmap/${newMindMap.id}`)
      console.log('新思维导图创建成功')
    } catch (error) {
      console.error('创建思维导图失败:', error)
      message.error('创建思维导图失败')
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * 保存思维导图
   */
  const saveMindMap = useCallback(async (data?: MindMapData) => {
    if (!mindmap) return

    try {
      setSaving(true)

      // 如果有新数据，更新内容
      if (data) {
        mindmap.content.nodes = data.nodes
        mindmap.content.connections = data.connections
      }

      await mindmapService.saveMindMap(mindmap)
      message.success('思维导图保存成功')
    } catch (error) {
      console.error('保存思维导图失败:', error)
      message.error('保存思维导图失败')
    } finally {
      setSaving(false)
    }
  }, [mindmap])

  /**
   * 导出思维导图
   */
  const exportMindMap = useCallback(async () => {
    if (!mindmap) return

    try {
      const imageData = await mindmapService.exportMindMapAsImage(mindmap, {
        format: 'png',
        quality: 1.0
      })

      if (imageData) {
        // 创建下载链接
        const link = document.createElement('a')
        link.download = `${mindmap.title}.png`
        link.href = imageData
        link.click()

        message.success('思维导图导出成功')
      }
    } catch (error) {
      console.error('导出思维导图失败:', error)
      message.error('导出思维导图失败')
    }
  }, [mindmap])

  /**
   * 返回首页
   */
  const goBack = useCallback(() => {
    navigate('/')
  }, [navigate])

  /**
   * 切换侧边栏
   */
  const toggleSidebar = useCallback(() => {
    setSidebarVisible(prev => !prev)
  }, [])

  /**
   * 初始化
   */
  useEffect(() => {
    if (id) {
      loadMindMap(id)
    } else {
      createNewMindMap()
    }
  }, [id, loadMindMap, createNewMindMap])

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Spin size="large" tip="加载思维导图中..." />
      </div>
    )
  }

  if (!mindmap) {
    return (
      <div className="h-full p-6">
        <Card className="h-full">
          <div className="text-center py-20">
            <NodeIndexOutlined className="text-6xl text-red-500 mb-4" />
            <Title level={2}>思维导图加载失败</Title>
            <Button type="primary" onClick={goBack}>
              返回首页
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center gap-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={goBack}
            type="text"
          >
            返回
          </Button>
          <div>
            <Title level={4} className="mb-0">{mindmap.title}</Title>
            <Text type="secondary" className="text-sm">
              最后更新: {mindmap.updatedAt.toLocaleString()}
            </Text>
          </div>
        </div>

        <Space>
          <Button
            icon={sidebarVisible ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
            onClick={toggleSidebar}
            type="text"
          >
            {sidebarVisible ? '隐藏面板' : '显示面板'}
          </Button>
          <Button
            icon={<FileImageOutlined />}
            onClick={exportMindMap}
          >
            导出
          </Button>
          <Button
            icon={<ShareAltOutlined />}
            disabled
          >
            分享
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={saving}
            onClick={() => saveMindMap()}
          >
            保存
          </Button>
        </Space>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1">
        <Layout className="h-full">
          {/* 思维导图画布 */}
          <Content className="flex-1 p-4 bg-gray-100">
            <MindMapCanvas
              width={1200}
              height={800}
              onSave={saveMindMap}
              className="mx-auto"
            />
          </Content>

          {/* 右侧边栏 - 跨格式关联面板 */}
          {sidebarVisible && (
            <Sider
              width={320}
              theme="light"
              className="border-l border-gray-200"
              style={{
                height: '100%',
                overflow: 'auto',
                backgroundColor: '#fafafa'
              }}
            >
              <div className="p-4 h-full">
                <CrossFormatLinkPanel
                  document={mindmap}
                  className="h-full"
                />
              </div>
            </Sider>
          )}
        </Layout>
      </div>
    </div>
  )
}

export default MindmapPage
