import React, { useState, useEffect, useCallback } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Card, Typography, Button, message, Spin, Space, Layout } from 'antd'
import {
  BgColorsOutlined,
  SaveOutlined,
  ArrowLeftOutlined,
  FileImageOutlined,
  ShareAltOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons'
import WhiteboardCanvas from '@components/whiteboard/WhiteboardCanvas'
import CrossFormatLinkPanel from '@components/crossFormat/CrossFormatLinkPanel'
import { whiteboardService } from '@services/whiteboardService'
import { WhiteboardDocument } from '@/types'

const { Title, Text } = Typography
const { Sider, Content } = Layout

/**
 * 白板页面组件
 * 提供完整的白板绘图功能
 */
const WhiteboardPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  const [whiteboard, setWhiteboard] = useState<WhiteboardDocument | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [sidebarVisible, setSidebarVisible] = useState(true)

  /**
   * 加载白板文档
   */
  const loadWhiteboard = useCallback(async (whiteboardId: string) => {
    try {
      setLoading(true)
      const doc = await whiteboardService.loadWhiteboard(whiteboardId)

      if (doc) {
        setWhiteboard(doc)
        console.log('白板加载成功:', doc.title)
      } else {
        message.error('白板不存在')
        navigate('/')
      }
    } catch (error) {
      console.error('加载白板失败:', error)
      message.error('加载白板失败')
      navigate('/')
    } finally {
      setLoading(false)
    }
  }, [navigate])

  /**
   * 创建新白板
   */
  const createNewWhiteboard = useCallback(async () => {
    try {
      setLoading(true)
      const newWhiteboard = await whiteboardService.createWhiteboard('新建白板')
      setWhiteboard(newWhiteboard)

      // 更新URL
      window.history.replaceState(null, '', `/whiteboard/${newWhiteboard.id}`)
      console.log('新白板创建成功')
    } catch (error) {
      console.error('创建白板失败:', error)
      message.error('创建白板失败')
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * 处理白板内容变化
   */
  const handleWhiteboardChange = useCallback((data: string) => {
    if (!whiteboard) return

    try {
      const parsedData = JSON.parse(data)
      setWhiteboard(prev => prev ? {
        ...prev,
        content: {
          ...prev.content,
          objects: parsedData.objects || [],
          viewport: parsedData.viewport || prev.content.viewport
        },
        updatedAt: new Date()
      } : null)
    } catch (error) {
      console.error('解析白板数据失败:', error)
    }
  }, [whiteboard])

  /**
   * 保存白板
   */
  const saveWhiteboard = useCallback(async (imageData?: string) => {
    if (!whiteboard) return

    try {
      setSaving(true)

      // 如果有图片数据，生成缩略图
      if (imageData) {
        whiteboard.thumbnail = imageData
      }

      await whiteboardService.saveWhiteboard(whiteboard)
      message.success('白板保存成功')
    } catch (error) {
      console.error('保存白板失败:', error)
      message.error('保存白板失败')
    } finally {
      setSaving(false)
    }
  }, [whiteboard])

  /**
   * 导出白板
   */
  const exportWhiteboard = useCallback(async () => {
    if (!whiteboard) return

    try {
      const imageData = await whiteboardService.exportWhiteboardAsImage(whiteboard, {
        format: 'png',
        quality: 1.0
      })

      if (imageData) {
        // 创建下载链接
        const link = document.createElement('a')
        link.download = `${whiteboard.title}.png`
        link.href = imageData
        link.click()

        message.success('白板导出成功')
      }
    } catch (error) {
      console.error('导出白板失败:', error)
      message.error('导出白板失败')
    }
  }, [whiteboard])

  /**
   * 返回首页
   */
  const goBack = useCallback(() => {
    navigate('/')
  }, [navigate])

  /**
   * 切换侧边栏
   */
  const toggleSidebar = useCallback(() => {
    setSidebarVisible(prev => !prev)
  }, [])

  /**
   * 初始化
   */
  useEffect(() => {
    if (id) {
      loadWhiteboard(id)
    } else {
      createNewWhiteboard()
    }
  }, [id, loadWhiteboard, createNewWhiteboard])

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Spin size="large" tip="加载白板中..." />
      </div>
    )
  }

  if (!whiteboard) {
    return (
      <div className="h-full p-6">
        <Card className="h-full">
          <div className="text-center py-20">
            <BgColorsOutlined className="text-6xl text-red-500 mb-4" />
            <Title level={2}>白板加载失败</Title>
            <Button type="primary" onClick={goBack}>
              返回首页
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center gap-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={goBack}
            type="text"
          >
            返回
          </Button>
          <div>
            <Title level={4} className="mb-0">{whiteboard.title}</Title>
            <Text type="secondary" className="text-sm">
              最后更新: {whiteboard.updatedAt.toLocaleString()}
            </Text>
          </div>
        </div>

        <Space>
          <Button
            icon={sidebarVisible ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
            onClick={toggleSidebar}
            type="text"
          >
            {sidebarVisible ? '隐藏面板' : '显示面板'}
          </Button>
          <Button
            icon={<FileImageOutlined />}
            onClick={exportWhiteboard}
          >
            导出
          </Button>
          <Button
            icon={<ShareAltOutlined />}
            disabled
          >
            分享
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={saving}
            onClick={() => saveWhiteboard()}
          >
            保存
          </Button>
        </Space>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1">
        <Layout className="h-full">
          {/* 白板画布 */}
          <Content className="flex-1 p-4 bg-gray-100">
            <WhiteboardCanvas
              document={whiteboard}
              width={whiteboard.content.canvas.width}
              height={whiteboard.content.canvas.height}
              onChange={handleWhiteboardChange}
              onSave={saveWhiteboard}
              className="mx-auto"
            />
          </Content>

          {/* 右侧边栏 - 跨格式关联面板 */}
          {sidebarVisible && (
            <Sider
              width={320}
              theme="light"
              className="border-l border-gray-200"
              style={{
                height: '100%',
                overflow: 'auto',
                backgroundColor: '#fafafa'
              }}
            >
              <div className="p-4 h-full">
                <CrossFormatLinkPanel
                  document={whiteboard}
                  className="h-full"
                />
              </div>
            </Sider>
          )}
        </Layout>
      </div>
    </div>
  )
}

export default WhiteboardPage
