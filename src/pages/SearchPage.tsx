/**
 * 搜索页面组件
 * 提供完整的文档搜索功能，包括基础搜索、高级搜索和搜索结果展示
 */

import React, { useState, useCallback, useEffect } from 'react'
import { Layout, Card, Row, Col, Divider, message } from 'antd'
import { useSearchParams, useNavigate } from 'react-router-dom'
import SearchInput from '@components/search/SearchInput'
import SearchResults from '@components/search/SearchResults'
import AdvancedSearch from '@components/search/AdvancedSearch'
import DocumentCategories from '@components/document/DocumentCategories'
import { BaseDocument, SearchOptions } from '@/types'
import { documentManager } from '@services/document/documentManager'
import {
  advancedSearchService,
  SearchResult,
  SearchFilter,
  SearchOptions as AdvancedSearchOptions
} from '@services/search/AdvancedSearchService'
import type { AdvancedSearchFilters } from '@components/search/AdvancedSearch'
import type { SearchResultItem } from '@components/search/SearchResults'

const { Content, Sider } = Layout

/**
 * 搜索页面组件
 */
const SearchPage: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  
  // 搜索状态
  const [query, setQuery] = useState(searchParams.get('q') || '')
  const [results, setResults] = useState<SearchResultItem[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [current, setCurrent] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  
  // 筛选状态
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>()
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [advancedSearchVisible, setAdvancedSearchVisible] = useState(false)
  const [currentFilters, setCurrentFilters] = useState<AdvancedSearchFilters>({})

  /**
   * 执行搜索
   */
  const performSearch = useCallback(async (
    searchQuery: string,
    filters: AdvancedSearchFilters = {},
    page: number = 1,
    size: number = 20
  ) => {
    if (!searchQuery.trim() && Object.keys(filters).length === 0) {
      setResults([])
      setTotal(0)
      return
    }

    try {
      setLoading(true)

      // 构建高级搜索选项
      const searchOptions: AdvancedSearchOptions = {
        query: searchQuery,
        filters: {
          types: filters.types,
          tags: selectedTags.length > 0 ? selectedTags : filters.tags,
          dateRange: filters.dateRange ? {
            start: filters.dateRange[0],
            end: filters.dateRange[1]
          } : undefined,
          hasLinks: filters.hasLinks,
          minWordCount: filters.wordCountRange?.[0],
          maxWordCount: filters.wordCountRange?.[1]
        },
        sortBy: filters.sortBy || 'relevance',
        sortOrder: filters.sortOrder || 'desc',
        limit: size,
        offset: (page - 1) * size,
        includeContent: true
      }

      // 使用高级搜索服务
      const { results: searchResults, total: totalCount } = await advancedSearchService.search(searchOptions)

      // 转换为搜索结果格式
      const resultItems: SearchResultItem[] = searchResults.map((result: SearchResult) => ({
        ...result.document,
        highlights: result.highlights,
        score: result.score,
        matchedFields: [result.matchType],
        relevanceReason: result.relevanceReason
      }))

      setResults(resultItems)
      setTotal(totalCount)
      setCurrent(page)
      setPageSize(size)

    } catch (error) {
      console.error('搜索失败:', error)
      message.error('搜索失败，请重试')
      setResults([])
      setTotal(0)
    } finally {
      setLoading(false)
    }
  }, [selectedTags])

  /**
   * 处理基础搜索
   */
  const handleSearch = useCallback((searchQuery: string) => {
    setQuery(searchQuery)
    setCurrentFilters({})
    setCurrent(1)
    
    // 更新URL参数
    if (searchQuery) {
      setSearchParams({ q: searchQuery })
    } else {
      setSearchParams({})
    }
    
    performSearch(searchQuery, {}, 1, pageSize)
  }, [pageSize, performSearch, setSearchParams])

  /**
   * 处理高级搜索
   */
  const handleAdvancedSearch = useCallback((filters: AdvancedSearchFilters) => {
    const searchQuery = filters.keywords || query
    setQuery(searchQuery)
    setCurrentFilters(filters)
    setCurrent(1)
    
    // 更新URL参数
    const params: Record<string, string> = {}
    if (searchQuery) params.q = searchQuery
    if (filters.types?.length) params.types = filters.types.join(',')
    if (filters.tags?.length) params.tags = filters.tags.join(',')
    setSearchParams(params)
    
    performSearch(searchQuery, filters, 1, pageSize)
  }, [query, pageSize, performSearch, setSearchParams])

  /**
   * 处理分页变化
   */
  const handlePageChange = useCallback((page: number, size: number) => {
    performSearch(query, currentFilters, page, size)
  }, [query, currentFilters, performSearch])

  /**
   * 处理文档选择
   */
  const handleDocumentSelect = useCallback((document: BaseDocument) => {
    // 根据文档类型导航到相应的编辑器
    switch (document.type) {
      case 'text':
        navigate(`/editor/${document.id}`)
        break
      case 'whiteboard':
        navigate(`/whiteboard/${document.id}`)
        break
      case 'mindmap':
        navigate(`/mindmap/${document.id}`)
        break
      case 'kanban':
        navigate(`/kanban/${document.id}`)
        break
      default:
        navigate(`/editor/${document.id}`)
    }
  }, [navigate])

  /**
   * 处理分类选择
   */
  const handleCategorySelect = useCallback((categoryId?: string) => {
    setSelectedCategoryId(categoryId)
    // 重新执行搜索
    if (query || Object.keys(currentFilters).length > 0) {
      performSearch(query, currentFilters, 1, pageSize)
    }
  }, [query, currentFilters, pageSize, performSearch])

  /**
   * 处理标签选择
   */
  const handleTagSelect = useCallback((tags: string[]) => {
    setSelectedTags(tags)
    // 重新执行搜索
    if (query || Object.keys(currentFilters).length > 0) {
      performSearch(query, currentFilters, 1, pageSize)
    }
  }, [query, currentFilters, pageSize, performSearch])

  /**
   * 初始化搜索
   */
  useEffect(() => {
    const initialQuery = searchParams.get('q')
    if (initialQuery) {
      setQuery(initialQuery)
      performSearch(initialQuery, {}, 1, pageSize)
    }
  }, [searchParams, pageSize, performSearch])

  return (
    <Layout className="h-full">
      <Content className="p-6">
        <div className="max-w-7xl mx-auto h-full">
          {/* 搜索头部 */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold mb-4">文档搜索</h1>
            <SearchInput
              value={query}
              onSearch={handleSearch}
              showAdvanced={true}
              showHistory={true}
              showSuggestions={true}
              size="large"
              className="max-w-2xl"
            />
          </div>

          <Row gutter={24} className="h-full">
            {/* 左侧筛选面板 */}
            <Col span={6}>
              <Card title="筛选条件" className="h-fit">
                <DocumentCategories
                  selectedCategoryId={selectedCategoryId}
                  selectedTags={selectedTags}
                  onCategorySelect={handleCategorySelect}
                  onTagSelect={handleTagSelect}
                />
              </Card>
            </Col>

            {/* 右侧搜索结果 */}
            <Col span={18}>
              <Card className="h-full">
                <SearchResults
                  results={results}
                  query={query}
                  total={total}
                  current={current}
                  pageSize={pageSize}
                  loading={loading}
                  onPageChange={handlePageChange}
                  onDocumentSelect={handleDocumentSelect}
                />
              </Card>
            </Col>
          </Row>
        </div>
      </Content>

      {/* 高级搜索对话框 */}
      <AdvancedSearch
        visible={advancedSearchVisible}
        initialFilters={currentFilters}
        onClose={() => setAdvancedSearchVisible(false)}
        onSearch={handleAdvancedSearch}
        onReset={() => {
          setCurrentFilters({})
          setQuery('')
          setResults([])
          setTotal(0)
          setSearchParams({})
        }}
      />
    </Layout>
  )
}

export default SearchPage
