/**
 * 链接管理页面
 * 提供全局的链接管理和分析功能
 */

import React, { useState, useEffect, useCallback } from 'react'
import {
  Layout,
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Statistic,
  Row,
  Col,
  message,
  Popconfirm,
  Tooltip,
  Typography
} from 'antd'
import {
  LinkOutlined,
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
  FilterOutlined,
  Bar<PERSON>hartOutlined,
  NodeIndexOutlined,
  FileTextOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { DocumentLink, BaseDocument, LinkType } from '@/types'
import { linkService } from '@services/link/linkService'
import { documentManager } from '@services/document/documentManager'
import { formatRelativeTime } from '@utils/index'
import LinkEditModal from '@components/link/LinkEditModal'

const { Content } = Layout
const { Search } = Input
const { Text } = Typography

/**
 * 链接统计接口
 */
interface LinkStats {
  totalLinks: number
  linksByType: Record<LinkType, number>
  orphanDocuments: number
  mostLinkedDocuments: Array<{
    document: BaseDocument
    linkCount: number
  }>
}

/**
 * 链接管理页面组件
 */
const LinkManagePage: React.FC = () => {
  const navigate = useNavigate()
  
  const [links, setLinks] = useState<DocumentLink[]>([])
  const [documents, setDocuments] = useState<Map<string, BaseDocument>>(new Map())
  const [stats, setStats] = useState<LinkStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedType, setSelectedType] = useState<LinkType | 'all'>('all')
  const [editingLink, setEditingLink] = useState<DocumentLink | null>(null)
  const [editModalVisible, setEditModalVisible] = useState(false)

  /**
   * 加载链接数据
   */
  const loadLinks = useCallback(async () => {
    try {
      setLoading(true)
      
      // 获取所有链接
      const allLinks = await linkService.getAllLinks()
      setLinks(allLinks)
      
      // 获取相关文档
      const documentIds = new Set<string>()
      allLinks.forEach(link => {
        documentIds.add(link.sourceId)
        documentIds.add(link.targetId)
      })
      
      const documentMap = new Map<string, BaseDocument>()
      for (const id of documentIds) {
        try {
          const doc = await documentManager.getDocumentById(id)
          if (doc) {
            documentMap.set(id, doc)
          }
        } catch (error) {
          console.warn(`无法加载文档 ${id}:`, error)
        }
      }
      setDocuments(documentMap)
      
      // 计算统计信息
      const linksByType = allLinks.reduce((acc, link) => {
        acc[link.type] = (acc[link.type] || 0) + 1
        return acc
      }, {} as Record<LinkType, number>)
      
      // 计算最多链接的文档
      const linkCounts = new Map<string, number>()
      allLinks.forEach(link => {
        linkCounts.set(link.sourceId, (linkCounts.get(link.sourceId) || 0) + 1)
        linkCounts.set(link.targetId, (linkCounts.get(link.targetId) || 0) + 1)
      })
      
      const mostLinkedDocuments = Array.from(linkCounts.entries())
        .map(([docId, count]) => ({
          document: documentMap.get(docId)!,
          linkCount: count
        }))
        .filter(item => item.document)
        .sort((a, b) => b.linkCount - a.linkCount)
        .slice(0, 5)
      
      // 计算孤立文档数量
      const allDocuments = await documentManager.getAllDocuments()
      const linkedDocumentIds = new Set(documentIds)
      const orphanDocuments = allDocuments.filter(doc => !linkedDocumentIds.has(doc.id)).length
      
      setStats({
        totalLinks: allLinks.length,
        linksByType,
        orphanDocuments,
        mostLinkedDocuments
      })
      
    } catch (error) {
      console.error('加载链接数据失败:', error)
      message.error('加载链接数据失败')
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * 删除链接
   */
  const handleDeleteLink = useCallback(async (linkId: string) => {
    try {
      await linkService.deleteLink(linkId)
      message.success('链接删除成功')
      loadLinks()
    } catch (error) {
      console.error('删除链接失败:', error)
      message.error('删除链接失败')
    }
  }, [loadLinks])

  /**
   * 编辑链接
   */
  const handleEditLink = useCallback((link: DocumentLink) => {
    setEditingLink(link)
    setEditModalVisible(true)
  }, [])

  /**
   * 保存链接编辑
   */
  const handleSaveLink = useCallback(async (linkId: string, updates: Partial<DocumentLink>) => {
    try {
      await linkService.updateLink(linkId, updates)
      message.success('链接更新成功')
      setEditModalVisible(false)
      setEditingLink(null)
      loadLinks()
    } catch (error) {
      console.error('更新链接失败:', error)
      message.error('更新链接失败')
      throw error
    }
  }, [loadLinks])

  /**
   * 导航到文档
   */
  const navigateToDocument = useCallback((document: BaseDocument) => {
    switch (document.type) {
      case 'text':
        navigate(`/editor/${document.id}`)
        break
      case 'whiteboard':
        navigate(`/whiteboard/${document.id}`)
        break
      case 'mindmap':
        navigate(`/mindmap/${document.id}`)
        break
      case 'kanban':
        navigate(`/kanban/${document.id}`)
        break
      default:
        navigate(`/editor/${document.id}`)
    }
  }, [navigate])

  /**
   * 过滤链接
   */
  const filteredLinks = links.filter(link => {
    if (selectedType !== 'all' && link.type !== selectedType) {
      return false
    }
    
    if (searchQuery) {
      const sourceDoc = documents.get(link.sourceId)
      const targetDoc = documents.get(link.targetId)
      const searchLower = searchQuery.toLowerCase()
      
      return (
        sourceDoc?.title.toLowerCase().includes(searchLower) ||
        targetDoc?.title.toLowerCase().includes(searchLower) ||
        link.label?.toLowerCase().includes(searchLower)
      )
    }
    
    return true
  })

  /**
   * 表格列定义
   */
  const columns = [
    {
      title: '源文档',
      key: 'source',
      render: (_, link: DocumentLink) => {
        const doc = documents.get(link.sourceId)
        return doc ? (
          <Button
            type="link"
            onClick={() => navigateToDocument(doc)}
            className="p-0 h-auto"
          >
            <div className="flex items-center space-x-2">
              <FileTextOutlined />
              <span>{doc.title}</span>
            </div>
          </Button>
        ) : (
          <Text type="secondary">文档不存在</Text>
        )
      }
    },
    {
      title: '目标文档',
      key: 'target',
      render: (_, link: DocumentLink) => {
        const doc = documents.get(link.targetId)
        return doc ? (
          <Button
            type="link"
            onClick={() => navigateToDocument(doc)}
            className="p-0 h-auto"
          >
            <div className="flex items-center space-x-2">
              <FileTextOutlined />
              <span>{doc.title}</span>
            </div>
          </Button>
        ) : (
          <Text type="secondary">文档不存在</Text>
        )
      }
    },
    {
      title: '链接类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: LinkType) => {
        const typeNames = {
          [LinkType.REFERENCE]: '引用',
          [LinkType.EMBED]: '嵌入',
          [LinkType.RELATED]: '相关',
          [LinkType.PARENT_CHILD]: '父子'
        }
        const colors = {
          [LinkType.REFERENCE]: 'blue',
          [LinkType.EMBED]: 'green',
          [LinkType.RELATED]: 'orange',
          [LinkType.PARENT_CHILD]: 'purple'
        }
        return (
          <Tag color={colors[type]}>
            {typeNames[type] || type}
          </Tag>
        )
      }
    },
    {
      title: '标签',
      dataIndex: 'label',
      key: 'label',
      render: (label: string) => label || '-'
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: Date) => formatRelativeTime(date)
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, link: DocumentLink) => (
        <Space>
          <Tooltip title="编辑链接">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditLink(link)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个链接吗？"
            onConfirm={() => handleDeleteLink(link.id)}
            okText="删除"
            cancelText="取消"
            okType="danger"
          >
            <Tooltip title="删除链接">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  useEffect(() => {
    loadLinks()
  }, [loadLinks])

  return (
    <Layout className="h-full">
      <Content className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold mb-4">链接管理</h1>
            
            {/* 统计信息 */}
            {stats && (
              <Row gutter={16} className="mb-6">
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="总链接数"
                      value={stats.totalLinks}
                      prefix={<LinkOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic
                      title="孤立文档"
                      value={stats.orphanDocuments}
                      prefix={<NodeIndexOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title="链接类型分布">
                    <Space wrap>
                      {Object.entries(stats.linksByType).map(([type, count]) => (
                        <Tag key={type} color="blue">
                          {type}: {count}
                        </Tag>
                      ))}
                    </Space>
                  </Card>
                </Col>
              </Row>
            )}
            
            {/* 搜索和筛选 */}
            <div className="flex items-center justify-between mb-4">
              <Space>
                <Search
                  placeholder="搜索链接..."
                  allowClear
                  style={{ width: 300 }}
                  onSearch={setSearchQuery}
                />
                <Select
                  value={selectedType}
                  onChange={setSelectedType}
                  style={{ width: 120 }}
                >
                  <Select.Option value="all">全部类型</Select.Option>
                  <Select.Option value={LinkType.REFERENCE}>引用</Select.Option>
                  <Select.Option value={LinkType.EMBED}>嵌入</Select.Option>
                  <Select.Option value={LinkType.RELATED}>相关</Select.Option>
                  <Select.Option value={LinkType.PARENT_CHILD}>父子</Select.Option>
                </Select>
              </Space>
              
              <Space>
                <Button
                  icon={<BarChartOutlined />}
                  onClick={() => navigate('/graph')}
                >
                  关系图谱
                </Button>
                <Button
                  type="primary"
                  icon={<FilterOutlined />}
                  onClick={loadLinks}
                >
                  刷新
                </Button>
              </Space>
            </div>
          </div>

          {/* 链接表格 */}
          <Card>
            <Table
              columns={columns}
              dataSource={filteredLinks}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }}
            />
          </Card>
        </div>
      </Content>

      {/* 链接编辑对话框 */}
      <LinkEditModal
        visible={editModalVisible}
        link={editingLink}
        sourceDocument={editingLink ? documents.get(editingLink.sourceId) : undefined}
        targetDocument={editingLink ? documents.get(editingLink.targetId) : undefined}
        onClose={() => {
          setEditModalVisible(false)
          setEditingLink(null)
        }}
        onSave={handleSaveLink}
      />
    </Layout>
  )
}

export default LinkManagePage
