import React, { useState, useEffect, useCallback } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Card, Typography, Button, message, Spin, Space, Statistic, Row, Col, Layout } from 'antd'
import {
  ProjectOutlined,
  SaveOutlined,
  <PERSON><PERSON>eftOutlined,
  DownloadOutlined,
  UploadOutlined,
  BarChartOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons'
import KanbanBoard, { KanbanData } from '@components/kanban/KanbanBoard'
import CrossFormatLinkPanel from '@components/crossFormat/CrossFormatLinkPanel'
import { kanbanService } from '@services/kanbanService'
import { KanbanDocument } from '@/types'

const { Title, Text } = Typography
const { Sider, Content } = Layout

/**
 * 看板页面组件
 * 提供完整的看板管理功能
 */
const KanbanPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  const [kanban, setKanban] = useState<KanbanDocument | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [stats, setStats] = useState<any>(null)
  const [sidebarVisible, setSidebarVisible] = useState(true)

  /**
   * 加载看板文档
   */
  const loadKanban = useCallback(async (kanbanId: string) => {
    try {
      setLoading(true)
      const doc = await kanbanService.loadKanban(kanbanId)

      if (doc) {
        setKanban(doc)
        console.log('看板加载成功:', doc.title)

        // 加载统计信息
        const statsData = await kanbanService.getKanbanStats(kanbanId)
        setStats(statsData)
      } else {
        message.error('看板不存在')
        navigate('/')
      }
    } catch (error) {
      console.error('加载看板失败:', error)
      message.error('加载看板失败')
      navigate('/')
    } finally {
      setLoading(false)
    }
  }, [navigate])

  /**
   * 创建新看板
   */
  const createNewKanban = useCallback(async () => {
    try {
      setLoading(true)
      const newKanban = await kanbanService.createKanban('新建看板')
      setKanban(newKanban)

      // 更新URL
      window.history.replaceState(null, '', `/kanban/${newKanban.id}`)
      console.log('新看板创建成功')
    } catch (error) {
      console.error('创建看板失败:', error)
      message.error('创建看板失败')
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * 保存看板
   */
  const saveKanban = useCallback(async (data?: KanbanData) => {
    if (!kanban) return

    try {
      setSaving(true)

      // 如果有新数据，更新内容
      if (data) {
        kanban.content.cards = data.cards
        kanban.content.columns = data.columns
      }

      await kanbanService.saveKanban(kanban)
      message.success('看板保存成功')

      // 重新加载统计信息
      if (kanban.id) {
        const statsData = await kanbanService.getKanbanStats(kanban.id)
        setStats(statsData)
      }
    } catch (error) {
      console.error('保存看板失败:', error)
      message.error('保存看板失败')
    } finally {
      setSaving(false)
    }
  }, [kanban])

  /**
   * 导出看板
   */
  const exportKanban = useCallback(async () => {
    if (!kanban) return

    try {
      const jsonData = await kanbanService.exportKanbanData(kanban.id)

      if (jsonData) {
        // 创建下载链接
        const blob = new Blob([jsonData], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.download = `${kanban.title}.json`
        link.href = url
        link.click()
        URL.revokeObjectURL(url)

        message.success('看板导出成功')
      }
    } catch (error) {
      console.error('导出看板失败:', error)
      message.error('导出看板失败')
    }
  }, [kanban])

  /**
   * 返回首页
   */
  const goBack = useCallback(() => {
    navigate('/')
  }, [navigate])

  /**
   * 切换侧边栏
   */
  const toggleSidebar = useCallback(() => {
    setSidebarVisible(prev => !prev)
  }, [])

  /**
   * 初始化
   */
  useEffect(() => {
    if (id) {
      loadKanban(id)
    } else {
      createNewKanban()
    }
  }, [id, loadKanban, createNewKanban])

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Spin size="large" tip="加载看板中..." />
      </div>
    )
  }

  if (!kanban) {
    return (
      <div className="h-full p-6">
        <Card className="h-full">
          <div className="text-center py-20">
            <ProjectOutlined className="text-6xl text-red-500 mb-4" />
            <Title level={2}>看板加载失败</Title>
            <Button type="primary" onClick={goBack}>
              返回首页
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center gap-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={goBack}
            type="text"
          >
            返回
          </Button>
          <div>
            <Title level={4} className="mb-0">{kanban.title}</Title>
            <Text type="secondary" className="text-sm">
              最后更新: {kanban.updatedAt.toLocaleString()}
            </Text>
          </div>
        </div>

        <Space>
          <Button
            icon={sidebarVisible ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
            onClick={toggleSidebar}
            type="text"
          >
            {sidebarVisible ? '隐藏面板' : '显示面板'}
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={exportKanban}
          >
            导出
          </Button>
          <Button
            icon={<UploadOutlined />}
            disabled
          >
            导入
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={saving}
            onClick={() => saveKanban()}
          >
            保存
          </Button>
        </Space>
      </div>

      {/* 统计信息 */}
      {stats && (
        <div className="p-4 bg-gray-50 border-b">
          <Row gutter={16}>
            <Col span={6}>
              <Statistic title="总卡片数" value={stats.totalCards} />
            </Col>
            <Col span={6}>
              <Statistic title="待办" value={stats.cardsByStatus.todo} />
            </Col>
            <Col span={6}>
              <Statistic title="进行中" value={stats.cardsByStatus.in_progress} />
            </Col>
            <Col span={6}>
              <Statistic
                title="逾期任务"
                value={stats.overdueTasks}
                valueStyle={{ color: stats.overdueTasks > 0 ? '#cf1322' : undefined }}
              />
            </Col>
          </Row>
        </div>
      )}

      {/* 主内容区域 */}
      <div className="flex-1">
        <Layout className="h-full">
          {/* 看板内容 */}
          <Content className="flex-1 p-4 bg-gray-100 overflow-hidden">
            <KanbanBoard
              data={{
                columns: kanban.content.columns,
                cards: kanban.content.cards
              }}
              onSave={saveKanban}
              className="h-full"
            />
          </Content>

          {/* 右侧边栏 - 跨格式关联面板 */}
          {sidebarVisible && (
            <Sider
              width={320}
              theme="light"
              className="border-l border-gray-200"
              style={{
                height: '100%',
                overflow: 'auto',
                backgroundColor: '#fafafa'
              }}
            >
              <div className="p-4 h-full">
                <CrossFormatLinkPanel
                  document={kanban}
                  className="h-full"
                />
              </div>
            </Sider>
          )}
        </Layout>
      </div>
    </div>
  )
}

export default KanbanPage
