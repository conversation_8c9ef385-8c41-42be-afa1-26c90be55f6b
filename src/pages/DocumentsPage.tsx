/**
 * 文档页面组件
 * 显示完整的文档列表和管理功能
 */

import React, { useState } from 'react'
import { Layout, Card, Tabs, Button, Space, Modal, message, Row, Col } from 'antd'
import {
  FileTextOutlined,
  DeleteOutlined,
  RestoreOutlined,
  ClearOutlined,
  ExportOutlined,
  ImportOutlined,
  SearchOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import DocumentList from '@components/document/DocumentList'
import DocumentCategories from '@components/document/DocumentCategories'
import SearchInput from '@components/search/SearchInput'
import { documentManager } from '@services/document/documentManager'
import { backupService } from '@services/database/backupService'
import { DocumentType, BaseDocument } from '@/types'

const { Content } = Layout
const { TabPane } = Tabs

/**
 * 文档页面组件
 */
const DocumentsPage: React.FC = () => {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('all')
  const [refreshKey, setRefreshKey] = useState(0)
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>()
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')

  /**
   * 刷新文档列表
   */
  const refreshDocuments = () => {
    setRefreshKey(prev => prev + 1)
  }

  /**
   * 处理文档创建
   */
  const handleDocumentCreate = (type: DocumentType) => {
    // 导航到相应的编辑器页面
    switch (type) {
      case DocumentType.TEXT:
        navigate('/editor')
        break
      case DocumentType.WHITEBOARD:
        navigate('/whiteboard')
        break
      case DocumentType.MINDMAP:
        navigate('/mindmap')
        break
      case DocumentType.KANBAN:
        navigate('/kanban')
        break
      default:
        navigate('/editor')
    }
  }

  /**
   * 处理搜索
   */
  const handleSearch = (query: string) => {
    if (query.trim()) {
      // 导航到搜索页面
      navigate(`/search?q=${encodeURIComponent(query)}`)
    } else {
      setSearchQuery('')
      refreshDocuments()
    }
  }

  /**
   * 处理分类选择
   */
  const handleCategorySelect = (categoryId?: string) => {
    setSelectedCategoryId(categoryId)
    refreshDocuments()
  }

  /**
   * 处理标签选择
   */
  const handleTagSelect = (tags: string[]) => {
    setSelectedTags(tags)
    refreshDocuments()
  }

  /**
   * 导出数据
   */
  const handleExportData = async () => {
    try {
      await backupService.downloadBackup()
      message.success('数据导出成功')
    } catch (error) {
      console.error('导出数据失败:', error)
      message.error('导出数据失败')
    }
  }

  /**
   * 导入数据
   */
  const handleImportData = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = async (event) => {
      const file = (event.target as HTMLInputElement).files?.[0]
      if (file) {
        try {
          const result = await backupService.importFromFile(file, {
            skipDuplicates: true
          })
          
          if (result.success) {
            message.success(`导入成功: ${result.documentsImported} 个文档, ${result.linksImported} 个链接`)
            refreshDocuments()
          } else {
            message.error(`导入失败: ${result.errors.join(', ')}`)
          }
        } catch (error) {
          console.error('导入数据失败:', error)
          message.error('导入数据失败')
        }
      }
    }
    input.click()
  }

  /**
   * 清空回收站
   */
  const handleEmptyTrash = () => {
    Modal.confirm({
      title: '确认清空回收站',
      content: '此操作将永久删除回收站中的所有文档，无法恢复。确定要继续吗？',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await documentManager.emptyTrash()
          message.success('回收站已清空')
          refreshDocuments()
        } catch (error) {
          console.error('清空回收站失败:', error)
          message.error('清空回收站失败')
        }
      }
    })
  }

  /**
   * 渲染工具栏
   */
  const renderToolbar = () => (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-xl font-semibold m-0">文档管理</h2>
        </div>
        <Space>
          <Button
            icon={<SearchOutlined />}
            onClick={() => navigate('/search')}
          >
            高级搜索
          </Button>
          <Button
            icon={<ImportOutlined />}
            onClick={handleImportData}
          >
            导入数据
          </Button>
          <Button
            icon={<ExportOutlined />}
            onClick={handleExportData}
          >
            导出数据
          </Button>
          {activeTab === 'trash' && (
            <Button
              danger
              icon={<ClearOutlined />}
              onClick={handleEmptyTrash}
            >
              清空回收站
            </Button>
          )}
        </Space>
      </div>

      {/* 搜索栏 */}
      <SearchInput
        value={searchQuery}
        onSearch={handleSearch}
        onChange={setSearchQuery}
        placeholder="搜索文档..."
        showAdvanced={false}
        showHistory={false}
        showSuggestions={true}
        size="large"
        className="max-w-md"
      />
    </div>
  )

  /**
   * 渲染文档列表
   */
  const renderDocumentList = (type?: 'trash') => (
    <DocumentList
      key={`${activeTab}-${refreshKey}`}
      showCreateButton={type !== 'trash'}
      showSearch={true}
      showFilters={true}
      pageSize={20}
      onDocumentCreate={handleDocumentCreate}
      className="h-full"
    />
  )

  /**
   * 渲染回收站列表
   */
  const renderTrashList = () => {
    // 这里需要一个专门的回收站组件
    // 暂时使用普通文档列表
    return (
      <Card className="h-full">
        <div className="text-center py-8 text-gray-500">
          <DeleteOutlined className="text-4xl mb-4" />
          <div className="text-lg mb-2">回收站</div>
          <div className="text-sm">已删除的文档会在这里显示</div>
          <div className="text-xs mt-2">功能开发中...</div>
        </div>
      </Card>
    )
  }

  return (
    <Layout className="h-full">
      <Content className="p-6">
        <div className="max-w-7xl mx-auto h-full">
          {renderToolbar()}

          <Row gutter={24} className="h-full">
            {/* 左侧筛选面板 */}
            <Col span={6}>
              <Card title="筛选条件" className="h-fit">
                <DocumentCategories
                  selectedCategoryId={selectedCategoryId}
                  selectedTags={selectedTags}
                  onCategorySelect={handleCategorySelect}
                  onTagSelect={handleTagSelect}
                />
              </Card>
            </Col>

            {/* 右侧文档列表 */}
            <Col span={18}>
              <div className="h-full">
                <Tabs
                  activeKey={activeTab}
                  onChange={setActiveTab}
                  className="h-full"
                  tabBarStyle={{ marginBottom: 16 }}
                >
                  <TabPane
                    tab={
                      <span>
                        <FileTextOutlined />
                        全部文档
                      </span>
                    }
                    key="all"
                    className="h-full"
                  >
                    {renderDocumentList()}
                  </TabPane>

                  <TabPane
                    tab={
                      <span>
                        <FileTextOutlined />
                        文本笔记
                      </span>
                    }
                    key="text"
                    className="h-full"
                  >
                    {renderDocumentList()}
                  </TabPane>

                  <TabPane
                    tab={
                      <span>
                        <DeleteOutlined />
                        回收站
                      </span>
                    }
                    key="trash"
                    className="h-full"
                  >
                    {renderTrashList()}
                  </TabPane>
                </Tabs>
              </div>
            </Col>
          </Row>
        </div>
      </Content>
    </Layout>
  )
}

export default DocumentsPage
