/**
 * 数据分析页面组件
 * 提供知识图谱分析、内容统计报表、使用习惯分析功能
 */

import React, { useState, useEffect, useCallback } from 'react'
import { 
  Card, 
  Typography, 
  Tabs, 
  Row, 
  Col, 
  Statistic, 
  Progress, 
  Table, 
  List, 
  Tag, 
  Spin,
  Button,
  Space,
  message,
  Empty,
  Tooltip
} from 'antd'
import {
  Bar<PERSON><PERSON>Outlined,
  PieChartOutlined,
  Line<PERSON>hartOutlined,
  DashboardOutlined,
  FileTextOutlined,
  LinkOutlined,
  TagOutlined,
  ClockCircleOutlined,
  ShareAltOutlined,
  QualityOutlined,
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { DocumentType } from '@/types'
import { 
  analyticsService,
  DocumentStats,
  LinkStats,
  TagStats,
  UsageAnalytics,
  GraphAnalytics,
  ContentQualityAnalysis
} from '@services/analytics/AnalyticsService'

const { Title, Text } = Typography
const { TabPane } = Tabs

/**
 * 文档类型标签映射
 */
const DOCUMENT_TYPE_LABELS = {
  [DocumentType.TEXT]: '文本',
  [DocumentType.WHITEBOARD]: '白板',
  [DocumentType.MINDMAP]: '思维导图',
  [DocumentType.KANBAN]: '看板'
}

/**
 * 文档类型颜色映射
 */
const DOCUMENT_TYPE_COLORS = {
  [DocumentType.TEXT]: 'blue',
  [DocumentType.WHITEBOARD]: 'green',
  [DocumentType.MINDMAP]: 'purple',
  [DocumentType.KANBAN]: 'orange'
}

/**
 * 数据分析页面组件
 */
const AnalyticsPage: React.FC = () => {
  const navigate = useNavigate()

  // 数据状态
  const [documentStats, setDocumentStats] = useState<DocumentStats | null>(null)
  const [linkStats, setLinkStats] = useState<LinkStats | null>(null)
  const [tagStats, setTagStats] = useState<TagStats | null>(null)
  const [usageAnalytics, setUsageAnalytics] = useState<UsageAnalytics | null>(null)
  const [graphAnalytics, setGraphAnalytics] = useState<GraphAnalytics | null>(null)
  const [contentQuality, setContentQuality] = useState<ContentQualityAnalysis | null>(null)

  // 加载状态
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  /**
   * 加载所有分析数据
   */
  const loadAnalyticsData = useCallback(async () => {
    try {
      setLoading(true)

      const [
        docStats,
        linkStatsData,
        tagStatsData,
        usageData,
        graphData,
        qualityData
      ] = await Promise.all([
        analyticsService.getDocumentStats(),
        analyticsService.getLinkStats(),
        analyticsService.getTagStats(),
        analyticsService.getUsageAnalytics(),
        analyticsService.getGraphAnalytics(),
        analyticsService.getContentQualityAnalysis()
      ])

      setDocumentStats(docStats)
      setLinkStats(linkStatsData)
      setTagStats(tagStatsData)
      setUsageAnalytics(usageData)
      setGraphAnalytics(graphData)
      setContentQuality(qualityData)

      console.log('数据分析加载完成')
    } catch (error) {
      console.error('加载数据分析失败:', error)
      message.error('加载数据分析失败')
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * 刷新数据
   */
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    await loadAnalyticsData()
    setRefreshing(false)
    message.success('数据已刷新')
  }, [loadAnalyticsData])

  /**
   * 导出报告
   */
  const handleExportReport = useCallback(() => {
    // 这里可以实现导出功能
    message.info('导出功能开发中...')
  }, [])

  /**
   * 组件挂载时加载数据
   */
  useEffect(() => {
    loadAnalyticsData()
  }, [loadAnalyticsData])

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Spin size="large" tip="加载数据分析中..." />
      </div>
    )
  }

  return (
    <div className="h-full p-6 bg-gray-50">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <Title level={2} className="mb-2">
              <DashboardOutlined className="mr-2" />
              数据分析
            </Title>
            <Text type="secondary">
              深入了解您的知识库使用情况和内容质量
            </Text>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={refreshing}
            >
              刷新数据
            </Button>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleExportReport}
            >
              导出报告
            </Button>
          </Space>
        </div>
      </div>

      <Tabs defaultActiveKey="overview" size="large">
        {/* 概览 */}
        <TabPane
          tab={
            <span>
              <DashboardOutlined />
              概览
            </span>
          }
          key="overview"
        >
          <Row gutter={[16, 16]}>
            {/* 文档统计 */}
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="总文档数"
                  value={documentStats?.totalDocuments || 0}
                  prefix={<FileTextOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="总链接数"
                  value={linkStats?.totalLinks || 0}
                  prefix={<LinkOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="总标签数"
                  value={tagStats?.totalTags || 0}
                  prefix={<TagOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card>
                <Statistic
                  title="内容质量分数"
                  value={contentQuality?.qualityScore || 0}
                  suffix="分"
                  prefix={<QualityOutlined />}
                />
                <Progress
                  percent={contentQuality?.qualityScore || 0}
                  showInfo={false}
                  strokeColor={{
                    '0%': '#ff4d4f',
                    '50%': '#faad14',
                    '100%': '#52c41a'
                  }}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} className="mt-4">
            {/* 文档类型分布 */}
            <Col xs={24} lg={12}>
              <Card title="文档类型分布" extra={<PieChartOutlined />}>
                {documentStats ? (
                  <div className="space-y-3">
                    {Object.entries(documentStats.documentsByType).map(([type, count]) => (
                      <div key={type} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Tag color={DOCUMENT_TYPE_COLORS[type as DocumentType]}>
                            {DOCUMENT_TYPE_LABELS[type as DocumentType]}
                          </Tag>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Text>{count}</Text>
                          <Progress
                            percent={Math.round((count / documentStats.totalDocuments) * 100)}
                            size="small"
                            style={{ width: 100 }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Empty description="暂无数据" />
                )}
              </Card>
            </Col>

            {/* 最常用标签 */}
            <Col xs={24} lg={12}>
              <Card title="最常用标签" extra={<TagOutlined />}>
                {tagStats && tagStats.mostUsedTags.length > 0 ? (
                  <div className="space-y-2">
                    {tagStats.mostUsedTags.slice(0, 10).map((tagInfo, index) => (
                      <div key={tagInfo.tag} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Text className="w-6 text-gray-500">#{index + 1}</Text>
                          <Tag>{tagInfo.tag}</Tag>
                        </div>
                        <Text type="secondary">{tagInfo.count} 次</Text>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Empty description="暂无标签数据" />
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 链接分析 */}
        <TabPane
          tab={
            <span>
              <ShareAltOutlined />
              链接分析
            </span>
          }
          key="links"
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="链接统计">
                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="平均链接数"
                      value={linkStats?.averageLinksPerDocument || 0}
                      precision={1}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="孤立文档"
                      value={linkStats?.orphanDocuments.length || 0}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>

            <Col xs={24} lg={12}>
              <Card title="网络分析">
                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="网络密度"
                      value={graphAnalytics?.networkDensity || 0}
                      precision={3}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="聚类系数"
                      value={graphAnalytics?.clusteringCoefficient || 0}
                      precision={3}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} className="mt-4">
            {/* 最多链接的文档 */}
            <Col xs={24} lg={12}>
              <Card title="最多链接的文档">
                {linkStats && linkStats.mostLinkedDocuments.length > 0 ? (
                  <List
                    size="small"
                    dataSource={linkStats.mostLinkedDocuments.slice(0, 10)}
                    renderItem={(item, index) => (
                      <List.Item>
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center">
                            <Text className="w-6 text-gray-500">#{index + 1}</Text>
                            <Tooltip title={item.document.title}>
                              <Text className="truncate max-w-xs">
                                {item.document.title}
                              </Text>
                            </Tooltip>
                          </div>
                          <Tag color="blue">{item.linkCount} 个链接</Tag>
                        </div>
                      </List.Item>
                    )}
                  />
                ) : (
                  <Empty description="暂无链接数据" />
                )}
              </Card>
            </Col>

            {/* 中心性分析 */}
            <Col xs={24} lg={12}>
              <Card title="中心性分析">
                {graphAnalytics && graphAnalytics.centralityScores.length > 0 ? (
                  <List
                    size="small"
                    dataSource={graphAnalytics.centralityScores.slice(0, 10)}
                    renderItem={(item, index) => (
                      <List.Item>
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center">
                            <Text className="w-6 text-gray-500">#{index + 1}</Text>
                            <Tooltip title={item.document.title}>
                              <Text className="truncate max-w-xs">
                                {item.document.title}
                              </Text>
                            </Tooltip>
                          </div>
                          <Tag color="purple">度数: {item.degreeCentrality}</Tag>
                        </div>
                      </List.Item>
                    )}
                  />
                ) : (
                  <Empty description="暂无中心性数据" />
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 内容质量 */}
        <TabPane
          tab={
            <span>
              <QualityOutlined />
              内容质量
            </span>
          }
          key="quality"
        >
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="质量概览">
                <Row gutter={16}>
                  <Col xs={24} sm={6}>
                    <Statistic
                      title="无标签文档"
                      value={contentQuality?.documentsWithoutTags.length || 0}
                      valueStyle={{ color: '#ff4d4f' }}
                    />
                  </Col>
                  <Col xs={24} sm={6}>
                    <Statistic
                      title="无链接文档"
                      value={contentQuality?.documentsWithoutLinks.length || 0}
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Col>
                  <Col xs={24} sm={6}>
                    <Statistic
                      title="短文档"
                      value={contentQuality?.shortDocuments.length || 0}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Col>
                  <Col xs={24} sm={6}>
                    <Statistic
                      title="重复内容组"
                      value={contentQuality?.duplicateContent.length || 0}
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} className="mt-4">
            {/* 需要改进的文档 */}
            <Col xs={24} lg={12}>
              <Card title="需要改进的文档">
                {contentQuality && contentQuality.documentsWithoutTags.length > 0 ? (
                  <List
                    size="small"
                    header={<Text type="secondary">无标签文档 (前10个)</Text>}
                    dataSource={contentQuality.documentsWithoutTags.slice(0, 10)}
                    renderItem={(doc) => (
                      <List.Item>
                        <div className="flex items-center justify-between w-full">
                          <Tooltip title={doc.title}>
                            <Text className="truncate max-w-xs">{doc.title}</Text>
                          </Tooltip>
                          <Tag color={DOCUMENT_TYPE_COLORS[doc.type]}>
                            {DOCUMENT_TYPE_LABELS[doc.type]}
                          </Tag>
                        </div>
                      </List.Item>
                    )}
                  />
                ) : (
                  <Empty description="所有文档都有标签" />
                )}
              </Card>
            </Col>

            {/* 孤立文档 */}
            <Col xs={24} lg={12}>
              <Card title="孤立文档">
                {linkStats && linkStats.orphanDocuments.length > 0 ? (
                  <List
                    size="small"
                    header={<Text type="secondary">无链接文档 (前10个)</Text>}
                    dataSource={linkStats.orphanDocuments.slice(0, 10)}
                    renderItem={(doc) => (
                      <List.Item>
                        <div className="flex items-center justify-between w-full">
                          <Tooltip title={doc.title}>
                            <Text className="truncate max-w-xs">{doc.title}</Text>
                          </Tooltip>
                          <Tag color={DOCUMENT_TYPE_COLORS[doc.type]}>
                            {DOCUMENT_TYPE_LABELS[doc.type]}
                          </Tag>
                        </div>
                      </List.Item>
                    )}
                  />
                ) : (
                  <Empty description="所有文档都有链接" />
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 使用习惯 */}
        <TabPane
          tab={
            <span>
              <ClockCircleOutlined />
              使用习惯
            </span>
          }
          key="usage"
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="文档创建活动">
                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="本周创建"
                      value={documentStats?.documentsCreatedThisWeek || 0}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="本月创建"
                      value={documentStats?.documentsCreatedThisMonth || 0}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>

            <Col xs={24} lg={12}>
              <Card title="今日活动">
                <Statistic
                  title="今日更新文档"
                  value={documentStats?.documentsUpdatedToday || 0}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} className="mt-4">
            {/* 最活跃的文档类型 */}
            <Col span={24}>
              <Card title="文档类型活跃度">
                {usageAnalytics && usageAnalytics.mostActiveDocumentTypes.length > 0 ? (
                  <div className="space-y-3">
                    {usageAnalytics.mostActiveDocumentTypes.map((typeInfo, index) => (
                      <div key={typeInfo.type} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Text className="w-6 text-gray-500">#{index + 1}</Text>
                          <Tag color={DOCUMENT_TYPE_COLORS[typeInfo.type]}>
                            {DOCUMENT_TYPE_LABELS[typeInfo.type]}
                          </Tag>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Text>{typeInfo.activity} 次活动</Text>
                          <Progress
                            percent={Math.round((typeInfo.activity / usageAnalytics.mostActiveDocumentTypes[0].activity) * 100)}
                            size="small"
                            style={{ width: 100 }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Empty description="暂无使用数据" />
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  )
}

export default AnalyticsPage
