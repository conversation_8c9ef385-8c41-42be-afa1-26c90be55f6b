import React, { useState, useEffect, useCallback } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { message, Spin, Layout } from 'antd'
import TextEditor from '@components/editor/TextEditor'
import LinkCreator from '@components/link/LinkCreator'
import BacklinkPanel from '@components/link/BacklinkPanel'
import CrossFormatLinkPanel from '@components/crossFormat/CrossFormatLinkPanel'
import { TextDocument, DocumentType, TextContent, BaseDocument } from '@/types'
import { documentDAO } from '@services/database/documentDAO'
import { linkDAO } from '@services/database/linkDAO'
import { generateUUID } from '@utils/index'

const { Sider, Content } = Layout

/**
 * 文本编辑器页面组件
 * 提供完整的Markdown文档编辑功能
 */
const TextEditorPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  const [document, setDocument] = useState<TextDocument | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [linkCreatorVisible, setLinkCreatorVisible] = useState(false)
  const [allDocuments, setAllDocuments] = useState<BaseDocument[]>([])
  const [documentLinks, setDocumentLinks] = useState<any[]>([])
  const [sidebarVisible, setSidebarVisible] = useState(true)

  /**
   * 加载文档
   */
  const loadDocument = useCallback(async (documentId: string) => {
    try {
      setLoading(true)
      const doc = await documentDAO.getById(documentId)

      if (doc && doc.type === DocumentType.TEXT) {
        setDocument(doc as TextDocument)
        console.log('文档加载成功:', doc.title)

        // 加载文档链接
        await loadDocumentLinks(documentId)
      } else {
        message.error('文档不存在或类型不匹配')
        navigate('/')
      }
    } catch (error) {
      console.error('加载文档失败:', error)
      message.error('加载文档失败')
      navigate('/')
    } finally {
      setLoading(false)
    }
  }, [navigate])

  /**
   * 加载所有文档（用于链接创建）
   */
  const loadAllDocuments = useCallback(async () => {
    try {
      const docs = await documentDAO.getAll()
      setAllDocuments(docs)
    } catch (error) {
      console.error('加载文档列表失败:', error)
    }
  }, [])

  /**
   * 加载文档链接
   */
  const loadDocumentLinks = useCallback(async (documentId: string) => {
    try {
      const { incoming, outgoing } = await linkDAO.getDocumentLinks(documentId)
      setDocumentLinks([...incoming, ...outgoing])
    } catch (error) {
      console.error('加载文档链接失败:', error)
    }
  }, [])

  /**
   * 创建新文档
   */
  const createNewDocument = useCallback(async () => {
    try {
      setLoading(true)

      const newDocument: Omit<TextDocument, 'id' | 'createdAt' | 'updatedAt'> = {
        title: '新建文档',
        type: DocumentType.TEXT,
        content: {
          markdown: '',
          outline: []
        } as TextContent,
        metadata: {
          version: 1,
          size: 0,
          checksum: '',
          wordCount: 0,
          readingTime: 0
        },
        tags: [],
        links: []
      }

      const createdDoc = await documentDAO.create(newDocument)
      setDocument(createdDoc as TextDocument)

      // 更新URL
      navigate(`/editor/${createdDoc.id}`, { replace: true })

      console.log('新文档创建成功:', createdDoc.id)
    } catch (error) {
      console.error('创建文档失败:', error)
      message.error('创建文档失败')
    } finally {
      setLoading(false)
    }
  }, [navigate])

  /**
   * 保存文档
   */
  const handleSave = useCallback(async (updatedDocument: TextDocument) => {
    try {
      setSaving(true)

      // 更新元数据
      const content = updatedDocument.content.markdown
      const wordCount = content.length
      const readingTime = Math.ceil(wordCount / 200) // 假设每分钟200字

      const docToSave: TextDocument = {
        ...updatedDocument,
        metadata: {
          ...updatedDocument.metadata,
          size: new Blob([content]).size,
          wordCount,
          readingTime,
          checksum: generateChecksum(content)
        }
      }

      const savedDoc = await documentDAO.update(docToSave.id, docToSave)
      setDocument(savedDoc as TextDocument)

      console.log('文档保存成功:', savedDoc.title)
    } catch (error) {
      console.error('保存文档失败:', error)
      message.error('保存文档失败')
      throw error // 重新抛出错误，让编辑器组件处理
    } finally {
      setSaving(false)
    }
  }, [])

  /**
   * 文档变化处理
   */
  const handleDocumentChange = useCallback((changes: Partial<TextDocument>) => {
    if (document) {
      setDocument(prev => prev ? { ...prev, ...changes } : null)
    }
  }, [document])

  /**
   * 创建链接
   */
  const handleCreateLink = useCallback(async (linkData: any) => {
    try {
      await linkDAO.create(linkData)
      message.success('链接创建成功')

      // 重新加载链接
      if (document) {
        await loadDocumentLinks(document.id)
      }

      setLinkCreatorVisible(false)
    } catch (error) {
      console.error('创建链接失败:', error)
      message.error('创建链接失败')
    }
  }, [document, loadDocumentLinks])

  /**
   * 删除链接
   */
  const handleDeleteLink = useCallback(async (linkId: string) => {
    try {
      await linkDAO.delete(linkId)
      message.success('链接删除成功')

      // 重新加载链接
      if (document) {
        await loadDocumentLinks(document.id)
      }
    } catch (error) {
      console.error('删除链接失败:', error)
      message.error('删除链接失败')
    }
  }, [document, loadDocumentLinks])

  /**
   * 打开链接创建器
   */
  const handleOpenLinkCreator = useCallback(() => {
    setLinkCreatorVisible(true)
    loadAllDocuments() // 确保有最新的文档列表
  }, [loadAllDocuments])

  /**
   * 处理反向链接文档点击
   */
  const handleBacklinkDocumentClick = useCallback((targetDocument: BaseDocument) => {
    // 导航到目标文档
    navigate(`/editor/${targetDocument.id}`)
  }, [navigate])

  /**
   * 处理反向链接删除
   */
  const handleBacklinkRemove = useCallback(async (link: any) => {
    try {
      await linkDAO.delete(link.id)
      message.success('链接删除成功')

      // 重新加载链接
      if (document) {
        await loadDocumentLinks(document.id)
      }
    } catch (error) {
      console.error('删除反向链接失败:', error)
      message.error('删除链接失败')
    }
  }, [document, loadDocumentLinks])

  /**
   * 切换侧边栏显示
   */
  const toggleSidebar = useCallback(() => {
    setSidebarVisible(prev => !prev)
  }, [])

  /**
   * 初始化页面
   */
  useEffect(() => {
    if (id) {
      // 加载现有文档
      loadDocument(id)
    } else {
      // 创建新文档
      createNewDocument()
    }

    // 加载所有文档用于链接创建
    loadAllDocuments()
  }, [id, loadDocument, createNewDocument, loadAllDocuments])

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Spin size="large" tip="加载文档中..." />
      </div>
    )
  }

  if (!document) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-500 mb-4">文档加载失败</div>
          <button
            onClick={() => navigate('/')}
            className="text-blue-500 hover:text-blue-600"
          >
            返回首页
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full">
      <Layout className="h-full">
        {/* 主编辑区域 */}
        <Content className="flex-1">
          <TextEditor
            document={document}
            onSave={handleSave}
            onDocumentChange={handleDocumentChange}
            onOpenLinkCreator={handleOpenLinkCreator}
            onToggleSidebar={toggleSidebar}
            autoSave={true}
            autoSaveDelay={2000}
            height="100%"
          />
        </Content>

        {/* 右侧边栏 - 反向链接面板 */}
        {sidebarVisible && (
          <Sider
            width={320}
            theme="light"
            className="border-l border-gray-200"
            style={{
              height: '100%',
              overflow: 'auto',
              backgroundColor: '#fafafa'
            }}
          >
            <div className="p-4 h-full flex flex-col gap-4">
              {/* 反向链接面板 */}
              <div className="flex-1">
                <BacklinkPanel
                  documentId={document.id}
                  onDocumentClick={handleBacklinkDocumentClick}
                  onLinkRemove={handleBacklinkRemove}
                  className="h-full"
                />
              </div>

              {/* 跨格式关联面板 */}
              <div className="flex-1">
                <CrossFormatLinkPanel
                  document={document}
                  className="h-full"
                />
              </div>
            </div>
          </Sider>
        )}
      </Layout>

      {/* 链接创建器 */}
      {document && (
        <LinkCreator
          currentDocument={document}
          documents={allDocuments}
          existingLinks={documentLinks}
          visible={linkCreatorVisible}
          onClose={() => setLinkCreatorVisible(false)}
          onCreateLink={handleCreateLink}
          onDeleteLink={handleDeleteLink}
        />
      )}
    </div>
  )
}

/**
 * 生成内容校验和
 */
function generateChecksum(content: string): string {
  // 简单的校验和算法
  let hash = 0
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash).toString(16)
}

export default TextEditorPage
