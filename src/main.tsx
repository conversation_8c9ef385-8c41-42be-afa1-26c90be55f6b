import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ConfigProvider, App as AntdApp } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

import App from './App'
import { ThemeProvider, useTheme } from '@hooks/useTheme'
import './index.css'

// 配置dayjs中文语言
dayjs.locale('zh-cn')

// 创建React Query客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5分钟
    },
  },
})

/**
 * 应用包装组件
 * 使用主题上下文提供主题配置
 */
const AppWrapper: React.FC = () => {
  const { antdThemeConfig } = useTheme()

  return (
    <ConfigProvider
      locale={zhCN}
      theme={antdThemeConfig}
    >
      <AntdApp>
        <App />
      </AntdApp>
    </ConfigProvider>
  )
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <AppWrapper />
        </ThemeProvider>
      </QueryClientProvider>
    </BrowserRouter>
  </React.StrictMode>,
)
