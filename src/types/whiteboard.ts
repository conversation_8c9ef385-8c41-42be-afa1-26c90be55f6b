/**
 * 白板相关类型定义
 */

/**
 * 白板元素基础类型
 */
export enum WhiteboardElementType {
  PATH = 'path',         // 自由绘制路径
  RECTANGLE = 'rectangle', // 矩形
  CIRCLE = 'circle',     // 圆形
  LINE = 'line',         // 直线
  TEXT = 'text',         // 文本
  IMAGE = 'image'        // 图片
}

/**
 * 坐标点接口
 */
export interface Point {
  x: number
  y: number
}

/**
 * 白板元素基础接口
 */
export interface BaseWhiteboardElement {
  id: string
  type: WhiteboardElementType
  x: number
  y: number
  width: number
  height: number
  rotation: number
  opacity: number
  visible: boolean
  locked: boolean
  zIndex: number
  createdAt: Date
  updatedAt: Date
}

/**
 * 样式属性接口
 */
export interface StyleProperties {
  strokeColor: string
  strokeWidth: number
  strokeDashArray?: number[]
  fillColor?: string
  fontSize?: number
  fontFamily?: string
  fontWeight?: string
  textAlign?: 'left' | 'center' | 'right'
}

/**
 * 路径元素（自由绘制）
 */
export interface PathElement extends BaseWhiteboardElement {
  type: WhiteboardElementType.PATH
  points: Point[]
  style: StyleProperties
}

/**
 * 矩形元素
 */
export interface RectangleElement extends BaseWhiteboardElement {
  type: WhiteboardElementType.RECTANGLE
  style: StyleProperties
}

/**
 * 圆形元素
 */
export interface CircleElement extends BaseWhiteboardElement {
  type: WhiteboardElementType.CIRCLE
  radius: number
  style: StyleProperties
}

/**
 * 直线元素
 */
export interface LineElement extends BaseWhiteboardElement {
  type: WhiteboardElementType.LINE
  startPoint: Point
  endPoint: Point
  style: StyleProperties
}

/**
 * 文本元素
 */
export interface TextElement extends BaseWhiteboardElement {
  type: WhiteboardElementType.TEXT
  text: string
  style: StyleProperties
}

/**
 * 图片元素
 */
export interface ImageElement extends BaseWhiteboardElement {
  type: WhiteboardElementType.IMAGE
  src: string
  originalWidth: number
  originalHeight: number
}

/**
 * 白板元素联合类型
 */
export type WhiteboardElement = 
  | PathElement 
  | RectangleElement 
  | CircleElement 
  | LineElement 
  | TextElement 
  | ImageElement

/**
 * 白板内容接口
 */
export interface WhiteboardContent {
  elements: WhiteboardElement[]
  background: {
    color: string
    image?: string
  }
  viewport: {
    x: number
    y: number
    zoom: number
  }
  grid: {
    enabled: boolean
    size: number
    color: string
  }
}

/**
 * 白板文档接口
 */
export interface WhiteboardDocument {
  id: string
  title: string
  content: WhiteboardContent
  thumbnail?: string
  createdAt: Date
  updatedAt: Date
  tags: string[]
  metadata: {
    version: string
    canvasWidth: number
    canvasHeight: number
    elementCount: number
  }
}

/**
 * 白板工具类型
 */
export enum WhiteboardTool {
  SELECT = 'select',     // 选择工具
  PEN = 'pen',          // 画笔
  ERASER = 'eraser',    // 橡皮擦
  RECTANGLE = 'rectangle', // 矩形
  CIRCLE = 'circle',    // 圆形
  LINE = 'line',        // 直线
  TEXT = 'text',        // 文本
  IMAGE = 'image',      // 图片
  HAND = 'hand'         // 手型工具（平移）
}

/**
 * 白板状态接口
 */
export interface WhiteboardState {
  currentTool: WhiteboardTool
  selectedElements: string[]
  clipboard: WhiteboardElement[]
  isDrawing: boolean
  isDragging: boolean
  dragStartPoint?: Point
  currentElement?: Partial<WhiteboardElement>
  history: {
    past: WhiteboardContent[]
    present: WhiteboardContent
    future: WhiteboardContent[]
  }
}

/**
 * 白板操作类型
 */
export enum WhiteboardActionType {
  ADD_ELEMENT = 'ADD_ELEMENT',
  UPDATE_ELEMENT = 'UPDATE_ELEMENT',
  DELETE_ELEMENT = 'DELETE_ELEMENT',
  SELECT_ELEMENT = 'SELECT_ELEMENT',
  DESELECT_ALL = 'DESELECT_ALL',
  MOVE_ELEMENT = 'MOVE_ELEMENT',
  RESIZE_ELEMENT = 'RESIZE_ELEMENT',
  CHANGE_TOOL = 'CHANGE_TOOL',
  UNDO = 'UNDO',
  REDO = 'REDO',
  CLEAR_CANVAS = 'CLEAR_CANVAS',
  SET_VIEWPORT = 'SET_VIEWPORT',
  SET_BACKGROUND = 'SET_BACKGROUND'
}

/**
 * 白板操作接口
 */
export interface WhiteboardAction {
  type: WhiteboardActionType
  payload?: any
  timestamp: number
}

/**
 * 白板配置接口
 */
export interface WhiteboardConfig {
  canvasWidth: number
  canvasHeight: number
  backgroundColor: string
  gridEnabled: boolean
  gridSize: number
  gridColor: string
  snapToGrid: boolean
  showRulers: boolean
  showMinimap: boolean
  autoSave: boolean
  autoSaveInterval: number
}

/**
 * 白板导出选项
 */
export interface WhiteboardExportOptions {
  format: 'png' | 'jpg' | 'svg' | 'pdf'
  quality?: number
  scale?: number
  includeBackground?: boolean
  selectedOnly?: boolean
  transparent?: boolean
}

/**
 * 白板导入选项
 */
export interface WhiteboardImportOptions {
  replaceContent?: boolean
  position?: Point
  scale?: number
}

/**
 * 白板事件类型
 */
export enum WhiteboardEventType {
  ELEMENT_ADDED = 'element_added',
  ELEMENT_UPDATED = 'element_updated',
  ELEMENT_DELETED = 'element_deleted',
  SELECTION_CHANGED = 'selection_changed',
  VIEWPORT_CHANGED = 'viewport_changed',
  TOOL_CHANGED = 'tool_changed',
  CANVAS_CLEARED = 'canvas_cleared'
}

/**
 * 白板事件接口
 */
export interface WhiteboardEvent {
  type: WhiteboardEventType
  data: any
  timestamp: number
}

/**
 * 白板快捷键配置
 */
export interface WhiteboardKeyboardShortcuts {
  undo: string[]
  redo: string[]
  copy: string[]
  paste: string[]
  delete: string[]
  selectAll: string[]
  deselect: string[]
  zoomIn: string[]
  zoomOut: string[]
  zoomFit: string[]
  save: string[]
}

/**
 * 默认白板配置
 */
export const DEFAULT_WHITEBOARD_CONFIG: WhiteboardConfig = {
  canvasWidth: 1920,
  canvasHeight: 1080,
  backgroundColor: '#ffffff',
  gridEnabled: false,
  gridSize: 20,
  gridColor: '#e0e0e0',
  snapToGrid: false,
  showRulers: false,
  showMinimap: false,
  autoSave: true,
  autoSaveInterval: 30000 // 30秒
}

/**
 * 默认样式属性
 */
export const DEFAULT_STYLE_PROPERTIES: StyleProperties = {
  strokeColor: '#000000',
  strokeWidth: 2,
  fillColor: 'transparent',
  fontSize: 16,
  fontFamily: 'Arial, sans-serif',
  fontWeight: 'normal',
  textAlign: 'left'
}

/**
 * 默认快捷键配置
 */
export const DEFAULT_KEYBOARD_SHORTCUTS: WhiteboardKeyboardShortcuts = {
  undo: ['Ctrl+Z', 'Cmd+Z'],
  redo: ['Ctrl+Y', 'Cmd+Y', 'Ctrl+Shift+Z', 'Cmd+Shift+Z'],
  copy: ['Ctrl+C', 'Cmd+C'],
  paste: ['Ctrl+V', 'Cmd+V'],
  delete: ['Delete', 'Backspace'],
  selectAll: ['Ctrl+A', 'Cmd+A'],
  deselect: ['Escape'],
  zoomIn: ['Ctrl+=', 'Cmd+='],
  zoomOut: ['Ctrl+-', 'Cmd+-'],
  zoomFit: ['Ctrl+0', 'Cmd+0'],
  save: ['Ctrl+S', 'Cmd+S']
}
