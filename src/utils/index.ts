/**
 * 通用工具函数库
 * 提供应用中常用的工具函数
 */

/**
 * 生成唯一ID
 * 使用时间戳和随机数生成唯一标识符
 */
export function generateId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 生成UUID v4
 * 标准的UUID格式
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 防抖函数
 * 在指定时间内只执行最后一次调用
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * 节流函数
 * 在指定时间内最多执行一次
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}

/**
 * 深拷贝对象
 * 使用JSON方法进行深拷贝（注意：不支持函数、Symbol等）
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  
  return obj
}

/**
 * 格式化文件大小
 * 将字节数转换为可读的文件大小格式
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化日期
 * 将日期对象格式化为指定格式的字符串
 */
export function formatDate(date: Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year.toString())
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 相对时间格式化
 * 将日期转换为相对时间描述
 */
export function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const months = Math.floor(days / 30)
  const years = Math.floor(days / 365)
  
  if (years > 0) return `${years}年前`
  if (months > 0) return `${months}个月前`
  if (days > 0) return `${days}天前`
  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  return '刚刚'
}

/**
 * 计算字符串的字节长度
 * 中文字符按2个字节计算
 */
export function getStringByteLength(str: string): number {
  let length = 0
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i)
    if (charCode >= 0x4e00 && charCode <= 0x9fff) {
      // 中文字符
      length += 2
    } else {
      length += 1
    }
  }
  return length
}

/**
 * 计算阅读时间
 * 基于字数估算阅读时间（中文200字/分钟，英文250词/分钟）
 */
export function calculateReadingTime(text: string): number {
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length
  const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').split(/\s+/).filter(word => word.length > 0).length
  
  const chineseTime = chineseChars / 200 // 中文200字/分钟
  const englishTime = englishWords / 250 // 英文250词/分钟
  
  return Math.ceil(chineseTime + englishTime)
}

/**
 * 截取字符串
 * 按指定长度截取字符串，支持中文
 */
export function truncateString(str: string, maxLength: number, suffix: string = '...'): string {
  if (getStringByteLength(str) <= maxLength) {
    return str
  }
  
  let length = 0
  let result = ''
  
  for (let i = 0; i < str.length; i++) {
    const char = str[i]
    if (!char) continue
    const charLength = char.charCodeAt(0) >= 0x4e00 && char.charCodeAt(0) <= 0x9fff ? 2 : 1
    
    if (length + charLength > maxLength - suffix.length) {
      break
    }
    
    result += char
    length += charLength
  }
  
  return result + suffix
}

/**
 * 验证邮箱格式
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证URL格式
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 下载文件
 * 创建下载链接并触发下载
 */
export function downloadFile(content: string | Blob, filename: string, mimeType?: string): void {
  const blob = content instanceof Blob ? content : new Blob([content], { type: mimeType || 'text/plain' })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  URL.revokeObjectURL(url)
}

/**
 * 复制文本到剪贴板
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      return true
    } else {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      const result = document.execCommand('copy')
      document.body.removeChild(textArea)
      return result
    }
  } catch (error) {
    console.error('复制到剪贴板失败:', error)
    return false
  }
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(filename: string): string {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
}

/**
 * 检查是否为移动设备
 */
export function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

/**
 * 高亮文本中的关键词
 * @param text 原始文本
 * @param query 搜索关键词
 * @param highlightClass 高亮样式类名
 * @returns 高亮后的HTML字符串
 */
export function highlightText(
  text: string,
  query: string,
  highlightClass = 'search-highlight'
): string {
  if (!query || !text) return text

  // 转义特殊字符
  const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

  // 创建正则表达式，忽略大小写
  const regex = new RegExp(`(${escapedQuery})`, 'gi')

  // 替换匹配的文本
  return text.replace(regex, `<mark class="${highlightClass}">$1</mark>`)
}

/**
 * 提取文本摘要
 * @param text 原始文本
 * @param query 搜索关键词
 * @param maxLength 最大长度
 * @returns 包含关键词的文本摘要
 */
export function extractTextSummary(
  text: string,
  query: string,
  maxLength: number = 200
): string {
  if (!text || !query) return truncateString(text, maxLength)

  // 查找关键词位置
  const lowerText = text.toLowerCase()
  const lowerQuery = query.toLowerCase()
  const index = lowerText.indexOf(lowerQuery)

  if (index === -1) {
    return truncateString(text, maxLength)
  }

  // 计算摘要的起始位置
  const start = Math.max(0, index - Math.floor((maxLength - query.length) / 2))
  const end = Math.min(text.length, start + maxLength)

  let summary = text.slice(start, end)

  // 添加省略号
  if (start > 0) summary = '...' + summary
  if (end < text.length) summary = summary + '...'

  return summary
}

/**
 * 本地存储工具
 */
export const storage = {
  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue || null
    } catch {
      return defaultValue || null
    }
  },

  set<T>(key: string, value: T): void {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('保存到本地存储失败:', error)
    }
  },

  remove(key: string): void {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('从本地存储删除失败:', error)
    }
  },

  clear(): void {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('清空本地存储失败:', error)
    }
  }
}
