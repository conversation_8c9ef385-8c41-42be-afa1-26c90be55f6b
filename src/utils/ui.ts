/**
 * UI工具函数库
 * 提供用户界面相关的工具函数和常量
 */

/**
 * 动画持续时间常量
 */
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  VERY_SLOW: 1000
} as const

/**
 * 断点常量
 */
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1440,
  LARGE_DESKTOP: 1920
} as const

/**
 * Z-Index 层级常量
 */
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  NOTIFICATION: 1080
} as const

/**
 * 颜色主题常量
 */
export const THEME_COLORS = {
  PRIMARY: '#1890ff',
  SUCCESS: '#52c41a',
  WARNING: '#fa8c16',
  ERROR: '#ff4d4f',
  INFO: '#1890ff',
  TEXT_PRIMARY: '#262626',
  TEXT_SECONDARY: '#8c8c8c',
  TEXT_DISABLED: '#bfbfbf',
  BORDER: '#d9d9d9',
  BACKGROUND: '#fafafa'
} as const

/**
 * 文档类型颜色映射
 */
export const DOCUMENT_TYPE_COLORS = {
  TEXT: '#1890ff',
  WHITEBOARD: '#52c41a',
  MINDMAP: '#722ed1',
  KANBAN: '#fa8c16'
} as const

/**
 * 组合CSS类名
 * @param classes - 类名数组
 * @returns 组合后的类名字符串
 */
export const classNames = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ')
}

/**
 * 条件性CSS类名
 * @param condition - 条件
 * @param trueClass - 条件为真时的类名
 * @param falseClass - 条件为假时的类名
 * @returns 条件性类名
 */
export const conditionalClass = (
  condition: boolean,
  trueClass: string,
  falseClass: string = ''
): string => {
  return condition ? trueClass : falseClass
}

/**
 * 获取响应式类名
 * @param base - 基础类名
 * @param mobile - 移动端类名
 * @param tablet - 平板端类名
 * @param desktop - 桌面端类名
 * @returns 响应式类名字符串
 */
export const responsiveClass = (
  base: string,
  mobile?: string,
  tablet?: string,
  desktop?: string
): string => {
  const classes = [base]
  
  if (mobile) classes.push(`sm:${mobile}`)
  if (tablet) classes.push(`md:${tablet}`)
  if (desktop) classes.push(`lg:${desktop}`)
  
  return classes.join(' ')
}

/**
 * 获取文档类型的显示信息
 * @param type - 文档类型
 * @returns 显示信息对象
 */
export const getDocumentTypeInfo = (type: string) => {
  const typeMap = {
    TEXT: {
      label: '文本文档',
      color: DOCUMENT_TYPE_COLORS.TEXT,
      icon: 'FileTextOutlined'
    },
    WHITEBOARD: {
      label: '白板',
      color: DOCUMENT_TYPE_COLORS.WHITEBOARD,
      icon: 'BgColorsOutlined'
    },
    MINDMAP: {
      label: '思维导图',
      color: DOCUMENT_TYPE_COLORS.MINDMAP,
      icon: 'NodeIndexOutlined'
    },
    KANBAN: {
      label: '看板',
      color: DOCUMENT_TYPE_COLORS.KANBAN,
      icon: 'ProjectOutlined'
    }
  }
  
  return typeMap[type as keyof typeof typeMap] || typeMap.TEXT
}

/**
 * 获取文件大小的人类可读格式
 * @param bytes - 字节数
 * @returns 格式化的文件大小字符串
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

/**
 * 获取颜色的透明度变体
 * @param color - 原始颜色（十六进制）
 * @param opacity - 透明度（0-1）
 * @returns RGBA颜色字符串
 */
export const getColorWithOpacity = (color: string, opacity: number): string => {
  // 移除 # 符号
  const hex = color.replace('#', '')
  
  // 解析RGB值
  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)
  
  return `rgba(${r}, ${g}, ${b}, ${opacity})`
}

/**
 * 生成渐变背景CSS
 * @param startColor - 起始颜色
 * @param endColor - 结束颜色
 * @param direction - 渐变方向
 * @returns CSS渐变字符串
 */
export const generateGradient = (
  startColor: string,
  endColor: string,
  direction: 'to-right' | 'to-left' | 'to-bottom' | 'to-top' | 'to-br' | 'to-bl' = 'to-right'
): string => {
  const directionMap = {
    'to-right': 'to right',
    'to-left': 'to left',
    'to-bottom': 'to bottom',
    'to-top': 'to top',
    'to-br': 'to bottom right',
    'to-bl': 'to bottom left'
  }
  
  return `linear-gradient(${directionMap[direction]}, ${startColor}, ${endColor})`
}

/**
 * 检查是否为暗色主题
 * @returns 是否为暗色主题
 */
export const isDarkTheme = (): boolean => {
  if (typeof document === 'undefined') return false
  return document.documentElement.classList.contains('dark')
}

/**
 * 获取当前主题的颜色
 * @param lightColor - 浅色主题颜色
 * @param darkColor - 深色主题颜色
 * @returns 当前主题对应的颜色
 */
export const getThemeColor = (lightColor: string, darkColor: string): string => {
  return isDarkTheme() ? darkColor : lightColor
}

/**
 * 平滑滚动到指定元素
 * @param elementId - 元素ID
 * @param offset - 偏移量
 */
export const smoothScrollTo = (elementId: string, offset: number = 0): void => {
  const element = document.getElementById(elementId)
  if (!element) return
  
  const targetPosition = element.offsetTop - offset
  
  window.scrollTo({
    top: targetPosition,
    behavior: 'smooth'
  })
}

/**
 * 复制文本到剪贴板
 * @param text - 要复制的文本
 * @returns Promise<boolean> 是否复制成功
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      return true
    } else {
      // 回退方案
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      const success = document.execCommand('copy')
      document.body.removeChild(textArea)
      return success
    }
  } catch (error) {
    console.error('复制到剪贴板失败:', error)
    return false
  }
}

/**
 * 获取元素的边界矩形信息
 * @param element - DOM元素
 * @returns 边界矩形信息
 */
export const getElementBounds = (element: HTMLElement) => {
  const rect = element.getBoundingClientRect()
  return {
    top: rect.top + window.scrollY,
    left: rect.left + window.scrollX,
    width: rect.width,
    height: rect.height,
    bottom: rect.bottom + window.scrollY,
    right: rect.right + window.scrollX
  }
}

/**
 * 检查元素是否在视口中
 * @param element - DOM元素
 * @param threshold - 阈值（0-1）
 * @returns 是否在视口中
 */
export const isElementInViewport = (element: HTMLElement, threshold: number = 0): boolean => {
  const rect = element.getBoundingClientRect()
  const windowHeight = window.innerHeight || document.documentElement.clientHeight
  const windowWidth = window.innerWidth || document.documentElement.clientWidth
  
  const verticalThreshold = windowHeight * threshold
  const horizontalThreshold = windowWidth * threshold
  
  return (
    rect.top >= -verticalThreshold &&
    rect.left >= -horizontalThreshold &&
    rect.bottom <= windowHeight + verticalThreshold &&
    rect.right <= windowWidth + horizontalThreshold
  )
}

/**
 * 防抖函数（UI专用）
 * @param func - 要防抖的函数
 * @param delay - 延迟时间
 * @returns 防抖后的函数
 */
export const debounceUI = <T extends (...args: any[]) => any>(
  func: T,
  delay: number = ANIMATION_DURATION.NORMAL
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

/**
 * 节流函数（UI专用）
 * @param func - 要节流的函数
 * @param delay - 延迟时间
 * @returns 节流后的函数
 */
export const throttleUI = <T extends (...args: any[]) => any>(
  func: T,
  delay: number = ANIMATION_DURATION.FAST
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}
