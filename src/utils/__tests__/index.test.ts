/**
 * 工具函数单元测试
 */

import {
  generateId,
  generateUUID,
  debounce,
  throttle,
  deepClone,
  formatFileSize,
  formatDate,
  formatRelativeTime,
  getStringByteLength,
  calculateReadingTime,
  truncateString,
  isValidEmail,
  isValidUrl,
  copyToClipboard,
  getFileExtension,
  isMobileDevice,
  storage,
  highlightText,
  extractTextSummary
} from '../index'

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

// Mock navigator.clipboard
const mockClipboard = {
  writeText: jest.fn()
}

Object.defineProperty(navigator, 'clipboard', {
  value: mockClipboard
})

// Mock navigator.userAgent
Object.defineProperty(navigator, 'userAgent', {
  value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  writable: true
})

describe('工具函数测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('ID生成函数', () => {
    describe('generateId', () => {
      it('应该生成唯一的ID', () => {
        const id1 = generateId()
        const id2 = generateId()
        
        expect(id1).not.toBe(id2)
        expect(typeof id1).toBe('string')
        expect(id1.length).toBeGreaterThan(0)
      })

      it('生成的ID应该包含时间戳', () => {
        const id = generateId()
        const timestamp = id.split('-')[0]
        
        expect(Number(timestamp)).toBeCloseTo(Date.now(), -3)
      })
    })

    describe('generateUUID', () => {
      it('应该生成符合UUID v4格式的字符串', () => {
        const uuid = generateUUID()
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
        
        expect(uuid).toMatch(uuidRegex)
      })

      it('应该生成唯一的UUID', () => {
        const uuid1 = generateUUID()
        const uuid2 = generateUUID()
        
        expect(uuid1).not.toBe(uuid2)
      })
    })
  })

  describe('函数式工具', () => {
    describe('debounce', () => {
      it('应该延迟执行函数', (done) => {
        const mockFn = jest.fn()
        const debouncedFn = debounce(mockFn, 100)
        
        debouncedFn()
        expect(mockFn).not.toHaveBeenCalled()
        
        setTimeout(() => {
          expect(mockFn).toHaveBeenCalledTimes(1)
          done()
        }, 150)
      })

      it('应该在多次调用时只执行最后一次', (done) => {
        const mockFn = jest.fn()
        const debouncedFn = debounce(mockFn, 100)
        
        debouncedFn()
        debouncedFn()
        debouncedFn()
        
        setTimeout(() => {
          expect(mockFn).toHaveBeenCalledTimes(1)
          done()
        }, 150)
      })
    })

    describe('throttle', () => {
      it('应该限制函数执行频率', (done) => {
        const mockFn = jest.fn()
        const throttledFn = throttle(mockFn, 100)
        
        throttledFn()
        throttledFn()
        throttledFn()
        
        expect(mockFn).toHaveBeenCalledTimes(1)
        
        setTimeout(() => {
          throttledFn()
          expect(mockFn).toHaveBeenCalledTimes(2)
          done()
        }, 150)
      })
    })
  })

  describe('对象操作', () => {
    describe('deepClone', () => {
      it('应该深拷贝对象', () => {
        const original = {
          a: 1,
          b: {
            c: 2,
            d: [3, 4, { e: 5 }]
          }
        }
        
        const cloned = deepClone(original)
        
        expect(cloned).toEqual(original)
        expect(cloned).not.toBe(original)
        expect(cloned.b).not.toBe(original.b)
        expect(cloned.b.d).not.toBe(original.b.d)
      })

      it('应该处理日期对象', () => {
        const date = new Date('2024-01-01')
        const cloned = deepClone(date)
        
        expect(cloned).toEqual(date)
        expect(cloned).not.toBe(date)
      })

      it('应该处理数组', () => {
        const array = [1, 2, { a: 3 }]
        const cloned = deepClone(array)
        
        expect(cloned).toEqual(array)
        expect(cloned).not.toBe(array)
        expect(cloned[2]).not.toBe(array[2])
      })

      it('应该处理基本类型', () => {
        expect(deepClone(null)).toBe(null)
        expect(deepClone(undefined)).toBe(undefined)
        expect(deepClone(42)).toBe(42)
        expect(deepClone('string')).toBe('string')
        expect(deepClone(true)).toBe(true)
      })
    })
  })

  describe('格式化函数', () => {
    describe('formatFileSize', () => {
      it('应该正确格式化文件大小', () => {
        expect(formatFileSize(0)).toBe('0 B')
        expect(formatFileSize(1024)).toBe('1 KB')
        expect(formatFileSize(1048576)).toBe('1 MB')
        expect(formatFileSize(1073741824)).toBe('1 GB')
        expect(formatFileSize(1536)).toBe('1.5 KB')
      })
    })

    describe('formatDate', () => {
      it('应该使用默认格式格式化日期', () => {
        const date = new Date('2024-01-01T12:30:45')
        const formatted = formatDate(date)
        
        expect(formatted).toBe('2024-01-01 12:30:45')
      })

      it('应该支持自定义格式', () => {
        const date = new Date('2024-01-01T12:30:45')
        const formatted = formatDate(date, 'YYYY/MM/DD')
        
        expect(formatted).toBe('2024/01/01')
      })
    })

    describe('formatRelativeTime', () => {
      it('应该正确格式化相对时间', () => {
        const now = new Date()
        
        // 刚刚
        const justNow = new Date(now.getTime() - 30 * 1000)
        expect(formatRelativeTime(justNow)).toBe('刚刚')
        
        // 分钟前
        const minutesAgo = new Date(now.getTime() - 5 * 60 * 1000)
        expect(formatRelativeTime(minutesAgo)).toBe('5分钟前')
        
        // 小时前
        const hoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000)
        expect(formatRelativeTime(hoursAgo)).toBe('2小时前')
        
        // 天前
        const daysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)
        expect(formatRelativeTime(daysAgo)).toBe('3天前')
      })
    })
  })

  describe('字符串处理', () => {
    describe('getStringByteLength', () => {
      it('应该正确计算字符串字节长度', () => {
        expect(getStringByteLength('hello')).toBe(5)
        expect(getStringByteLength('你好')).toBe(4) // 中文字符按2字节计算
        expect(getStringByteLength('hello你好')).toBe(9)
      })
    })

    describe('calculateReadingTime', () => {
      it('应该正确计算阅读时间', () => {
        const chineseText = '这是一个测试文本。'.repeat(100) // 约500个中文字符
        const englishText = 'This is a test text. '.repeat(100) // 约500个英文单词
        
        expect(calculateReadingTime(chineseText)).toBe(3) // 500字 / 200字/分钟 = 2.5，向上取整为3
        expect(calculateReadingTime(englishText)).toBe(2) // 500词 / 250词/分钟 = 2
      })
    })

    describe('truncateString', () => {
      it('应该正确截取字符串', () => {
        const text = 'hello world'
        expect(truncateString(text, 5)).toBe('he...')
        expect(truncateString(text, 20)).toBe(text) // 不需要截取
      })

      it('应该支持自定义后缀', () => {
        const text = 'hello world'
        expect(truncateString(text, 5, '---')).toBe('h---')
      })

      it('应该正确处理中文字符', () => {
        const text = '你好世界'
        expect(truncateString(text, 4)).toBe('你...')
      })
    })

    describe('highlightText', () => {
      it('应该高亮匹配的文本', () => {
        const text = 'Hello World'
        const query = 'World'
        const result = highlightText(text, query)
        
        expect(result).toBe('Hello <mark class="search-highlight">World</mark>')
      })

      it('应该忽略大小写', () => {
        const text = 'Hello World'
        const query = 'world'
        const result = highlightText(text, query)
        
        expect(result).toBe('Hello <mark class="search-highlight">World</mark>')
      })

      it('应该支持自定义高亮类名', () => {
        const text = 'Hello World'
        const query = 'World'
        const result = highlightText(text, query, 'custom-highlight')
        
        expect(result).toBe('Hello <mark class="custom-highlight">World</mark>')
      })

      it('应该处理空查询', () => {
        const text = 'Hello World'
        const result = highlightText(text, '')
        
        expect(result).toBe(text)
      })
    })

    describe('extractTextSummary', () => {
      it('应该提取包含关键词的摘要', () => {
        const text = '这是一个很长的文本，包含了很多内容。其中有一个重要的关键词在这里。后面还有更多的内容。'
        const query = '关键词'
        const result = extractTextSummary(text, query, 50)
        
        expect(result).toContain('关键词')
        expect(result.length).toBeLessThanOrEqual(50)
      })

      it('应该在没有匹配时返回开头部分', () => {
        const text = '这是一个很长的文本，没有匹配的内容。'
        const query = '不存在'
        const result = extractTextSummary(text, query, 20)
        
        expect(result).toBe('这是一个很长的文本，没有匹配的内容。')
      })
    })
  })

  describe('验证函数', () => {
    describe('isValidEmail', () => {
      it('应该验证有效的邮箱地址', () => {
        expect(isValidEmail('<EMAIL>')).toBe(true)
        expect(isValidEmail('<EMAIL>')).toBe(true)
      })

      it('应该拒绝无效的邮箱地址', () => {
        expect(isValidEmail('invalid-email')).toBe(false)
        expect(isValidEmail('test@')).toBe(false)
        expect(isValidEmail('@example.com')).toBe(false)
      })
    })

    describe('isValidUrl', () => {
      it('应该验证有效的URL', () => {
        expect(isValidUrl('https://example.com')).toBe(true)
        expect(isValidUrl('http://localhost:3000')).toBe(true)
        expect(isValidUrl('ftp://files.example.com')).toBe(true)
      })

      it('应该拒绝无效的URL', () => {
        expect(isValidUrl('invalid-url')).toBe(false)
        expect(isValidUrl('not-a-url')).toBe(false)
      })
    })
  })

  describe('设备检测', () => {
    describe('isMobileDevice', () => {
      it('应该检测移动设备', () => {
        // Mock mobile user agent
        Object.defineProperty(navigator, 'userAgent', {
          value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
          writable: true
        })
        
        expect(isMobileDevice()).toBe(true)
      })

      it('应该检测桌面设备', () => {
        // Mock desktop user agent
        Object.defineProperty(navigator, 'userAgent', {
          value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          writable: true
        })
        
        expect(isMobileDevice()).toBe(false)
      })
    })
  })

  describe('本地存储工具', () => {
    describe('storage.get', () => {
      it('应该获取存储的值', () => {
        mockLocalStorage.getItem.mockReturnValue(JSON.stringify({ test: 'value' }))
        
        const result = storage.get('test-key')
        expect(result).toEqual({ test: 'value' })
        expect(mockLocalStorage.getItem).toHaveBeenCalledWith('test-key')
      })

      it('应该在没有值时返回默认值', () => {
        mockLocalStorage.getItem.mockReturnValue(null)
        
        const result = storage.get('test-key', 'default')
        expect(result).toBe('default')
      })

      it('应该处理JSON解析错误', () => {
        mockLocalStorage.getItem.mockReturnValue('invalid-json')
        
        const result = storage.get('test-key', 'default')
        expect(result).toBe('default')
      })
    })

    describe('storage.set', () => {
      it('应该保存值到本地存储', () => {
        storage.set('test-key', { test: 'value' })
        
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
          'test-key',
          JSON.stringify({ test: 'value' })
        )
      })

      it('应该处理存储错误', () => {
        mockLocalStorage.setItem.mockImplementation(() => {
          throw new Error('Storage error')
        })
        
        // 应该不抛出错误
        expect(() => storage.set('test-key', 'value')).not.toThrow()
      })
    })

    describe('storage.remove', () => {
      it('应该移除存储的值', () => {
        storage.remove('test-key')
        
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('test-key')
      })
    })

    describe('storage.clear', () => {
      it('应该清空所有存储', () => {
        storage.clear()
        
        expect(mockLocalStorage.clear).toHaveBeenCalled()
      })
    })
  })

  describe('剪贴板操作', () => {
    describe('copyToClipboard', () => {
      it('应该使用现代API复制文本', async () => {
        mockClipboard.writeText.mockResolvedValue(undefined)
        Object.defineProperty(window, 'isSecureContext', { value: true })
        
        const result = await copyToClipboard('test text')
        
        expect(result).toBe(true)
        expect(mockClipboard.writeText).toHaveBeenCalledWith('test text')
      })

      it('应该处理复制错误', async () => {
        mockClipboard.writeText.mockRejectedValue(new Error('Copy failed'))
        
        const result = await copyToClipboard('test text')
        
        expect(result).toBe(false)
      })
    })
  })

  describe('文件操作', () => {
    describe('getFileExtension', () => {
      it('应该正确提取文件扩展名', () => {
        expect(getFileExtension('file.txt')).toBe('txt')
        expect(getFileExtension('document.pdf')).toBe('pdf')
        expect(getFileExtension('archive.tar.gz')).toBe('gz')
        expect(getFileExtension('no-extension')).toBe('')
      })
    })
  })
})
