/**
 * 主题管理Hook
 * 提供全局主题状态管理和主题切换功能
 */

import React, { useState, useEffect, useCallback, createContext, useContext } from 'react'
import { theme as antdTheme } from 'antd'
import { ThemeMode, getStoredTheme, saveThemeToStorage, applyThemeToDocument, getSystemThemePreference } from '@components/common/ThemeToggle'

// Ant Design主题算法
const { defaultAlgorithm, darkAlgorithm, compactAlgorithm } = antdTheme

// 主题配置接口
export interface ThemeConfig {
  /** 当前主题模式 */
  mode: ThemeMode
  /** 是否为紧凑模式 */
  compact: boolean
  /** 主色调 */
  primaryColor: string
  /** 是否启用动画 */
  motion: boolean
}

// 主题上下文接口
export interface ThemeContextValue {
  /** 当前主题配置 */
  config: ThemeConfig
  /** 是否为暗色主题 */
  isDark: boolean
  /** Ant Design主题配置 */
  antdThemeConfig: any
  /** 切换主题模式 */
  setThemeMode: (mode: ThemeMode) => void
  /** 切换紧凑模式 */
  toggleCompact: () => void
  /** 设置主色调 */
  setPrimaryColor: (color: string) => void
  /** 切换动画效果 */
  toggleMotion: () => void
  /** 重置主题配置 */
  resetTheme: () => void
}

// 默认主题配置
const DEFAULT_THEME_CONFIG: ThemeConfig = {
  mode: ThemeMode.LIGHT,
  compact: false,
  primaryColor: '#1890ff',
  motion: true
}

// 主题上下文
const ThemeContext = createContext<ThemeContextValue | null>(null)

/**
 * 生成Ant Design主题配置
 * @param config 主题配置
 * @returns Ant Design主题配置对象
 */
const generateAntdThemeConfig = (config: ThemeConfig) => {
  const { mode, compact, primaryColor, motion } = config
  
  // 确定是否使用暗色主题
  const isDark = mode === ThemeMode.DARK || 
    (mode === ThemeMode.AUTO && getSystemThemePreference())
  
  // 构建算法数组
  const algorithms = []
  if (isDark) {
    algorithms.push(darkAlgorithm)
  } else {
    algorithms.push(defaultAlgorithm)
  }
  
  if (compact) {
    algorithms.push(compactAlgorithm)
  }
  
  return {
    algorithm: algorithms,
    token: {
      colorPrimary: primaryColor,
      motion: motion,
      borderRadius: 6,
      fontSize: 14,
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
    },
    components: {
      Layout: {
        headerBg: isDark ? '#141414' : '#ffffff',
        siderBg: isDark ? '#1f1f1f' : '#fafafa',
        bodyBg: isDark ? '#000000' : '#ffffff',
      },
      Menu: {
        itemBg: 'transparent',
        itemSelectedBg: isDark ? '#1677ff' : '#e6f7ff',
        itemHoverBg: isDark ? '#262626' : '#f5f5f5',
        darkItemSelectedBg: '#1677ff',
        darkItemHoverBg: '#262626',
      },
      Button: {
        primaryShadow: isDark ? 'none' : '0 2px 0 rgba(5, 145, 255, 0.1)',
      },
      Card: {
        headerBg: isDark ? '#1f1f1f' : '#fafafa',
      },
      Input: {
        colorBgContainer: isDark ? '#1f1f1f' : '#ffffff',
      },
      Select: {
        colorBgContainer: isDark ? '#1f1f1f' : '#ffffff',
      },
      Table: {
        headerBg: isDark ? '#1f1f1f' : '#fafafa',
        rowHoverBg: isDark ? '#262626' : '#f5f5f5',
      },
    },
    cssVar: true, // 启用CSS变量模式
  }
}

/**
 * 主题管理Hook
 * @param initialConfig 初始主题配置
 * @returns 主题上下文值
 */
export const useThemeManager = (initialConfig?: Partial<ThemeConfig>): ThemeContextValue => {
  // 初始化主题配置
  const [config, setConfig] = useState<ThemeConfig>(() => {
    const storedMode = getStoredTheme()
    return {
      ...DEFAULT_THEME_CONFIG,
      mode: storedMode,
      ...initialConfig
    }
  })
  
  // 计算是否为暗色主题
  const isDark = config.mode === ThemeMode.DARK || 
    (config.mode === ThemeMode.AUTO && getSystemThemePreference())
  
  // 生成Ant Design主题配置
  const antdThemeConfig = generateAntdThemeConfig(config)
  
  // 监听系统主题变化
  useEffect(() => {
    if (typeof window === 'undefined') return
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleSystemThemeChange = () => {
      if (config.mode === ThemeMode.AUTO) {
        applyThemeToDocument(ThemeMode.AUTO)
      }
    }
    
    mediaQuery.addEventListener('change', handleSystemThemeChange)
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange)
  }, [config.mode])
  
  // 应用主题到文档
  useEffect(() => {
    applyThemeToDocument(config.mode)
  }, [config.mode])
  
  // 切换主题模式
  const setThemeMode = useCallback((mode: ThemeMode) => {
    setConfig(prev => ({ ...prev, mode }))
    saveThemeToStorage(mode)
  }, [])
  
  // 切换紧凑模式
  const toggleCompact = useCallback(() => {
    setConfig(prev => ({ ...prev, compact: !prev.compact }))
  }, [])
  
  // 设置主色调
  const setPrimaryColor = useCallback((color: string) => {
    setConfig(prev => ({ ...prev, primaryColor: color }))
  }, [])
  
  // 切换动画效果
  const toggleMotion = useCallback(() => {
    setConfig(prev => ({ ...prev, motion: !prev.motion }))
  }, [])
  
  // 重置主题配置
  const resetTheme = useCallback(() => {
    setConfig(DEFAULT_THEME_CONFIG)
    saveThemeToStorage(DEFAULT_THEME_CONFIG.mode)
  }, [])
  
  return {
    config,
    isDark,
    antdThemeConfig,
    setThemeMode,
    toggleCompact,
    setPrimaryColor,
    toggleMotion,
    resetTheme
  }
}

/**
 * 主题上下文Provider组件属性
 */
export interface ThemeProviderProps {
  children: React.ReactNode
  initialConfig?: Partial<ThemeConfig>
}

/**
 * 主题上下文Provider组件
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  initialConfig
}) => {
  const themeValue = useThemeManager(initialConfig)

  return React.createElement(
    ThemeContext.Provider,
    { value: themeValue },
    children
  )
}

/**
 * 使用主题上下文的Hook
 * @returns 主题上下文值
 */
export const useTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

export default useTheme
