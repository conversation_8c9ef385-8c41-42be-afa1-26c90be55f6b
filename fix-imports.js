#!/usr/bin/env node

/**
 * 修复TypeScript导入路径的脚本
 */

const fs = require('fs');
const path = require('path');

// 需要修复的导入路径映射
const importFixes = [
  {
    from: "from '@types/index'",
    to: "from '../types/index'"
  },
  {
    from: "from '@types'",
    to: "from '../types/index'"
  }
];

// 需要处理的文件扩展名
const fileExtensions = ['.ts', '.tsx'];

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      walkDir(filePath, callback);
    } else if (stat.isFile() && fileExtensions.some(ext => file.endsWith(ext))) {
      callback(filePath);
    }
  });
}

// 修复单个文件
function fixFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  importFixes.forEach(fix => {
    if (content.includes(fix.from)) {
      // 计算相对路径
      const relativePath = path.relative(path.dirname(filePath), path.join(__dirname, 'src'));
      const correctImport = fix.to.replace('../', relativePath ? relativePath + '/' : './');
      
      content = content.replace(new RegExp(fix.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), 
                               fix.to);
      modified = true;
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed: ${filePath}`);
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'src');
  
  console.log('开始修复导入路径...');
  
  walkDir(srcDir, fixFile);
  
  console.log('导入路径修复完成！');
}

if (require.main === module) {
  main();
}
